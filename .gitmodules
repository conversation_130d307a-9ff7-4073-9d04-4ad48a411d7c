[submodule "fast-apply/repos/antipattern"]
	path = training/repos/antipattern
	url = **************:reissbaker/antipattern.git
[submodule "fast-apply/repos/structural"]
	path = training/repos/structural
	url = **************:reissbaker/structural.git
[submodule "fast-apply/repos/ink"]
	path = training/repos/ink
	url = **************:vadimdemedes/ink.git
[submodule "fast-apply/repos/figlet"]
	path = training/repos/figlet
	url = **************:patorjk/figlet.js.git
[submodule "fast-apply/repos/asciimatics"]
	path = training/repos/asciimatics
	url = **************:peterbrittain/asciimatics.git
[submodule "fast-apply/repos/ruby-jwt"]
	path = training/repos/ruby-jwt
	url = **************:jwt/ruby-jwt.git
[submodule "fast-apply/repos/terrapin"]
	path = training/repos/terrapin
	url = **************:thoughtbot/terrapin.git
[submodule "fast-apply/repos/clamp"]
	path = training/repos/clamp
	url = **************:mdub/clamp.git
[submodule "fast-apply/repos/colorize"]
	path = training/repos/colorize
	url = **************:fazibear/colorize.git
[submodule "fast-apply/repos/toolz"]
	path = training/repos/toolz
	url = **************:pytoolz/toolz.git
[submodule "fast-apply/repos/funcy"]
	path = training/repos/funcy
	url = **************:Suor/funcy.git
[submodule "fast-apply/repos/chip8-rust"]
	path = training/repos/chip8-rust
	url = **************:starrhorne/chip8-rust.git
[submodule "fast-apply/repos/xplr"]
	path = training/repos/xplr
	url = **************:sayanarijit/xplr.git
[submodule "fast-apply/repos/lsd"]
	path = training/repos/lsd
	url = **************:lsd-rs/lsd.git
[submodule "fast-apply/repos/rust-raytracer"]
	path = training/repos/rust-raytracer
	url = **************:dps/rust-raytracer.git
[submodule "fast-apply/repos/unfat"]
	path = training/repos/unfat
	url = **************:reissbaker/unfat.git
[submodule "training/fix-json/json-repos/pokedex"]
	path = training/fix-json/json-repos/pokedex
	url = **************:Biuni/PokemonGO-Pokedex.git
[submodule "training/fix-json/json-repos/wikipedia-movie-data"]
	path = training/fix-json/json-repos/wikipedia-movie-data
	url = **************:prust/wikipedia-movie-data.git
