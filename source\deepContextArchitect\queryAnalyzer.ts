import * as fs from "fs/promises";
import * as path from "path";

export interface QueryAnalysis {
  intent: string;
  targetComponents: string[];
  operationType: 'create' | 'modify' | 'delete' | 'refactor' | 'debug' | 'analyze';
  complexity: 'simple' | 'moderate' | 'complex';
  keywords: string[];
  filePatterns: string[];
  functionPatterns: string[];
  classPatterns: string[];
}

export class QueryAnalyzer {
  private readonly intentPatterns = {
    create: [
      /create|add|implement|build|make|generate|new/i,
      /write.*function|write.*class|write.*component/i,
    ],
    modify: [
      /modify|change|update|edit|alter|fix|improve/i,
      /refactor(?!.*entire)|optimize|enhance/i,
    ],
    delete: [
      /delete|remove|eliminate|drop/i,
    ],
    refactor: [
      /refactor.*entire|restructure|reorganize|redesign/i,
      /move.*to|extract.*into|split.*into/i,
    ],
    debug: [
      /debug|fix.*bug|troubleshoot|diagnose/i,
      /error|issue|problem|not.*work/i,
    ],
    analyze: [
      /analyze|understand|explain|show.*how|what.*does/i,
      /find.*usage|where.*used|dependencies/i,
    ],
  };

  private readonly complexityIndicators = {
    simple: [
      /single.*function|one.*method|simple.*change/i,
      /add.*log|remove.*line|fix.*typo/i,
    ],
    complex: [
      /entire.*system|multiple.*files|architecture/i,
      /database.*schema|api.*design|framework/i,
      /refactor.*entire|redesign.*from/i,
    ],
  };

  async analyze(query: string, workingDirectory: string): Promise<QueryAnalysis> {
    const intent = this.extractIntent(query);
    const operationType = this.determineOperationType(query);
    const complexity = this.assessComplexity(query);
    const keywords = this.extractKeywords(query);
    
    // Extract potential target components from the query
    const targetComponents = await this.extractTargetComponents(query, workingDirectory);
    const filePatterns = this.extractFilePatterns(query);
    const functionPatterns = this.extractFunctionPatterns(query);
    const classPatterns = this.extractClassPatterns(query);

    return {
      intent,
      targetComponents,
      operationType,
      complexity,
      keywords,
      filePatterns,
      functionPatterns,
      classPatterns,
    };
  }

  private extractIntent(query: string): string {
    // Use the query as-is but clean it up
    return query.trim().replace(/\s+/g, ' ');
  }

  private determineOperationType(query: string): QueryAnalysis['operationType'] {
    for (const [type, patterns] of Object.entries(this.intentPatterns)) {
      if (patterns.some(pattern => pattern.test(query))) {
        return type as QueryAnalysis['operationType'];
      }
    }
    return 'analyze'; // Default fallback
  }

  private assessComplexity(query: string): QueryAnalysis['complexity'] {
    if (this.complexityIndicators.simple.some(pattern => pattern.test(query))) {
      return 'simple';
    }
    if (this.complexityIndicators.complex.some(pattern => pattern.test(query))) {
      return 'complex';
    }
    return 'moderate';
  }

  private extractKeywords(query: string): string[] {
    // Extract meaningful keywords, excluding common words
    const commonWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
      'between', 'among', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we',
      'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'need', 'want', 'like',
    ]);

    return query
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.has(word))
      .filter((word, index, arr) => arr.indexOf(word) === index); // Remove duplicates
  }

  private async extractTargetComponents(query: string, workingDirectory: string): Promise<string[]> {
    const components: string[] = [];
    
    // Extract file names mentioned in the query
    const fileMatches = query.match(/[\w-]+\.(ts|js|tsx|jsx|json|md|txt|py|java|cpp|c|h)/gi);
    if (fileMatches) {
      components.push(...fileMatches);
    }

    // Extract function/method names (camelCase or snake_case patterns)
    const functionMatches = query.match(/\b[a-z][a-zA-Z0-9_]*(?:\(\))?/g);
    if (functionMatches) {
      components.push(...functionMatches.filter(match => match.length > 3));
    }

    // Extract class names (PascalCase patterns)
    const classMatches = query.match(/\b[A-Z][a-zA-Z0-9]*\b/g);
    if (classMatches) {
      components.push(...classMatches);
    }

    // Try to find actual files in the working directory that match mentioned names
    try {
      const actualFiles = await this.findMatchingFiles(workingDirectory, components);
      components.push(...actualFiles);
    } catch (error) {
      // Ignore errors in file system access
    }

    // Remove duplicates and return
    return [...new Set(components)];
  }

  private async findMatchingFiles(workingDirectory: string, components: string[]): Promise<string[]> {
    const matchingFiles: string[] = [];
    
    try {
      const files = await this.getAllFiles(workingDirectory);
      
      for (const component of components) {
        const matches = files.filter(file => {
          const basename = path.basename(file, path.extname(file));
          return basename.toLowerCase().includes(component.toLowerCase()) ||
                 component.toLowerCase().includes(basename.toLowerCase());
        });
        matchingFiles.push(...matches);
      }
    } catch (error) {
      // Ignore file system errors
    }

    return matchingFiles;
  }

  private async getAllFiles(dir: string, maxDepth: number = 3, currentDepth: number = 0): Promise<string[]> {
    if (currentDepth >= maxDepth) return [];
    
    const files: string[] = [];
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
          const subFiles = await this.getAllFiles(fullPath, maxDepth, currentDepth + 1);
          files.push(...subFiles);
        } else if (entry.isFile() && this.isRelevantFile(entry.name)) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Ignore directories we can't read
    }
    
    return files;
  }

  private shouldSkipDirectory(name: string): boolean {
    const skipDirs = [
      'node_modules', '.git', 'dist', 'build', '.next', '.nuxt', 'coverage',
      '__pycache__', '.pytest_cache', 'venv', 'env', '.venv', '.env',
    ];
    return skipDirs.includes(name) || name.startsWith('.');
  }

  private isRelevantFile(name: string): boolean {
    const relevantExtensions = [
      '.ts', '.js', '.tsx', '.jsx', '.json', '.md', '.txt',
      '.py', '.java', '.cpp', '.c', '.h', '.hpp', '.cs',
      '.go', '.rs', '.php', '.rb', '.swift', '.kt',
    ];
    return relevantExtensions.some(ext => name.endsWith(ext));
  }

  private extractFilePatterns(query: string): string[] {
    const patterns: string[] = [];
    
    // Look for file extension patterns
    const extMatches = query.match(/\*\.\w+/g);
    if (extMatches) {
      patterns.push(...extMatches);
    }

    // Look for directory patterns
    const dirMatches = query.match(/[\w-]+\/\**/g);
    if (dirMatches) {
      patterns.push(...dirMatches);
    }

    return patterns;
  }

  private extractFunctionPatterns(query: string): string[] {
    const patterns: string[] = [];
    
    // Look for function-like patterns
    const functionMatches = query.match(/\b\w+\s*\(/g);
    if (functionMatches) {
      patterns.push(...functionMatches.map(match => match.replace(/\s*\($/, '')));
    }

    return patterns;
  }

  private extractClassPatterns(query: string): string[] {
    const patterns: string[] = [];
    
    // Look for class-like patterns (PascalCase)
    const classMatches = query.match(/\b[A-Z][a-zA-Z0-9]*(?:Class|Component|Service|Manager|Handler|Controller)?\b/g);
    if (classMatches) {
      patterns.push(...classMatches);
    }

    return patterns;
  }
}