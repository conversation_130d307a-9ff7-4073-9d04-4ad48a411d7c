This project contains E5Context's source code: the code to build the E5Context system.

This project can take up to 20s to build via `npx tsc`. Make sure your timeouts
are long enough.

E5Context features a revolutionary Deep Context Architect that provides comprehensive
code analysis before making any changes. Always use the deepContextArchitect tool
to analyze code context before implementing changes.

Prefer `type Blah = { ... }` to `interface Blah { ... }` unless you *need* an
interface: i.e. if it's designed for classes to implement. If it's not, just
use a `type`.
