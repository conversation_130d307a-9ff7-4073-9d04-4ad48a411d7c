import { QueryAnalyzer } from "./queryAnalyzer.ts";
import { RelationshipMapper } from "./relationshipMapper.ts";
import { ContextAggregator } from "./contextAggregator.ts";
import { PromptGenerator } from "./promptGenerator.ts";

export interface DeepContextResult {
  analysis: {
    queryIntent: string;
    targetComponents: string[];
    affectedFiles: string[];
    dependencies: Array<{
      file: string;
      type: 'import' | 'export' | 'usage' | 'definition' | 'inheritance' | 'composition';
      component: string;
    }>;
    riskAssessment: {
      level: 'low' | 'medium' | 'high';
      factors: string[];
    };
  };
  context: {
    relevantCode: Array<{
      file: string;
      content: string;
      relevanceScore: number;
    }>;
    relatedTests: string[];
    documentation: string[];
    recentChanges: Array<{
      file: string;
      timestamp: string;
      summary: string;
    }>;
  };
  recommendations: {
    implementationPlan: string;
    suggestedApproach: string;
    alternativeApproaches: string[];
    testingStrategy: string;
    rollbackPlan: string;
  };
}

export class DeepContextArchitect {
  private queryAnalyzer: QueryAnalyzer;
  private relationshipMapper: RelationshipMapper;
  private contextAggregator: ContextAggregator;
  private promptGenerator: PromptGenerator;

  constructor() {
    this.queryAnalyzer = new QueryAnalyzer();
    this.relationshipMapper = new RelationshipMapper();
    this.contextAggregator = new ContextAggregator();
    this.promptGenerator = new PromptGenerator();
  }

  async analyze(
    query: string,
    workingDirectory: string,
    targetFiles?: string[],
    analysisDepth: 'shallow' | 'deep' | 'comprehensive' = 'deep'
  ): Promise<DeepContextResult> {
    // Step 1: Analyze the query to understand intent
    const queryAnalysis = await this.queryAnalyzer.analyze(query, workingDirectory);

    // Step 2: Map code relationships
    const relationships = await this.relationshipMapper.mapRelationships(
      workingDirectory,
      queryAnalysis.targetComponents,
      targetFiles,
      analysisDepth
    );

    // Step 3: Aggregate relevant context
    const context = await this.contextAggregator.gatherContext(
      workingDirectory,
      queryAnalysis,
      relationships,
      analysisDepth
    );

    // Step 4: Generate comprehensive recommendations
    const recommendations = await this.promptGenerator.generateRecommendations(
      queryAnalysis,
      relationships,
      context
    );

    return {
      analysis: {
        queryIntent: queryAnalysis.intent,
        targetComponents: queryAnalysis.targetComponents,
        affectedFiles: relationships.affectedFiles,
        dependencies: relationships.dependencies,
        riskAssessment: relationships.riskAssessment,
      },
      context: {
        relevantCode: context.relevantCode,
        relatedTests: context.relatedTests,
        documentation: context.documentation,
        recentChanges: context.recentChanges,
      },
      recommendations,
    };
  }
}