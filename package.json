{"name": "e5context", "version": "0.0.24", "license": "MIT", "repository": "github:synthetic-lab/e5context", "bin": "dist/source/cli.js", "type": "module", "preferGlobal": true, "engines": {"node": ">=16"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "exec": "tsc && node ./dist/source/cli.js", "test": "vitest", "test:run": "vitest run"}, "files": ["dist", "source", "package.json", "package-lock.json", "tsconfig.json"], "dependencies": {"@ai-sdk/openai": "^2.0.4", "@anthropic-ai/sdk": "^0.58.0", "@commander-js/extra-typings": "^13.1.0", "@modelcontextprotocol/sdk": "^1.0.1", "ai": "^5.0.5", "antipattern": "^0.0.7", "chalk": "^5.4.1", "commander": "^13.1.0", "diff": "^8.0.2", "figlet": "^1.8.0", "figures": "^6.1.0", "html-to-text": "^9.0.5", "ink": "^5.2.0", "ink-select-input": "^6.0.0", "ink-spinner": "^5.0.0", "ink-text-input": "^6.0.0", "json-schema": "^0.4.0", "json5": "^2.2.3", "openai": "^5.9.0", "react": "^18.2.0", "structural": "^0.0.29", "zustand": "^5.0.3"}, "devDependencies": {"@types/figlet": "^1.7.0", "@types/html-to-text": "^9.0.4", "@types/json-schema": "^7.0.15", "@types/node": "^22.17.1", "@types/react": "^18.0.32", "ink-testing-library": "^3.0.0", "parse-git-diff": "^0.0.19", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4"}}