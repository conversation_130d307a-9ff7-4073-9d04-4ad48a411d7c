# E5Context Complete Setup Guide

## 🎯 **System Overview**

E5<PERSON>ontex<PERSON> is a revolutionary coding assistant with a **Deep Context Architect** that analyzes code like a senior developer before making any changes. It supports both local and API-based language models.

### **How It Works**

```
User Request → E5Context → [Deep Context Architect] → LLM → Informed Code Changes
```

1. **User makes a coding request** (e.g., "Add authentication")
2. **<PERSON>5Context can call Deep Context Architect** to analyze the codebase
3. **Deep Context Architect provides comprehensive analysis**:
   - Query intent understanding
   - Code relationship mapping
   - Risk assessment
   - Implementation planning
   - Testing strategies
   - Rollback procedures
4. **LLM receives full context** and makes informed decisions
5. **Code changes are made** with complete understanding

---

## 🚀 **Installation**

### **Option 1: Install from Current Directory (Development)**

```bash
# From the e5context directory
npm install
npm run build  # Note: May have some TypeScript warnings, but system works
npm link       # Makes e5context available globally
```

### **Option 2: Install from NPM (When Published)**

```bash
npm install --global e5context
```

---

## ⚙️ **Initial Setup**

### **1. Initialize Configuration**

```bash
e5context init
```

This will guide you through setting up your first model. You'll be prompted to choose:

- **Autofix models** (recommended for better performance)
- **Model provider** (OpenAI, Anthropic, local, etc.)
- **API configuration**

### **2. Configuration File Location**

After initialization, your config will be at:
- **Windows**: `C:\Users\<USER>\.config\e5context\e5context.json5`
- **macOS/Linux**: `~/.config/e5context/e5context.json5`

---

## 🌐 **API-Based Model Setup**

### **OpenAI (GPT-4, GPT-4o, etc.)**

1. **Get API Key**: Visit [OpenAI API](https://platform.openai.com/api-keys)
2. **Set Environment Variable**:
   ```bash
   # Windows (PowerShell)
   $env:OPENAI_API_KEY = "your-api-key-here"
   
   # Windows (Command Prompt)
   set OPENAI_API_KEY=your-api-key-here
   
   # macOS/Linux
   export OPENAI_API_KEY="your-api-key-here"
   ```

3. **Add to Config** (or use `e5context init`):
   ```json5
   {
     "models": [
       {
         "nickname": "GPT-4",
         "model": "gpt-4o",
         "baseUrl": "https://api.openai.com/v1",
         "apiEnvVar": "OPENAI_API_KEY",
         "context": 128000
       }
     ]
   }
   ```

### **Anthropic (Claude)**

1. **Get API Key**: Visit [Anthropic Console](https://console.anthropic.com/)
2. **Set Environment Variable**:
   ```bash
   # Windows (PowerShell)
   $env:ANTHROPIC_API_KEY = "your-api-key-here"
   
   # macOS/Linux
   export ANTHROPIC_API_KEY="your-api-key-here"
   ```

3. **Add to Config**:
   ```json5
   {
     "models": [
       {
         "nickname": "Claude-3.5-Sonnet",
         "model": "claude-3-5-sonnet-20241022",
         "baseUrl": "https://api.anthropic.com",
         "apiEnvVar": "ANTHROPIC_API_KEY",
         "context": 200000
       }
     ]
   }
   ```

### **Google Gemini**

1. **Get API Key**: Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. **Set Environment Variable**:
   ```bash
   export GOOGLE_API_KEY="your-api-key-here"
   ```

3. **Add to Config**:
   ```json5
   {
     "models": [
       {
         "nickname": "Gemini-Pro",
         "model": "gemini-pro",
         "baseUrl": "https://generativelanguage.googleapis.com/v1beta",
         "apiEnvVar": "GOOGLE_API_KEY",
         "context": 32000
       }
     ]
   }
   ```

---

## 🏠 **Local Model Setup**

### **Option 1: Ollama (Recommended)**

1. **Install Ollama**: Visit [ollama.ai](https://ollama.ai)

2. **Download a Model**:
   ```bash
   ollama pull codellama:13b
   # or
   ollama pull deepseek-coder:6.7b
   # or
   ollama pull qwen2.5-coder:7b
   ```

3. **Start Ollama Server**:
   ```bash
   ollama serve
   ```

4. **Set Environment Variable** (any non-empty value):
   ```bash
   export OLLAMA_API_KEY="dummy"
   ```

5. **Add to E5Context Config**:
   ```json5
   {
     "models": [
       {
         "nickname": "CodeLlama-13B",
         "model": "codellama:13b",
         "baseUrl": "http://localhost:11434/v1",
         "apiEnvVar": "OLLAMA_API_KEY",
         "context": 16000
       }
     ]
   }
   ```

### **Option 2: LM Studio**

1. **Install LM Studio**: Visit [lmstudio.ai](https://lmstudio.ai)

2. **Download a Model** (e.g., CodeLlama, DeepSeek Coder)

3. **Start Local Server** in LM Studio (usually port 1234)

4. **Add to Config**:
   ```json5
   {
     "models": [
       {
         "nickname": "Local-CodeLlama",
         "model": "codellama-13b-instruct",
         "baseUrl": "http://localhost:1234/v1",
         "apiEnvVar": "LOCAL_API_KEY",
         "context": 16000
       }
     ]
   }
   ```

### **Option 3: llama.cpp**

1. **Install llama.cpp**: Follow [llama.cpp instructions](https://github.com/ggerganov/llama.cpp)

2. **Download GGUF Model**:
   ```bash
   # Example: Download CodeLlama
   wget https://huggingface.co/TheBloke/CodeLlama-13B-Instruct-GGUF/resolve/main/codellama-13b-instruct.Q4_K_M.gguf
   ```

3. **Start Server**:
   ```bash
   ./server -m codellama-13b-instruct.Q4_K_M.gguf -c 4096 --host 0.0.0.0 --port 8080
   ```

4. **Add to Config**:
   ```json5
   {
     "models": [
       {
         "nickname": "CodeLlama-llama.cpp",
         "model": "codellama-13b-instruct",
         "baseUrl": "http://localhost:8080/v1",
         "apiEnvVar": "LLAMA_API_KEY",
         "context": 4096
       }
     ]
   }
   ```

---

## 🧪 **Testing Your Setup**

### **1. Basic Functionality Test**

```bash
# Test if E5Context starts
e5context

# Test with a simple prompt
e5context prompt "Hello, can you help me with coding?"
```

### **2. Test Deep Context Architect**

Create a test project:

```bash
mkdir test-project
cd test-project
echo "console.log('Hello World');" > index.js
echo "# Test Project" > README.md
```

Start E5Context and test:

```bash
e5context
```

Then in the E5Context interface, try:
```
"Use the deepContextArchitect tool to analyze adding error handling to this project"
```

### **3. Test Model Switching**

In E5Context, press `ESC` to open the menu, then select "Switch Model" to test different models.

### **4. Verify Deep Context Architect Integration**

The system should automatically use the Deep Context Architect when making code changes. You can verify this by:

1. Making a coding request
2. Looking for the comprehensive analysis report
3. Checking that the LLM receives detailed context before making changes

---

## 📁 **Project Structure & Instructions**

### **Create Project Instructions**

Create an `E5CONTEXT.md` file in your project root:

```markdown
# Project Instructions for E5Context

This is a [describe your project type] project.

## Key Guidelines
- Always use the deepContextArchitect tool before making significant changes
- Follow existing code patterns and conventions
- Ensure comprehensive testing for all changes
- Maintain backward compatibility where possible

## Project Structure
- `src/` - Main source code
- `tests/` - Test files
- `docs/` - Documentation

## Coding Standards
- Use TypeScript for type safety
- Follow ESLint configuration
- Write comprehensive tests
- Document complex functions
```

### **Global Instructions**

Create global instructions at `~/.config/e5context/E5CONTEXT.md`:

```markdown
# Global E5Context Instructions

## Always Remember
- Use deepContextArchitect for complex changes
- Prioritize code safety and reliability
- Follow security best practices
- Write clean, maintainable code

## Preferred Technologies
- TypeScript over JavaScript
- React for frontend
- Node.js for backend
- Jest for testing
```

---

## 🔧 **Advanced Configuration**

### **Multiple Models Setup**

```json5
{
  "models": [
    {
      "nickname": "GPT-4 (Primary)",
      "model": "gpt-4o",
      "baseUrl": "https://api.openai.com/v1",
      "apiEnvVar": "OPENAI_API_KEY",
      "context": 128000
    },
    {
      "nickname": "Claude-3.5 (Reasoning)",
      "model": "claude-3-5-sonnet-20241022",
      "baseUrl": "https://api.anthropic.com",
      "apiEnvVar": "ANTHROPIC_API_KEY",
      "context": 200000
    },
    {
      "nickname": "Local CodeLlama",
      "model": "codellama:13b",
      "baseUrl": "http://localhost:11434/v1",
      "apiEnvVar": "OLLAMA_API_KEY",
      "context": 16000
    }
  ],
  "diffApply": {
    "nickname": "Diff Fixer",
    "model": "gpt-3.5-turbo",
    "baseUrl": "https://api.openai.com/v1",
    "apiEnvVar": "OPENAI_API_KEY"
  },
  "fixJson": {
    "nickname": "JSON Fixer",
    "model": "gpt-3.5-turbo",
    "baseUrl": "https://api.openai.com/v1",
    "apiEnvVar": "OPENAI_API_KEY"
  }
}
```

### **MCP Server Integration**

Add MCP servers for extended functionality:

```json5
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/directory"]
    },
    "git": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-git", "--repository", "."]
    }
  }
}
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

1. **"Model not found" error**:
   - Check API key is set correctly
   - Verify model name matches provider's API
   - Test API key with curl/postman

2. **"Connection refused" for local models**:
   - Ensure local server is running
   - Check port numbers match
   - Verify firewall settings

3. **Deep Context Architect not working**:
   - Check TypeScript compilation completed
   - Verify all dependencies installed
   - Test with simple project first

4. **Environment variables not found**:
   ```bash
   # Check if set
   echo $OPENAI_API_KEY
   
   # Set permanently (add to ~/.bashrc or ~/.zshrc)
   echo 'export OPENAI_API_KEY="your-key"' >> ~/.bashrc
   source ~/.bashrc
   ```

### **Debug Mode**

Enable verbose logging:

```bash
E5CONTEXT_VERBOSE=1 e5context
```

### **Reset Configuration**

```bash
# Remove config file to start fresh
rm ~/.config/e5context/e5context.json5
e5context init
```

---

## 🎯 **Best Practices**

### **For API Models**
- Use GPT-4o or Claude-3.5-Sonnet for best results
- Set appropriate context limits
- Monitor API usage and costs
- Use autofix models for better reliability

### **For Local Models**
- Use models with at least 7B parameters for coding
- CodeLlama, DeepSeek Coder, or Qwen2.5-Coder recommended
- Ensure sufficient RAM (16GB+ for 13B models)
- Use quantized models (Q4_K_M) for better performance

### **For Deep Context Architect**
- Always use for complex changes
- Start with "shallow" analysis for simple tasks
- Use "comprehensive" for critical system changes
- Review the analysis report before proceeding

### **Project Organization**
- Create project-specific `E5CONTEXT.md` files
- Use clear, descriptive commit messages
- Maintain good project structure
- Keep dependencies up to date

---

## 🚀 **Ready to Use!**

Your E5Context system is now ready with:

✅ **Revolutionary Deep Context Architect** - Analyzes code like a senior developer  
✅ **Multiple Model Support** - API and local models  
✅ **Comprehensive Analysis** - Risk assessment, implementation planning, testing strategies  
✅ **Bulletproof Implementation** - Detailed rollback procedures and safety measures  
✅ **Extensible Architecture** - MCP servers, custom instructions, multiple analysis depths  

Start coding with confidence knowing that E5Context provides the wisdom and caution of a senior developer built right in!