import * as fs from "fs/promises";
import * as path from "path";
import { QueryAnalysis } from "./queryAnalyzer.ts";

export interface CodeRelationship {
  file: string;
  type: 'import' | 'export' | 'usage' | 'definition' | 'inheritance' | 'composition';
  component: string;
  lineNumber?: number;
  context?: string;
}

export interface RiskAssessment {
  level: 'low' | 'medium' | 'high';
  factors: string[];
}

export interface RelationshipMap {
  affectedFiles: string[];
  dependencies: CodeRelationship[];
  riskAssessment: RiskAssessment;
  dependencyGraph: Map<string, string[]>;
  usageMap: Map<string, string[]>;
}

export class RelationshipMapper {
  private readonly riskFactors = {
    high: [
      'core system files',
      'shared utilities',
      'configuration files',
      'database schemas',
      'API contracts',
      'authentication/authorization',
    ],
    medium: [
      'business logic',
      'data models',
      'service layers',
      'middleware',
      'validation logic',
    ],
  };

  async mapRelationships(
    workingDirectory: string,
    targetComponents: string[],
    targetFiles?: string[],
    analysisDepth: 'shallow' | 'deep' | 'comprehensive' = 'deep'
  ): Promise<RelationshipMap> {
    const allFiles = await this.getAllRelevantFiles(workingDirectory);
    const filesToAnalyze = targetFiles || allFiles;
    
    const dependencies: CodeRelationship[] = [];
    const affectedFiles = new Set<string>();
    const dependencyGraph = new Map<string, string[]>();
    const usageMap = new Map<string, string[]>();

    // Analyze each file for relationships
    for (const file of filesToAnalyze) {
      try {
        const content = await fs.readFile(file, 'utf-8');
        const fileRelationships = await this.analyzeFileRelationships(
          file,
          content,
          targetComponents,
          analysisDepth
        );
        
        dependencies.push(...fileRelationships);
        
        if (fileRelationships.length > 0) {
          affectedFiles.add(file);
        }

        // Build dependency graph
        const fileDeps = this.extractFileDependencies(content, file, workingDirectory);
        dependencyGraph.set(file, fileDeps);

        // Build usage map
        const usages = this.extractUsages(content, targetComponents);
        if (usages.length > 0) {
          usageMap.set(file, usages);
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    // If analysis depth is comprehensive, also analyze transitive dependencies
    if (analysisDepth === 'comprehensive') {
      await this.analyzeTransitiveDependencies(
        workingDirectory,
        dependencyGraph,
        affectedFiles,
        dependencies,
        targetComponents
      );
    }

    const riskAssessment = this.assessRisk(Array.from(affectedFiles), dependencies);

    return {
      affectedFiles: Array.from(affectedFiles),
      dependencies,
      riskAssessment,
      dependencyGraph,
      usageMap,
    };
  }

  private async getAllRelevantFiles(workingDirectory: string): Promise<string[]> {
    const files: string[] = [];
    
    const traverse = async (dir: string, depth: number = 0): Promise<void> => {
      if (depth > 5) return; // Limit recursion depth
      
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          
          if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
            await traverse(fullPath, depth + 1);
          } else if (entry.isFile() && this.isCodeFile(entry.name)) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    };

    await traverse(workingDirectory);
    return files;
  }

  private shouldSkipDirectory(name: string): boolean {
    const skipDirs = [
      'node_modules', '.git', 'dist', 'build', '.next', '.nuxt', 'coverage',
      '__pycache__', '.pytest_cache', 'venv', 'env', '.venv', '.env',
      'target', 'bin', 'obj', '.vs', '.vscode',
    ];
    return skipDirs.includes(name) || name.startsWith('.');
  }

  private isCodeFile(name: string): boolean {
    const codeExtensions = [
      '.ts', '.js', '.tsx', '.jsx', '.json',
      '.py', '.java', '.cpp', '.c', '.h', '.hpp', '.cs',
      '.go', '.rs', '.php', '.rb', '.swift', '.kt',
    ];
    return codeExtensions.some(ext => name.endsWith(ext));
  }

  private async analyzeFileRelationships(
    filePath: string,
    content: string,
    targetComponents: string[],
    analysisDepth: 'shallow' | 'deep' | 'comprehensive'
  ): Promise<CodeRelationship[]> {
    const relationships: CodeRelationship[] = [];
    const lines = content.split('\n');

    // Analyze imports/exports
    relationships.push(...this.analyzeImportsExports(filePath, lines, targetComponents));

    // Analyze function/class definitions and usages
    if (analysisDepth !== 'shallow') {
      relationships.push(...this.analyzeDefinitionsAndUsages(filePath, lines, targetComponents));
    }

    // Analyze inheritance and composition
    if (analysisDepth === 'comprehensive') {
      relationships.push(...this.analyzeInheritanceAndComposition(filePath, lines, targetComponents));
    }

    return relationships;
  }

  private analyzeImportsExports(
    filePath: string,
    lines: string[],
    targetComponents: string[]
  ): CodeRelationship[] {
    const relationships: CodeRelationship[] = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      // Import statements
      const importMatch = trimmedLine.match(/^import\s+(?:{([^}]+)}|\*\s+as\s+(\w+)|(\w+))\s+from\s+['"]([^'"]+)['"]/);
      if (importMatch) {
        const [, namedImports, namespaceImport, defaultImport, modulePath] = importMatch;
        
        if (namedImports) {
          const imports = namedImports.split(',').map(imp => imp.trim());
          imports.forEach(imp => {
            if (targetComponents.some(comp => imp.includes(comp))) {
              relationships.push({
                file: filePath,
                type: 'import',
                component: imp,
                lineNumber: index + 1,
                context: line.trim(),
              });
            }
          });
        } else if (namespaceImport || defaultImport) {
          const importName = namespaceImport || defaultImport;
          if (targetComponents.some(comp => importName.includes(comp))) {
            relationships.push({
              file: filePath,
              type: 'import',
              component: importName,
              lineNumber: index + 1,
              context: line.trim(),
            });
          }
        }
      }

      // Export statements
      const exportMatch = trimmedLine.match(/^export\s+(?:default\s+)?(?:class|function|const|let|var|interface|type)\s+(\w+)/);
      if (exportMatch) {
        const [, exportName] = exportMatch;
        if (targetComponents.some(comp => exportName.includes(comp))) {
          relationships.push({
            file: filePath,
            type: 'export',
            component: exportName,
            lineNumber: index + 1,
            context: line.trim(),
          });
        }
      }
    });

    return relationships;
  }

  private analyzeDefinitionsAndUsages(
    filePath: string,
    lines: string[],
    targetComponents: string[]
  ): CodeRelationship[] {
    const relationships: CodeRelationship[] = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      // Function definitions
      const functionMatch = trimmedLine.match(/(?:function\s+|const\s+|let\s+|var\s+)(\w+)\s*(?:=\s*(?:async\s+)?(?:\([^)]*\)\s*=>|\([^)]*\)\s*{|function)|\([^)]*\)\s*{)/);
      if (functionMatch) {
        const [, functionName] = functionMatch;
        if (targetComponents.some(comp => functionName.includes(comp) || comp.includes(functionName))) {
          relationships.push({
            file: filePath,
            type: 'definition',
            component: functionName,
            lineNumber: index + 1,
            context: line.trim(),
          });
        }
      }

      // Class definitions
      const classMatch = trimmedLine.match(/class\s+(\w+)/);
      if (classMatch) {
        const [, className] = classMatch;
        if (targetComponents.some(comp => className.includes(comp) || comp.includes(className))) {
          relationships.push({
            file: filePath,
            type: 'definition',
            component: className,
            lineNumber: index + 1,
            context: line.trim(),
          });
        }
      }

      // Usage patterns
      targetComponents.forEach(component => {
        if (trimmedLine.includes(component) && !trimmedLine.startsWith('//') && !trimmedLine.startsWith('*')) {
          // Avoid duplicate definitions
          if (!relationships.some(rel => rel.lineNumber === index + 1 && rel.type === 'definition')) {
            relationships.push({
              file: filePath,
              type: 'usage',
              component,
              lineNumber: index + 1,
              context: line.trim(),
            });
          }
        }
      });
    });

    return relationships;
  }

  private analyzeInheritanceAndComposition(
    filePath: string,
    lines: string[],
    targetComponents: string[]
  ): CodeRelationship[] {
    const relationships: CodeRelationship[] = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      // Inheritance (extends)
      const extendsMatch = trimmedLine.match(/class\s+\w+\s+extends\s+(\w+)/);
      if (extendsMatch) {
        const [, parentClass] = extendsMatch;
        if (targetComponents.some(comp => parentClass.includes(comp))) {
          relationships.push({
            file: filePath,
            type: 'inheritance',
            component: parentClass,
            lineNumber: index + 1,
            context: line.trim(),
          });
        }
      }

      // Interface implementation (implements)
      const implementsMatch = trimmedLine.match(/class\s+\w+\s+implements\s+([^{]+)/);
      if (implementsMatch) {
        const interfaces = implementsMatch[1].split(',').map(iface => iface.trim());
        interfaces.forEach(iface => {
          if (targetComponents.some(comp => iface.includes(comp))) {
            relationships.push({
              file: filePath,
              type: 'inheritance',
              component: iface,
              lineNumber: index + 1,
              context: line.trim(),
            });
          }
        });
      }

      // Composition (property declarations with types)
      const compositionMatch = trimmedLine.match(/(?:private|public|protected)?\s*(\w+):\s*(\w+)/);
      if (compositionMatch) {
        const [, propertyName, propertyType] = compositionMatch;
        if (targetComponents.some(comp => propertyType.includes(comp))) {
          relationships.push({
            file: filePath,
            type: 'composition',
            component: propertyType,
            lineNumber: index + 1,
            context: line.trim(),
          });
        }
      }
    });

    return relationships;
  }

  private extractFileDependencies(content: string, filePath: string, workingDirectory: string): string[] {
    const dependencies: string[] = [];
    const lines = content.split('\n');

    lines.forEach(line => {
      const importMatch = line.match(/from\s+['"]([^'"]+)['"]/);
      if (importMatch) {
        const [, importPath] = importMatch;
        
        // Resolve relative imports to absolute paths
        if (importPath.startsWith('.')) {
          const resolvedPath = path.resolve(path.dirname(filePath), importPath);
          dependencies.push(resolvedPath);
        } else if (!importPath.startsWith('@') && !importPath.includes('node_modules')) {
          // Local module imports
          const resolvedPath = path.resolve(workingDirectory, importPath);
          dependencies.push(resolvedPath);
        }
      }
    });

    return dependencies;
  }

  private extractUsages(content: string, targetComponents: string[]): string[] {
    const usages: string[] = [];
    
    targetComponents.forEach(component => {
      const regex = new RegExp(`\\b${component}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        usages.push(...matches);
      }
    });

    return [...new Set(usages)]; // Remove duplicates
  }

  private async analyzeTransitiveDependencies(
    workingDirectory: string,
    dependencyGraph: Map<string, string[]>,
    affectedFiles: Set<string>,
    dependencies: CodeRelationship[],
    targetComponents: string[]
  ): Promise<void> {
    const visited = new Set<string>();
    const toVisit = [...affectedFiles];

    while (toVisit.length > 0) {
      const currentFile = toVisit.pop()!;
      if (visited.has(currentFile)) continue;
      
      visited.add(currentFile);
      const fileDeps = dependencyGraph.get(currentFile) || [];
      
      for (const dep of fileDeps) {
        if (!visited.has(dep) && !affectedFiles.has(dep)) {
          try {
            // Check if this dependency file contains any target components
            const content = await fs.readFile(dep, 'utf-8');
            const hasTargetComponents = targetComponents.some(comp => content.includes(comp));
            
            if (hasTargetComponents) {
              affectedFiles.add(dep);
              toVisit.push(dep);
              
              // Add transitive relationship
              dependencies.push({
                file: dep,
                type: 'usage',
                component: 'transitive dependency',
                context: `Transitive dependency from ${currentFile}`,
              });
            }
          } catch (error) {
            // Skip files that can't be read
          }
        }
      }
    }
  }

  private assessRisk(affectedFiles: string[], dependencies: CodeRelationship[]): RiskAssessment {
    const factors: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Check number of affected files
    if (affectedFiles.length > 10) {
      factors.push(`High number of affected files (${affectedFiles.length})`);
      riskLevel = 'high';
    } else if (affectedFiles.length > 5) {
      factors.push(`Moderate number of affected files (${affectedFiles.length})`);
      if (riskLevel === 'low') riskLevel = 'medium';
    }

    // Check for high-risk file patterns
    const highRiskFiles = affectedFiles.filter(file => {
      const fileName = path.basename(file).toLowerCase();
      return this.riskFactors.high.some(factor => 
        fileName.includes(factor.replace(/\s+/g, '')) ||
        file.toLowerCase().includes(factor.replace(/\s+/g, ''))
      );
    });

    if (highRiskFiles.length > 0) {
      factors.push(`High-risk files affected: ${highRiskFiles.map(f => path.basename(f)).join(', ')}`);
      riskLevel = 'high';
    }

    // Check for medium-risk patterns
    const mediumRiskFiles = affectedFiles.filter(file => {
      const fileName = path.basename(file).toLowerCase();
      return this.riskFactors.medium.some(factor => 
        fileName.includes(factor.replace(/\s+/g, '')) ||
        file.toLowerCase().includes(factor.replace(/\s+/g, ''))
      );
    });

    if (mediumRiskFiles.length > 0 && riskLevel === 'low') {
      factors.push(`Medium-risk files affected: ${mediumRiskFiles.map(f => path.basename(f)).join(', ')}`);
      riskLevel = 'medium';
    }

    // Check dependency complexity
    const complexDependencies = dependencies.filter(dep => 
      dep.type === 'inheritance' || dep.type === 'composition'
    );

    if (complexDependencies.length > 5) {
      factors.push(`Complex dependencies detected (${complexDependencies.length} inheritance/composition relationships)`);
      if (riskLevel !== 'high') riskLevel = 'medium';
    }

    // If no specific risk factors found, assess based on general complexity
    if (factors.length === 0) {
      if (dependencies.length > 20) {
        factors.push('High number of code relationships');
        riskLevel = 'medium';
      } else {
        factors.push('Low complexity change with minimal dependencies');
      }
    }

    return {
      level: riskLevel,
      factors,
    };
  }
}