import { QueryAnalysis } from "./queryAnalyzer.ts";
import { RelationshipMap } from "./relationshipMapper.ts";
import { ContextData } from "./contextAggregator.ts";
import * as path from "path";

export interface Recommendations {
  implementationPlan: string;
  suggestedApproach: string;
  alternativeApproaches: string[];
  testingStrategy: string;
  rollbackPlan: string;
}

export class PromptGenerator {
  async generateRecommendations(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): Promise<Recommendations> {
    const implementationPlan = this.generateImplementationPlan(
      queryAnalysis,
      relationships,
      context
    );

    const suggestedApproach = this.generateSuggestedApproach(
      queryAnalysis,
      relationships,
      context
    );

    const alternativeApproaches = this.generateAlternativeApproaches(
      queryAnalysis,
      relationships,
      context
    );

    const testingStrategy = this.generateTestingStrategy(
      queryAnalysis,
      relationships,
      context
    );

    const rollbackPlan = this.generateRollbackPlan(
      queryAnalysis,
      relationships,
      context
    );

    return {
      implementationPlan,
      suggestedApproach,
      alternativeApproaches,
      testingStrategy,
      rollbackPlan,
    };
  }

  private generateImplementationPlan(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string {
    const steps: string[] = [];
    
    // Step 1: Analysis and preparation
    steps.push("## Phase 1: Analysis and Preparation");
    steps.push("1. **Backup Current State**");
    steps.push("   - Create a backup of all affected files");
    steps.push("   - Document current behavior/state");
    
    if (relationships.riskAssessment.level === 'high') {
      steps.push("   - ⚠️ HIGH RISK: Consider creating a feature branch");
      steps.push("   - Review with team before proceeding");
    }

    steps.push("");
    steps.push("2. **Validate Dependencies**");
    steps.push("   - Ensure all required dependencies are available");
    steps.push("   - Check for version compatibility issues");
    
    if (context.packageInfo) {
      steps.push(`   - Current project: ${context.packageInfo.name || 'Unknown'} v${context.packageInfo.version || 'Unknown'}`);
    }

    // Step 2: Implementation phases based on operation type
    steps.push("");
    steps.push("## Phase 2: Implementation");
    
    switch (queryAnalysis.operationType) {
      case 'create':
        steps.push(...this.generateCreateSteps(queryAnalysis, relationships, context));
        break;
      case 'modify':
        steps.push(...this.generateModifySteps(queryAnalysis, relationships, context));
        break;
      case 'delete':
        steps.push(...this.generateDeleteSteps(queryAnalysis, relationships, context));
        break;
      case 'refactor':
        steps.push(...this.generateRefactorSteps(queryAnalysis, relationships, context));
        break;
      case 'debug':
        steps.push(...this.generateDebugSteps(queryAnalysis, relationships, context));
        break;
      default:
        steps.push(...this.generateGenericSteps(queryAnalysis, relationships, context));
    }

    // Step 3: Validation and testing
    steps.push("");
    steps.push("## Phase 3: Validation and Testing");
    steps.push("1. **Unit Testing**");
    if (context.relatedTests.length > 0) {
      steps.push("   - Run existing related tests:");
      context.relatedTests.slice(0, 5).forEach(test => {
        steps.push(`     - ${path.basename(test)}`);
      });
    } else {
      steps.push("   - Create new unit tests for modified functionality");
    }

    steps.push("");
    steps.push("2. **Integration Testing**");
    steps.push("   - Test interactions with dependent components");
    steps.push("   - Verify no breaking changes in public APIs");

    steps.push("");
    steps.push("3. **Manual Testing**");
    steps.push("   - Test the specific functionality mentioned in the query");
    steps.push("   - Verify edge cases and error conditions");

    // Step 4: Deployment considerations
    steps.push("");
    steps.push("## Phase 4: Deployment");
    steps.push("1. **Code Review**");
    steps.push("   - Review all changes with team members");
    steps.push("   - Ensure code follows project standards");

    steps.push("");
    steps.push("2. **Documentation Updates**");
    if (context.documentation.length > 0) {
      steps.push("   - Update existing documentation:");
      context.documentation.slice(0, 3).forEach(doc => {
        steps.push(`     - ${path.basename(doc)}`);
      });
    } else {
      steps.push("   - Create documentation for new functionality");
    }

    steps.push("");
    steps.push("3. **Gradual Rollout**");
    if (relationships.riskAssessment.level === 'high') {
      steps.push("   - Consider feature flags for gradual rollout");
      steps.push("   - Monitor system metrics after deployment");
    } else {
      steps.push("   - Deploy to staging environment first");
      steps.push("   - Monitor for any unexpected issues");
    }

    return steps.join('\n');
  }

  private generateCreateSteps(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string[] {
    const steps: string[] = [];
    
    steps.push("1. **Design New Component**");
    steps.push("   - Define interface/API for new functionality");
    steps.push("   - Identify required dependencies and imports");
    
    if (queryAnalysis.targetComponents.length > 0) {
      steps.push("   - Target components to create:");
      queryAnalysis.targetComponents.forEach(comp => {
        steps.push(`     - ${comp}`);
      });
    }

    steps.push("");
    steps.push("2. **Create Implementation Files**");
    
    // Suggest file locations based on existing structure
    const suggestedLocation = this.suggestFileLocation(queryAnalysis, context);
    if (suggestedLocation) {
      steps.push(`   - Recommended location: ${suggestedLocation}`);
    }
    
    steps.push("   - Implement core functionality");
    steps.push("   - Add proper error handling");
    steps.push("   - Include comprehensive documentation");

    steps.push("");
    steps.push("3. **Integration**");
    steps.push("   - Update imports in dependent files");
    steps.push("   - Register new component in appropriate modules");
    
    if (context.configFiles.length > 0) {
      steps.push("   - Update configuration files if needed:");
      context.configFiles.slice(0, 3).forEach(config => {
        steps.push(`     - ${path.basename(config)}`);
      });
    }

    return steps;
  }

  private generateModifySteps(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string[] {
    const steps: string[] = [];
    
    steps.push("1. **Analyze Current Implementation**");
    steps.push("   - Review existing code structure");
    steps.push("   - Identify modification points");
    
    if (relationships.affectedFiles.length > 0) {
      steps.push("   - Files to modify:");
      relationships.affectedFiles.slice(0, 5).forEach(file => {
        steps.push(`     - ${path.basename(file)}`);
      });
    }

    steps.push("");
    steps.push("2. **Plan Modifications**");
    steps.push("   - Identify backward compatibility requirements");
    steps.push("   - Plan for graceful degradation if needed");
    
    const criticalDependencies = relationships.dependencies.filter(dep => 
      dep.type === 'export' || dep.type === 'definition'
    );
    
    if (criticalDependencies.length > 0) {
      steps.push("   - Critical dependencies to consider:");
      criticalDependencies.slice(0, 5).forEach(dep => {
        steps.push(`     - ${dep.component} in ${path.basename(dep.file)}`);
      });
    }

    steps.push("");
    steps.push("3. **Implement Changes**");
    steps.push("   - Make incremental changes");
    steps.push("   - Test after each significant modification");
    steps.push("   - Update related documentation and comments");

    return steps;
  }

  private generateDeleteSteps(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string[] {
    const steps: string[] = [];
    
    steps.push("1. **Impact Analysis**");
    steps.push("   - Identify all usages of components to be deleted");
    steps.push("   - Plan replacement or migration strategy");
    
    const usages = relationships.dependencies.filter(dep => dep.type === 'usage');
    if (usages.length > 0) {
      steps.push("   - Components with usages to address:");
      usages.slice(0, 5).forEach(usage => {
        steps.push(`     - ${usage.component} used in ${path.basename(usage.file)}`);
      });
    }

    steps.push("");
    steps.push("2. **Deprecation Phase** (Recommended)");
    steps.push("   - Mark components as deprecated");
    steps.push("   - Add deprecation warnings");
    steps.push("   - Provide migration guide");

    steps.push("");
    steps.push("3. **Removal Phase**");
    steps.push("   - Remove deprecated components");
    steps.push("   - Clean up imports and references");
    steps.push("   - Update build configurations");

    return steps;
  }

  private generateRefactorSteps(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string[] {
    const steps: string[] = [];
    
    steps.push("1. **Refactoring Strategy**");
    steps.push("   - Define refactoring goals and success criteria");
    steps.push("   - Plan for maintaining existing functionality");
    
    if (relationships.riskAssessment.level === 'high') {
      steps.push("   - ⚠️ Consider breaking into smaller refactoring steps");
    }

    steps.push("");
    steps.push("2. **Incremental Refactoring**");
    steps.push("   - Start with least risky changes");
    steps.push("   - Maintain test coverage throughout");
    steps.push("   - Use automated refactoring tools where possible");

    steps.push("");
    steps.push("3. **Validation**");
    steps.push("   - Ensure all tests pass after each step");
    steps.push("   - Verify performance hasn't degraded");
    steps.push("   - Check that public APIs remain stable");

    return steps;
  }

  private generateDebugSteps(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string[] {
    const steps: string[] = [];
    
    steps.push("1. **Problem Reproduction**");
    steps.push("   - Create minimal reproduction case");
    steps.push("   - Document expected vs actual behavior");
    steps.push("   - Identify environmental factors");

    steps.push("");
    steps.push("2. **Root Cause Analysis**");
    steps.push("   - Add logging/debugging statements");
    steps.push("   - Use debugging tools and profilers");
    steps.push("   - Trace through related code paths");
    
    if (context.relatedTests.length > 0) {
      steps.push("   - Review related test failures:");
      context.relatedTests.slice(0, 3).forEach(test => {
        steps.push(`     - ${path.basename(test)}`);
      });
    }

    steps.push("");
    steps.push("3. **Fix Implementation**");
    steps.push("   - Implement targeted fix");
    steps.push("   - Add regression tests");
    steps.push("   - Verify fix doesn't introduce new issues");

    return steps;
  }

  private generateGenericSteps(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string[] {
    const steps: string[] = [];
    
    steps.push("1. **Understand Requirements**");
    steps.push("   - Clarify specific requirements");
    steps.push("   - Identify success criteria");
    
    steps.push("");
    steps.push("2. **Implementation**");
    steps.push("   - Follow established patterns in codebase");
    steps.push("   - Maintain consistency with existing code style");
    
    if (relationships.affectedFiles.length > 0) {
      steps.push("   - Work with these files:");
      relationships.affectedFiles.slice(0, 5).forEach(file => {
        steps.push(`     - ${path.basename(file)}`);
      });
    }

    return steps;
  }

  private generateSuggestedApproach(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string {
    const approach: string[] = [];
    
    approach.push(`**Recommended Approach for ${queryAnalysis.operationType} operation:**`);
    approach.push("");

    // Risk-based approach selection
    if (relationships.riskAssessment.level === 'high') {
      approach.push("🔴 **High-Risk Change - Conservative Approach Recommended**");
      approach.push("- Implement changes incrementally");
      approach.push("- Use feature flags for gradual rollout");
      approach.push("- Extensive testing at each step");
      approach.push("- Have rollback plan ready");
    } else if (relationships.riskAssessment.level === 'medium') {
      approach.push("🟡 **Medium-Risk Change - Balanced Approach**");
      approach.push("- Standard development workflow");
      approach.push("- Thorough testing before deployment");
      approach.push("- Code review with focus on affected areas");
    } else {
      approach.push("🟢 **Low-Risk Change - Standard Approach**");
      approach.push("- Follow normal development process");
      approach.push("- Standard testing and review procedures");
    }

    approach.push("");

    // Complexity-based recommendations
    if (queryAnalysis.complexity === 'complex') {
      approach.push("**Complexity Management:**");
      approach.push("- Break down into smaller, manageable tasks");
      approach.push("- Consider pair programming or team collaboration");
      approach.push("- Document architectural decisions");
    } else if (queryAnalysis.complexity === 'simple') {
      approach.push("**Simple Change - Direct Implementation:**");
      approach.push("- Can be implemented in a single session");
      approach.push("- Minimal coordination required");
    }

    approach.push("");

    // Technology-specific recommendations
    if (context.packageInfo) {
      approach.push("**Technology Stack Considerations:**");
      
      const dependencies = context.packageInfo.dependencies || {};
      const devDependencies = context.packageInfo.devDependencies || {};
      
      if (dependencies.react || devDependencies.react) {
        approach.push("- Follow React best practices and hooks patterns");
        approach.push("- Consider component lifecycle implications");
      }
      
      if (dependencies.typescript || devDependencies.typescript) {
        approach.push("- Maintain strong typing throughout");
        approach.push("- Update type definitions as needed");
      }
      
      if (dependencies.express || dependencies.fastify) {
        approach.push("- Consider API versioning for breaking changes");
        approach.push("- Maintain backward compatibility where possible");
      }
    }

    return approach.join('\n');
  }

  private generateAlternativeApproaches(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string[] {
    const alternatives: string[] = [];
    
    // Operation-specific alternatives
    switch (queryAnalysis.operationType) {
      case 'create':
        alternatives.push("**Alternative 1: Extend Existing Component**");
        alternatives.push("- Modify existing similar component instead of creating new one");
        alternatives.push("- Pros: Less code duplication, faster implementation");
        alternatives.push("- Cons: May increase complexity of existing component");
        
        alternatives.push("");
        alternatives.push("**Alternative 2: Use Composition Pattern**");
        alternatives.push("- Create smaller, composable components");
        alternatives.push("- Pros: Better reusability, easier testing");
        alternatives.push("- Cons: More initial setup required");
        break;

      case 'modify':
        alternatives.push("**Alternative 1: Create New Version**");
        alternatives.push("- Keep existing implementation, create new version");
        alternatives.push("- Pros: Zero risk to existing functionality");
        alternatives.push("- Cons: Code duplication, migration overhead");
        
        alternatives.push("");
        alternatives.push("**Alternative 2: Gradual Migration**");
        alternatives.push("- Implement changes behind feature flags");
        alternatives.push("- Pros: Safe rollout, easy rollback");
        alternatives.push("- Cons: Temporary code complexity");
        break;

      case 'refactor':
        alternatives.push("**Alternative 1: Big Bang Refactor**");
        alternatives.push("- Complete refactor in single change");
        alternatives.push("- Pros: Clean, consistent result");
        alternatives.push("- Cons: High risk, difficult to review");
        
        alternatives.push("");
        alternatives.push("**Alternative 2: Strangler Fig Pattern**");
        alternatives.push("- Gradually replace old code with new implementation");
        alternatives.push("- Pros: Low risk, continuous delivery");
        alternatives.push("- Cons: Longer timeline, temporary complexity");
        break;

      default:
        alternatives.push("**Alternative 1: Minimal Change Approach**");
        alternatives.push("- Make smallest possible change to achieve goal");
        alternatives.push("- Pros: Low risk, quick implementation");
        alternatives.push("- Cons: May not address root cause");
        
        alternatives.push("");
        alternatives.push("**Alternative 2: Comprehensive Solution**");
        alternatives.push("- Address broader context and related issues");
        alternatives.push("- Pros: More robust, future-proof solution");
        alternatives.push("- Cons: Higher complexity, longer timeline");
    }

    // Risk-based alternatives
    if (relationships.riskAssessment.level === 'high') {
      alternatives.push("");
      alternatives.push("**Alternative 3: Proof of Concept First**");
      alternatives.push("- Create isolated proof of concept");
      alternatives.push("- Validate approach before full implementation");
      alternatives.push("- Pros: Reduces risk, validates assumptions");
      alternatives.push("- Cons: Additional time investment");
    }

    return alternatives;
  }

  private generateTestingStrategy(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string {
    const strategy: string[] = [];
    
    strategy.push("**Comprehensive Testing Strategy:**");
    strategy.push("");

    // Unit Testing
    strategy.push("**1. Unit Testing**");
    if (context.relatedTests.length > 0) {
      strategy.push("   - Existing tests to run/update:");
      context.relatedTests.slice(0, 5).forEach(test => {
        strategy.push(`     - ${path.basename(test)}`);
      });
    } else {
      strategy.push("   - Create new unit tests for:");
      queryAnalysis.targetComponents.slice(0, 5).forEach(component => {
        strategy.push(`     - ${component}`);
      });
    }

    strategy.push("");

    // Integration Testing
    strategy.push("**2. Integration Testing**");
    strategy.push("   - Test component interactions");
    strategy.push("   - Verify data flow between components");
    
    if (relationships.dependencies.length > 0) {
      strategy.push("   - Focus on these relationships:");
      relationships.dependencies.slice(0, 5).forEach(dep => {
        strategy.push(`     - ${dep.type}: ${dep.component} in ${path.basename(dep.file)}`);
      });
    }

    strategy.push("");

    // End-to-End Testing
    strategy.push("**3. End-to-End Testing**");
    strategy.push("   - Test complete user workflows");
    strategy.push("   - Verify system behavior under realistic conditions");
    
    if (queryAnalysis.operationType === 'create') {
      strategy.push("   - Test new functionality in isolation");
      strategy.push("   - Test integration with existing features");
    } else if (queryAnalysis.operationType === 'modify') {
      strategy.push("   - Regression testing for existing functionality");
      strategy.push("   - Validation of new/changed behavior");
    }

    strategy.push("");

    // Performance Testing
    if (relationships.riskAssessment.level !== 'low') {
      strategy.push("**4. Performance Testing**");
      strategy.push("   - Benchmark before and after changes");
      strategy.push("   - Monitor memory usage and execution time");
      strategy.push("   - Load testing for high-traffic components");
      strategy.push("");
    }

    // Error Handling Testing
    strategy.push("**5. Error Handling & Edge Cases**");
    strategy.push("   - Test error conditions and edge cases");
    strategy.push("   - Verify graceful degradation");
    strategy.push("   - Test with invalid/unexpected inputs");

    strategy.push("");

    // Test Automation
    strategy.push("**6. Test Automation**");
    strategy.push("   - Integrate tests into CI/CD pipeline");
    strategy.push("   - Set up automated regression testing");
    strategy.push("   - Configure test coverage reporting");

    return strategy.join('\n');
  }

  private generateRollbackPlan(
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    context: ContextData
  ): string {
    const rollback: string[] = [];
    
    rollback.push("**Comprehensive Rollback Plan:**");
    rollback.push("");

    // Immediate Rollback
    rollback.push("**1. Immediate Rollback (< 5 minutes)**");
    rollback.push("   - Revert to previous deployment/commit");
    rollback.push("   - Use version control to restore previous state");
    
    if (context.configFiles.length > 0) {
      rollback.push("   - Restore configuration files:");
      context.configFiles.slice(0, 3).forEach(config => {
        rollback.push(`     - ${path.basename(config)}`);
      });
    }

    rollback.push("");

    // Database/State Rollback
    if (queryAnalysis.keywords.some(keyword => 
      ['database', 'schema', 'migration', 'data'].includes(keyword.toLowerCase())
    )) {
      rollback.push("**2. Data Rollback (if applicable)**");
      rollback.push("   - Restore database from backup");
      rollback.push("   - Run reverse migrations");
      rollback.push("   - Verify data integrity after rollback");
      rollback.push("");
    }

    // Partial Rollback
    rollback.push("**3. Partial Rollback Options**");
    rollback.push("   - Use feature flags to disable new functionality");
    rollback.push("   - Rollback specific components while keeping others");
    
    if (relationships.affectedFiles.length > 1) {
      rollback.push("   - Files that can be independently rolled back:");
      relationships.affectedFiles.slice(0, 5).forEach(file => {
        rollback.push(`     - ${path.basename(file)}`);
      });
    }

    rollback.push("");

    // Monitoring and Validation
    rollback.push("**4. Post-Rollback Validation**");
    rollback.push("   - Run health checks and smoke tests");
    rollback.push("   - Monitor system metrics and error rates");
    rollback.push("   - Verify user-facing functionality");
    
    if (context.relatedTests.length > 0) {
      rollback.push("   - Run critical test suites:");
      context.relatedTests.slice(0, 3).forEach(test => {
        rollback.push(`     - ${path.basename(test)}`);
      });
    }

    rollback.push("");

    // Communication Plan
    rollback.push("**5. Communication Plan**");
    rollback.push("   - Notify stakeholders of rollback");
    rollback.push("   - Document rollback reason and timeline");
    rollback.push("   - Plan for re-deployment after fixes");

    rollback.push("");

    // Prevention
    rollback.push("**6. Future Prevention**");
    rollback.push("   - Analyze root cause of rollback need");
    rollback.push("   - Improve testing/validation processes");
    rollback.push("   - Consider additional safeguards");

    return rollback.join('\n');
  }

  private suggestFileLocation(queryAnalysis: QueryAnalysis, context: ContextData): string | null {
    // Analyze existing file structure to suggest appropriate location
    const relevantFiles = context.relevantCode.map(code => code.file);
    
    if (relevantFiles.length === 0) return null;

    // Find common directory patterns
    const directories = relevantFiles.map(file => path.dirname(file));
    const directoryCount = directories.reduce((acc, dir) => {
      acc[dir] = (acc[dir] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Find most common directory
    const mostCommonDir = Object.entries(directoryCount)
      .sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0];

    if (mostCommonDir) {
      // Suggest file name based on target components
      const suggestedName = queryAnalysis.targetComponents[0] || 'newComponent';
      return path.join(mostCommonDir, `${suggestedName}.ts`);
    }

    return null;
  }
}