import * as fs from "fs/promises";
import * as path from "path";
import { QueryAnalysis } from "./queryAnalyzer.ts";
import { RelationshipMap } from "./relationshipMapper.ts";

export interface RelevantCode {
  file: string;
  content: string;
  relevanceScore: number;
  summary?: string;
}

export interface RecentChange {
  file: string;
  timestamp: string;
  summary: string;
}

export interface ContextData {
  relevantCode: RelevantCode[];
  relatedTests: string[];
  documentation: string[];
  recentChanges: RecentChange[];
  configFiles: string[];
  packageInfo: any;
}

export class ContextAggregator {
  private readonly maxCodeSnippetLength = 2000;
  private readonly maxRelevantFiles = 20;

  async gatherContext(
    workingDirectory: string,
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    analysisDepth: 'shallow' | 'deep' | 'comprehensive'
  ): Promise<ContextData> {
    const relevantCode = await this.gatherRelevantCode(
      workingDirectory,
      queryAnalysis,
      relationships,
      analysisDepth
    );

    const relatedTests = await this.findRelatedTests(
      workingDirectory,
      queryAnalysis.targetComponents,
      relationships.affectedFiles
    );

    const documentation = await this.findDocumentation(
      workingDirectory,
      queryAnalysis.targetComponents
    );

    const recentChanges = await this.getRecentChanges(
      workingDirectory,
      relationships.affectedFiles
    );

    const configFiles = await this.findConfigFiles(workingDirectory);
    const packageInfo = await this.getPackageInfo(workingDirectory);

    return {
      relevantCode,
      relatedTests,
      documentation,
      recentChanges,
      configFiles,
      packageInfo,
    };
  }

  private async gatherRelevantCode(
    workingDirectory: string,
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    analysisDepth: 'shallow' | 'deep' | 'comprehensive'
  ): Promise<RelevantCode[]> {
    const relevantCode: RelevantCode[] = [];
    const processedFiles = new Set<string>();

    // Start with directly affected files
    for (const file of relationships.affectedFiles) {
      if (processedFiles.has(file)) continue;
      processedFiles.add(file);

      try {
        const content = await fs.readFile(file, 'utf-8');
        const relevanceScore = this.calculateRelevanceScore(
          file,
          content,
          queryAnalysis,
          relationships
        );

        if (relevanceScore > 0.3) { // Only include files with decent relevance
          relevantCode.push({
            file,
            content: this.truncateContent(content),
            relevanceScore,
            summary: this.generateCodeSummary(content, file),
          });
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    // For deep/comprehensive analysis, include related files
    if (analysisDepth !== 'shallow') {
      await this.includeRelatedFiles(
        workingDirectory,
        queryAnalysis,
        relationships,
        relevantCode,
        processedFiles,
        analysisDepth === 'comprehensive'
      );
    }

    // Sort by relevance score and limit results
    relevantCode.sort((a, b) => b.relevanceScore - a.relevanceScore);
    return relevantCode.slice(0, this.maxRelevantFiles);
  }

  private calculateRelevanceScore(
    file: string,
    content: string,
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap
  ): number {
    let score = 0;

    // Base score for being in affected files
    if (relationships.affectedFiles.includes(file)) {
      score += 0.5;
    }

    // Score based on target component mentions
    const componentMentions = queryAnalysis.targetComponents.reduce((count, component) => {
      const regex = new RegExp(`\\b${component}\\b`, 'gi');
      const matches = content.match(regex);
      return count + (matches ? matches.length : 0);
    }, 0);
    score += Math.min(componentMentions * 0.1, 0.4);

    // Score based on keyword relevance
    const keywordMentions = queryAnalysis.keywords.reduce((count, keyword) => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
      const matches = content.match(regex);
      return count + (matches ? matches.length : 0);
    }, 0);
    score += Math.min(keywordMentions * 0.05, 0.3);

    // Boost score for certain file types based on operation type
    const fileName = path.basename(file).toLowerCase();
    if (queryAnalysis.operationType === 'create' && fileName.includes('template')) {
      score += 0.2;
    }
    if (queryAnalysis.operationType === 'debug' && fileName.includes('test')) {
      score += 0.3;
    }
    if (fileName.includes('config') || fileName.includes('setting')) {
      score += 0.1;
    }

    // Boost score based on relationship types
    const fileRelationships = relationships.dependencies.filter(dep => dep.file === file);
    const hasDefinitions = fileRelationships.some(rel => rel.type === 'definition');
    const hasExports = fileRelationships.some(rel => rel.type === 'export');
    
    if (hasDefinitions) score += 0.2;
    if (hasExports) score += 0.15;

    return Math.min(score, 1.0); // Cap at 1.0
  }

  private truncateContent(content: string): string {
    if (content.length <= this.maxCodeSnippetLength) {
      return content;
    }

    // Try to truncate at a natural boundary (end of function, class, etc.)
    const lines = content.split('\n');
    let truncated = '';
    let braceCount = 0;
    let inFunction = false;

    for (const line of lines) {
      truncated += line + '\n';
      
      // Track braces to find natural boundaries
      for (const char of line) {
        if (char === '{') {
          braceCount++;
          if (line.includes('function') || line.includes('class')) {
            inFunction = true;
          }
        } else if (char === '}') {
          braceCount--;
          if (braceCount === 0 && inFunction) {
            inFunction = false;
            // Good place to truncate if we're near the limit
            if (truncated.length > this.maxCodeSnippetLength * 0.8) {
              return truncated + '\n// ... (truncated)';
            }
          }
        }
      }

      if (truncated.length > this.maxCodeSnippetLength) {
        return truncated.substring(0, this.maxCodeSnippetLength) + '\n// ... (truncated)';
      }
    }

    return truncated;
  }

  private generateCodeSummary(content: string, filePath: string): string {
    const fileName = path.basename(filePath);
    const lines = content.split('\n');
    
    // Extract key elements
    const exports = lines.filter(line => line.trim().startsWith('export')).length;
    const functions = lines.filter(line => 
      line.includes('function') || line.match(/const\s+\w+\s*=.*=>/)
    ).length;
    const classes = lines.filter(line => line.trim().startsWith('class')).length;
    const imports = lines.filter(line => line.trim().startsWith('import')).length;

    let summary = `${fileName}: `;
    const parts = [];
    
    if (classes > 0) parts.push(`${classes} class${classes > 1 ? 'es' : ''}`);
    if (functions > 0) parts.push(`${functions} function${functions > 1 ? 's' : ''}`);
    if (exports > 0) parts.push(`${exports} export${exports > 1 ? 's' : ''}`);
    if (imports > 0) parts.push(`${imports} import${imports > 1 ? 's' : ''}`);

    summary += parts.join(', ') || 'code file';
    
    // Add first comment or docstring if available
    const firstComment = lines.find(line => 
      line.trim().startsWith('//') || line.trim().startsWith('/*') || line.trim().startsWith('*')
    );
    if (firstComment) {
      const comment = firstComment.trim().replace(/^(\/\/|\*|\/\*)/, '').trim();
      if (comment.length > 0 && comment.length < 100) {
        summary += ` - ${comment}`;
      }
    }

    return summary;
  }

  private async includeRelatedFiles(
    workingDirectory: string,
    queryAnalysis: QueryAnalysis,
    relationships: RelationshipMap,
    relevantCode: RelevantCode[],
    processedFiles: Set<string>,
    comprehensive: boolean
  ): Promise<void> {
    // Include files that import/export target components
    for (const [file, dependencies] of relationships.dependencyGraph) {
      if (processedFiles.has(file)) continue;

      const hasRelevantDependencies = dependencies.some(dep => 
        relationships.affectedFiles.includes(dep) ||
        queryAnalysis.targetComponents.some(comp => dep.includes(comp))
      );

      if (hasRelevantDependencies) {
        try {
          const content = await fs.readFile(file, 'utf-8');
          const relevanceScore = this.calculateRelevanceScore(
            file,
            content,
            queryAnalysis,
            relationships
          );

          if (relevanceScore > 0.2) {
            relevantCode.push({
              file,
              content: this.truncateContent(content),
              relevanceScore,
              summary: this.generateCodeSummary(content, file),
            });
            processedFiles.add(file);
          }
        } catch (error) {
          // Skip files that can't be read
        }
      }
    }

    // For comprehensive analysis, include utility files and shared modules
    if (comprehensive) {
      await this.includeUtilityFiles(
        workingDirectory,
        queryAnalysis,
        relevantCode,
        processedFiles
      );
    }
  }

  private async includeUtilityFiles(
    workingDirectory: string,
    queryAnalysis: QueryAnalysis,
    relevantCode: RelevantCode[],
    processedFiles: Set<string>
  ): Promise<void> {
    const utilityPatterns = [
      'util', 'helper', 'common', 'shared', 'lib', 'core',
      'constant', 'config', 'type', 'interface', 'model'
    ];

    try {
      const allFiles = await this.getAllFiles(workingDirectory);
      
      for (const file of allFiles) {
        if (processedFiles.has(file)) continue;
        
        const fileName = path.basename(file).toLowerCase();
        const isUtilityFile = utilityPatterns.some(pattern => 
          fileName.includes(pattern)
        );

        if (isUtilityFile) {
          try {
            const content = await fs.readFile(file, 'utf-8');
            
            // Check if this utility file is relevant to our query
            const hasRelevantContent = queryAnalysis.keywords.some(keyword =>
              content.toLowerCase().includes(keyword.toLowerCase())
            ) || queryAnalysis.targetComponents.some(component =>
              content.includes(component)
            );

            if (hasRelevantContent) {
              relevantCode.push({
                file,
                content: this.truncateContent(content),
                relevanceScore: 0.3, // Medium relevance for utility files
                summary: this.generateCodeSummary(content, file),
              });
              processedFiles.add(file);
            }
          } catch (error) {
            // Skip files that can't be read
          }
        }
      }
    } catch (error) {
      // Skip if we can't read the directory
    }
  }

  private async findRelatedTests(
    workingDirectory: string,
    targetComponents: string[],
    affectedFiles: string[]
  ): Promise<string[]> {
    const testFiles: string[] = [];
    
    try {
      const allFiles = await this.getAllFiles(workingDirectory);
      
      for (const file of allFiles) {
        const fileName = path.basename(file).toLowerCase();
        const isTestFile = fileName.includes('test') || 
                          fileName.includes('spec') ||
                          file.includes('__tests__') ||
                          file.includes('.test.') ||
                          file.includes('.spec.');

        if (isTestFile) {
          try {
            const content = await fs.readFile(file, 'utf-8');
            
            // Check if test file is related to our target components or affected files
            const isRelated = targetComponents.some(component =>
              content.includes(component)
            ) || affectedFiles.some(affectedFile => {
              const baseName = path.basename(affectedFile, path.extname(affectedFile));
              return content.includes(baseName) || fileName.includes(baseName.toLowerCase());
            });

            if (isRelated) {
              testFiles.push(file);
            }
          } catch (error) {
            // Skip files that can't be read
          }
        }
      }
    } catch (error) {
      // Skip if we can't read the directory
    }

    return testFiles;
  }

  private async findDocumentation(
    workingDirectory: string,
    targetComponents: string[]
  ): Promise<string[]> {
    const docFiles: string[] = [];
    
    try {
      const allFiles = await this.getAllFiles(workingDirectory);
      
      for (const file of allFiles) {
        const fileName = path.basename(file).toLowerCase();
        const isDocFile = fileName.endsWith('.md') || 
                         fileName.endsWith('.txt') ||
                         fileName.endsWith('.rst') ||
                         fileName === 'readme' ||
                         fileName.includes('doc') ||
                         fileName.includes('guide');

        if (isDocFile) {
          try {
            const content = await fs.readFile(file, 'utf-8');
            
            // Check if documentation mentions our target components
            const isRelevant = targetComponents.some(component =>
              content.toLowerCase().includes(component.toLowerCase())
            );

            if (isRelevant) {
              docFiles.push(file);
            }
          } catch (error) {
            // Skip files that can't be read
          }
        }
      }
    } catch (error) {
      // Skip if we can't read the directory
    }

    return docFiles;
  }

  private async getRecentChanges(
    workingDirectory: string,
    affectedFiles: string[]
  ): Promise<RecentChange[]> {
    const recentChanges: RecentChange[] = [];
    
    // This is a simplified implementation - in a real scenario,
    // you'd integrate with git or other VCS to get actual change history
    try {
      for (const file of affectedFiles) {
        try {
          const stats = await fs.stat(file);
          const modifiedTime = stats.mtime.toISOString();
          
          // Simple heuristic: if file was modified recently, include it
          const daysSinceModified = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
          
          if (daysSinceModified < 30) { // Modified within last 30 days
            recentChanges.push({
              file,
              timestamp: modifiedTime,
              summary: `File modified ${Math.floor(daysSinceModified)} days ago`,
            });
          }
        } catch (error) {
          // Skip files that can't be accessed
        }
      }
    } catch (error) {
      // Skip if we can't access file system
    }

    return recentChanges.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }

  private async findConfigFiles(workingDirectory: string): Promise<string[]> {
    const configFiles: string[] = [];
    const configPatterns = [
      'package.json', 'tsconfig.json', 'webpack.config.js', 'vite.config.ts',
      '.env', '.env.local', 'config.js', 'config.ts', 'settings.json',
      '.gitignore', '.eslintrc', '.prettierrc', 'jest.config.js'
    ];

    try {
      const files = await fs.readdir(workingDirectory);
      
      for (const file of files) {
        if (configPatterns.some(pattern => file.includes(pattern))) {
          const fullPath = path.join(workingDirectory, file);
          try {
            await fs.access(fullPath);
            configFiles.push(fullPath);
          } catch (error) {
            // Skip files that can't be accessed
          }
        }
      }
    } catch (error) {
      // Skip if we can't read the directory
    }

    return configFiles;
  }

  private async getPackageInfo(workingDirectory: string): Promise<any> {
    try {
      const packageJsonPath = path.join(workingDirectory, 'package.json');
      const content = await fs.readFile(packageJsonPath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      return null;
    }
  }

  private async getAllFiles(workingDirectory: string): Promise<string[]> {
    const files: string[] = [];
    
    const traverse = async (dir: string, depth: number = 0): Promise<void> => {
      if (depth > 4) return; // Limit recursion depth
      
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          
          if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
            await traverse(fullPath, depth + 1);
          } else if (entry.isFile()) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    };

    await traverse(workingDirectory);
    return files;
  }

  private shouldSkipDirectory(name: string): boolean {
    const skipDirs = [
      'node_modules', '.git', 'dist', 'build', '.next', '.nuxt', 'coverage',
      '__pycache__', '.pytest_cache', 'venv', 'env', '.venv', '.env',
      'target', 'bin', 'obj', '.vs', '.vscode',
    ];
    return skipDirs.includes(name) || name.startsWith('.');
  }
}