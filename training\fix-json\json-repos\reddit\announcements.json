{"kind": "Listing", "data": {"after": "t3_blev4z", "dist": 26, "modhash": "jsg7pkvnnb7b3eca2f93aa1974b0614f02ff573e4baaf0692c", "geo_filter": null, "children": [{"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "", "author_fullname": "t2_nsj7i", "saved": false, "mod_reason_title": null, "gilded": 1, "clicked": false, "title": "This subreddit is closed for new posts and comments. For future updates, announcements, and news related to Reddit Inc. and the platform, please visit r/reddit.", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_t93ec3", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.4, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 0, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 0, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "default", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1646697768.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "reddit.com", "allow_live_comments": true, "selftext_html": null, "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "url_overridden_by_dest": "https://www.reddit.com/r/reddit/", "view_count": null, "archived": true, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "t93ec3", "is_robot_indexable": true, "report_reasons": null, "author": "keth<PERSON><PERSON>", "discussion_type": null, "num_comments": 1, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/t93ec3/this_subreddit_is_closed_for_new_posts_and/", "stickied": true, "url": "https://www.reddit.com/r/reddit/", "subreddit_subscribers": *********, "created_utc": 1646697768.0, "num_crossposts": 42, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "", "user_reports": [], "saved": false, "mod_reason_title": null, "gilded": 1, "clicked": false, "title": "COVID denialism and policy clarifications", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_pg006s", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.63, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 13947, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "author_fullname": "t2_qromv", "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 13947, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "default", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "link", "content_categories": null, "is_self": false, "mod_note": null, "crosspost_parent_list": [{"approved_at_utc": null, "subreddit": "RedditSafety", "selftext": "“Happy” Wednesday everyone\n\nAs u/spez mentioned in his [announcement post](https://www.reddit.com/r/announcements/comments/pbmy5y/debate_dissent_and_protest_on_reddit/) last week, COVID has been hard on all of us. It will likely go down as one of the most defining periods of our generation. Many of us have lost loved ones to the virus. It has caused confusion, fear, frustration, and served to further divide us. It is my job to oversee the enforcement of our policies on the platform. I’ve never professed to be perfect at this. Our policies, and how we enforce them, evolve with time. We base these evolutions on two things: user trends and data. Last year, after we rolled out [the largest policy change in Reddit’s history](https://www.reddit.com/r/announcements/comments/hi3oht/update_to_our_content_policy/), I shared a [post](https://www.reddit.com/r/redditsecurity/comments/idclo1/understanding_hate_on_reddit_and_the_impact_of/) on the prevalence of hateful content on the platform. Today, many of our users are telling us that they are confused and even frustrated with our handling of COVID denial content on the platform, so it seemed like the right time for us to share some data around the topic. \n\n*Analysis of Covid Denial*\n\nWe sought to answer the following questions:\n\n* How often is this content submitted?\n* What is the community reception?\n* Where are the concentration centers for this content?\n\nBelow is a chart of all of the COVID-related content that has been posted on the platform since January 1, 2020. We are using common keywords and known COVID focused communities to measure this. The volume has been relatively flat since mid last year, but since July (coinciding with the increased prevalence of the Delta variant), we have seen a sizable increase. \n\n[COVID Content Submissions](https://preview.redd.it/5draikoecxk71.png?width=1145&amp;format=png&amp;auto=webp&amp;s=4aa9a815e3b8637ad2c8f1a654b1b5f82cac4d97)\n\nThe trend is even more notable when we look at COVID-related content reported to us by users. Since August, we see approximately 2.5k reports/day vs an average of around 500 reports/day a year ago. This is approximately 2.5% of all COVID related content. \n\n[Reports on COVID Content](https://preview.redd.it/v35d5wfgcxk71.png?width=1146&amp;format=png&amp;auto=webp&amp;s=f6604d4fae70f04ba0cbf5beb3949aab5169fc99)\n\nWhile this data alone does not tell us that COVID denial content on the platform is increasing, it is certainly an indicator. To help make this story more clear, we looked into potential networks of denial communities. There are some well known subreddits dedicated to discussing and challenging the policy response to COVID, and we used this as a basis to identify other similar subreddits. I’ll refer to these as “high signal subs.”\n\nLast year, we saw that less than 1% of COVID content came from these high signal subs, today we see that it's over 3%. COVID content in these communities is around 3x more likely to be reported than in other communities (this is fairly consistent over the last year). Together with information above we can infer that there has been an increase in COVID denial content on the platform, and that increase has been more pronounced since July. While the increase is suboptimal, it is noteworthy that the large majority of the content is outside of these COVID denial subreddits. It’s also hard to put an exact number on the increase or the overall volume. \n\nAn important part of our moderation structure is the community members themselves. How are users responding to COVID-related posts? How much visibility do they have? Is there a difference in the response in these high signal subs than the rest of Reddit?\n\nHigh Signal Subs\n\n* Content positively received - 48% on posts, 43% on comments\n* Median exposure - 119 viewers on posts, 100 viewers on comments\n* Median vote count - 21 on posts, 5 on comments\n\nAll Other Subs\n\n* Content positively received - 27% on posts, 41% on comments\n* Median exposure - 24 viewers on posts, 100 viewers on comments\n* Median vote count - 10 on posts, 6 on comments\n\nThis tells us that in these high signal subs, there is generally less of the critical feedback mechanism than we would expect to see in other non-denial based subreddits, which leads to content in these communities being more visible than the typical COVID post in other subreddits. \n\n*Interference Analysis*\n\nIn addition to this, we have also been investigating the claims around targeted interference by some of these subreddits. While we want to be a place where people can explore unpopular views, it is never acceptable to interfere with other communities. Claims of “brigading” are common and often hard to quantify. However, in this case, we found very clear signals indicating that r/NoNewNormal was the source of around 80 brigades in the last 30 days (largely directed at communities with more mainstream views on COVID or location-based communities that have been discussing COVID restrictions). This behavior continued even after a warning was issued from our team to the Mods. r/NoNewNormal is the only subreddit in our list of high signal subs where we have identified this behavior and it is one of the largest sources of community interference we surfaced as part of this work (we will be investigating a few other unrelated subreddits as well). \n\n*Analysis into Action*\n\nWe are taking several actions:\n\n1. Ban r/NoNewNormal immediately for breaking our rules against brigading\n2. Quarantine 54 additional COVID denial subreddits under [Rule 1](https://www.redditinc.com/policies/content-policy)\n3. Build a new reporting feature for moderators to allow them to better provide us signal when they see community interference. It will take us a few days to get this built, and we will subsequently evaluate the usefulness of this feature. \n\n*Clarifying our Policies*\n\nWe also hear the feedback that our policies are not clear around our handling of health misinformation. To address this, we wanted to provide a summary of our current approach to misinformation/disinformation in our [Content Policy](https://www.redditinc.com/policies/content-policy). \n\nOur approach is broken out into (1) how we deal with health misinformation (falsifiable health related information that is disseminated regardless of intent), (2) health disinformation (falsifiable health information that is disseminated with an intent to mislead), (3) problematic subreddits that pose misinformation risks, and (4) problematic users who invade other subreddits to “debate” topics unrelated to the wants/needs of that community. \n\n1. Health Misinformation. We have long interpreted our rule against posting content that “encourages” physical harm, in this [help center article](https://www.reddithelp.com/hc/en-us/articles/360043513151), as covering health misinformation, meaning falsifiable health information that encourages or poses a significant risk of physical harm to the reader. For example, a post pushing a verifiably false “cure” for cancer that would actually result in harm to people would violate our policies.  \n\n2. Health Disinformation. Our rule against impersonation, as described in this [help center article](https://www.reddithelp.com/hc/en-us/articles/360043075032), extends to “manipulated content presented to mislead.” We have interpreted this rule as covering health disinformation, meaning falsifiable health information that has been manipulated and presented to mislead. This includes falsified medical data and faked WHO/CDC advice.  \n\n3. Problematic subreddits.  We have long applied [quarantine](https://www.reddithelp.com/hc/en-us/articles/360043069012) to communities that warrant additional scrutiny. The purpose of quarantining a community is to prevent its content from being accidentally viewed or viewed without appropriate context. \n\n4. Community Interference. Also relevant to the discussion of the activities of problematic subreddits, [Rule 2](https://www.redditinc.com/policies/content-policy) forbids users or communities from “cheating” or engaging in “content manipulation” or otherwise interfering with or disrupting Reddit communities.  We have interpreted this rule as forbidding communities from manipulating the platform, creating inauthentic conversations, and picking fights with other communities. We typically enforce Rule 2 through our anti-brigading efforts, although it is still an example of bad behavior that has led to bans of a variety of subreddits.\n\nAs I mentioned at the start, we never claim to be perfect at these things but our goal is to constantly evolve. These prevalence studies are helpful for evolving our thinking. We also need to evolve how we communicate our policy and enforcement decisions. As always, I will stick around to answer your questions and will also be joined by u/traceroo our GC and head of policy.", "author_fullname": "t2_qromv", "saved": false, "mod_reason_title": null, "gilded": 4, "clicked": false, "title": "COVID denialism and policy clarifications", "link_flair_richtext": [], "subreddit_name_prefixed": "r/RedditSafety", "hidden": false, "pwls": null, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "media_metadata": {"5draikoecxk71": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 36, "x": 108, "u": "https://preview.redd.it/5draikoecxk71.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=1249010341b8830a35be5d116b133d32a99e6825"}, {"y": 73, "x": 216, "u": "https://preview.redd.it/5draikoecxk71.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=c1ec1519c465691bf7ec14db9c5edfd0b5f24bf1"}, {"y": 108, "x": 320, "u": "https://preview.redd.it/5draikoecxk71.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=7c346bc32ba5331d56f702adc678b571bfb84214"}, {"y": 216, "x": 640, "u": "https://preview.redd.it/5draikoecxk71.png?width=640&amp;crop=smart&amp;auto=webp&amp;s=cb7f851a0e10cc253a09d7d99960657c2f9ef2ea"}, {"y": 325, "x": 960, "u": "https://preview.redd.it/5draikoecxk71.png?width=960&amp;crop=smart&amp;auto=webp&amp;s=796ca85295ba630ca48049a376457eb8c28b4105"}, {"y": 365, "x": 1080, "u": "https://preview.redd.it/5draikoecxk71.png?width=1080&amp;crop=smart&amp;auto=webp&amp;s=bc19dca52685cea9cffd338de4e1890b7376b2c6"}], "s": {"y": 388, "x": 1145, "u": "https://preview.redd.it/5draikoecxk71.png?width=1145&amp;format=png&amp;auto=webp&amp;s=4aa9a815e3b8637ad2c8f1a654b1b5f82cac4d97"}, "id": "5draikoecxk71"}, "v35d5wfgcxk71": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 36, "x": 108, "u": "https://preview.redd.it/v35d5wfgcxk71.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=9c272d4d41422fd9975df3869581036c27a36947"}, {"y": 72, "x": 216, "u": "https://preview.redd.it/v35d5wfgcxk71.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=137285fcb03062626daed44896eae87335bdaa1d"}, {"y": 108, "x": 320, "u": "https://preview.redd.it/v35d5wfgcxk71.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=ee449cf5b1c7ca8c3762a5022859bf38f9e25e70"}, {"y": 216, "x": 640, "u": "https://preview.redd.it/v35d5wfgcxk71.png?width=640&amp;crop=smart&amp;auto=webp&amp;s=a87cbc5b6cc0afd8fe3d7101551351b564273ff6"}, {"y": 324, "x": 960, "u": "https://preview.redd.it/v35d5wfgcxk71.png?width=960&amp;crop=smart&amp;auto=webp&amp;s=11ff5ecbe225d058dc5c94bea02af30ac3b1ca53"}, {"y": 364, "x": 1080, "u": "https://preview.redd.it/v35d5wfgcxk71.png?width=1080&amp;crop=smart&amp;auto=webp&amp;s=fe80b2b210371caf7c85ec09748ec1301ab82eaf"}], "s": {"y": 387, "x": 1146, "u": "https://preview.redd.it/v35d5wfgcxk71.png?width=1146&amp;format=png&amp;auto=webp&amp;s=f6604d4fae70f04ba0cbf5beb3949aab5169fc99"}, "id": "v35d5wfgcxk71"}}, "name": "t3_pfyqqn", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.66, "author_flair_background_color": null, "subreddit_type": "restricted", "ups": 18261, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 18261, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1630517431.0, "link_flair_type": "text", "wls": null, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.RedditSafety", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;“Happy” Wednesday everyone&lt;/p&gt;\n\n&lt;p&gt;As &lt;a href=\"/u/spez\"&gt;u/spez&lt;/a&gt; mentioned in his &lt;a href=\"https://www.reddit.com/r/announcements/comments/pbmy5y/debate_dissent_and_protest_on_reddit/\"&gt;announcement post&lt;/a&gt; last week, COVID has been hard on all of us. It will likely go down as one of the most defining periods of our generation. Many of us have lost loved ones to the virus. It has caused confusion, fear, frustration, and served to further divide us. It is my job to oversee the enforcement of our policies on the platform. I’ve never professed to be perfect at this. Our policies, and how we enforce them, evolve with time. We base these evolutions on two things: user trends and data. Last year, after we rolled out &lt;a href=\"https://www.reddit.com/r/announcements/comments/hi3oht/update_to_our_content_policy/\"&gt;the largest policy change in Reddit’s history&lt;/a&gt;, I shared a &lt;a href=\"https://www.reddit.com/r/redditsecurity/comments/idclo1/understanding_hate_on_reddit_and_the_impact_of/\"&gt;post&lt;/a&gt; on the prevalence of hateful content on the platform. Today, many of our users are telling us that they are confused and even frustrated with our handling of COVID denial content on the platform, so it seemed like the right time for us to share some data around the topic. &lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Analysis of Covid Denial&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;We sought to answer the following questions:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;How often is this content submitted?&lt;/li&gt;\n&lt;li&gt;What is the community reception?&lt;/li&gt;\n&lt;li&gt;Where are the concentration centers for this content?&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;Below is a chart of all of the COVID-related content that has been posted on the platform since January 1, 2020. We are using common keywords and known COVID focused communities to measure this. The volume has been relatively flat since mid last year, but since July (coinciding with the increased prevalence of the Delta variant), we have seen a sizable increase. &lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/5draikoecxk71.png?width=1145&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=4aa9a815e3b8637ad2c8f1a654b1b5f82cac4d97\"&gt;COVID Content Submissions&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;The trend is even more notable when we look at COVID-related content reported to us by users. Since August, we see approximately 2.5k reports/day vs an average of around 500 reports/day a year ago. This is approximately 2.5% of all COVID related content. &lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/v35d5wfgcxk71.png?width=1146&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=f6604d4fae70f04ba0cbf5beb3949aab5169fc99\"&gt;Reports on COVID Content&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;While this data alone does not tell us that COVID denial content on the platform is increasing, it is certainly an indicator. To help make this story more clear, we looked into potential networks of denial communities. There are some well known subreddits dedicated to discussing and challenging the policy response to COVID, and we used this as a basis to identify other similar subreddits. I’ll refer to these as “high signal subs.”&lt;/p&gt;\n\n&lt;p&gt;Last year, we saw that less than 1% of COVID content came from these high signal subs, today we see that it&amp;#39;s over 3%. COVID content in these communities is around 3x more likely to be reported than in other communities (this is fairly consistent over the last year). Together with information above we can infer that there has been an increase in COVID denial content on the platform, and that increase has been more pronounced since July. While the increase is suboptimal, it is noteworthy that the large majority of the content is outside of these COVID denial subreddits. It’s also hard to put an exact number on the increase or the overall volume. &lt;/p&gt;\n\n&lt;p&gt;An important part of our moderation structure is the community members themselves. How are users responding to COVID-related posts? How much visibility do they have? Is there a difference in the response in these high signal subs than the rest of Reddit?&lt;/p&gt;\n\n&lt;p&gt;High Signal Subs&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Content positively received - 48% on posts, 43% on comments&lt;/li&gt;\n&lt;li&gt;Median exposure - 119 viewers on posts, 100 viewers on comments&lt;/li&gt;\n&lt;li&gt;Median vote count - 21 on posts, 5 on comments&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;All Other Subs&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Content positively received - 27% on posts, 41% on comments&lt;/li&gt;\n&lt;li&gt;Median exposure - 24 viewers on posts, 100 viewers on comments&lt;/li&gt;\n&lt;li&gt;Median vote count - 10 on posts, 6 on comments&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;This tells us that in these high signal subs, there is generally less of the critical feedback mechanism than we would expect to see in other non-denial based subreddits, which leads to content in these communities being more visible than the typical COVID post in other subreddits. &lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Interference Analysis&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;In addition to this, we have also been investigating the claims around targeted interference by some of these subreddits. While we want to be a place where people can explore unpopular views, it is never acceptable to interfere with other communities. Claims of “brigading” are common and often hard to quantify. However, in this case, we found very clear signals indicating that &lt;a href=\"/r/NoNewNormal\"&gt;r/NoNewNormal&lt;/a&gt; was the source of around 80 brigades in the last 30 days (largely directed at communities with more mainstream views on COVID or location-based communities that have been discussing COVID restrictions). This behavior continued even after a warning was issued from our team to the Mods. &lt;a href=\"/r/NoNewNormal\"&gt;r/NoNewNormal&lt;/a&gt; is the only subreddit in our list of high signal subs where we have identified this behavior and it is one of the largest sources of community interference we surfaced as part of this work (we will be investigating a few other unrelated subreddits as well). &lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Analysis into Action&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;We are taking several actions:&lt;/p&gt;\n\n&lt;ol&gt;\n&lt;li&gt;Ban &lt;a href=\"/r/NoNewNormal\"&gt;r/NoNewNormal&lt;/a&gt; immediately for breaking our rules against brigading&lt;/li&gt;\n&lt;li&gt;Quarantine 54 additional COVID denial subreddits under &lt;a href=\"https://www.redditinc.com/policies/content-policy\"&gt;Rule 1&lt;/a&gt;&lt;/li&gt;\n&lt;li&gt;Build a new reporting feature for moderators to allow them to better provide us signal when they see community interference. It will take us a few days to get this built, and we will subsequently evaluate the usefulness of this feature. &lt;/li&gt;\n&lt;/ol&gt;\n\n&lt;p&gt;&lt;em&gt;Clarifying our Policies&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;We also hear the feedback that our policies are not clear around our handling of health misinformation. To address this, we wanted to provide a summary of our current approach to misinformation/disinformation in our &lt;a href=\"https://www.redditinc.com/policies/content-policy\"&gt;Content Policy&lt;/a&gt;. &lt;/p&gt;\n\n&lt;p&gt;Our approach is broken out into (1) how we deal with health misinformation (falsifiable health related information that is disseminated regardless of intent), (2) health disinformation (falsifiable health information that is disseminated with an intent to mislead), (3) problematic subreddits that pose misinformation risks, and (4) problematic users who invade other subreddits to “debate” topics unrelated to the wants/needs of that community. &lt;/p&gt;\n\n&lt;ol&gt;\n&lt;li&gt;&lt;p&gt;Health Misinformation. We have long interpreted our rule against posting content that “encourages” physical harm, in this &lt;a href=\"https://www.reddithelp.com/hc/en-us/articles/360043513151\"&gt;help center article&lt;/a&gt;, as covering health misinformation, meaning falsifiable health information that encourages or poses a significant risk of physical harm to the reader. For example, a post pushing a verifiably false “cure” for cancer that would actually result in harm to people would violate our policies.  &lt;/p&gt;&lt;/li&gt;\n&lt;li&gt;&lt;p&gt;Health Disinformation. Our rule against impersonation, as described in this &lt;a href=\"https://www.reddithelp.com/hc/en-us/articles/360043075032\"&gt;help center article&lt;/a&gt;, extends to “manipulated content presented to mislead.” We have interpreted this rule as covering health disinformation, meaning falsifiable health information that has been manipulated and presented to mislead. This includes falsified medical data and faked WHO/CDC advice.  &lt;/p&gt;&lt;/li&gt;\n&lt;li&gt;&lt;p&gt;Problematic subreddits.  We have long applied &lt;a href=\"https://www.reddithelp.com/hc/en-us/articles/360043069012\"&gt;quarantine&lt;/a&gt; to communities that warrant additional scrutiny. The purpose of quarantining a community is to prevent its content from being accidentally viewed or viewed without appropriate context. &lt;/p&gt;&lt;/li&gt;\n&lt;li&gt;&lt;p&gt;Community Interference. Also relevant to the discussion of the activities of problematic subreddits, &lt;a href=\"https://www.redditinc.com/policies/content-policy\"&gt;Rule 2&lt;/a&gt; forbids users or communities from “cheating” or engaging in “content manipulation” or otherwise interfering with or disrupting Reddit communities.  We have interpreted this rule as forbidding communities from manipulating the platform, creating inauthentic conversations, and picking fights with other communities. We typically enforce Rule 2 through our anti-brigading efforts, although it is still an example of bad behavior that has led to bans of a variety of subreddits.&lt;/p&gt;&lt;/li&gt;\n&lt;/ol&gt;\n\n&lt;p&gt;As I mentioned at the start, we never claim to be perfect at these things but our goal is to constantly evolve. These prevalence studies are helpful for evolving our thinking. We also need to evolve how we communicate our policy and enforcement decisions. As always, I will stick around to answer your questions and will also be joined by &lt;a href=\"/u/traceroo\"&gt;u/traceroo&lt;/a&gt; our GC and head of policy.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?auto=webp&amp;s=a6f7aad4b389e0fc0326529d5c942c1a96c671b5", "width": 1200, "height": 627}, "resolutions": [{"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=fad6502851946dfa4c06d16daeebd7302b49362d", "width": 108, "height": 56}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=66c9b46928c9d4d9030e475cbdd64f39bdc9ae97", "width": 216, "height": 112}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=8d9e52aa3e8020f804c3a53427514f962e7de0c5", "width": 320, "height": 167}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=04df43c21aa54b0e6b25dd7e3003bbcdea341546", "width": 640, "height": 334}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=145d991b603842f4d2507d013964490442172d0c", "width": 960, "height": 501}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=f0f7c7a2c8e56eadc134b1ee8753d9df6f5de495", "width": 1080, "height": 564}], "variants": {}, "id": "4GF2ZTGSM89ofXwffptE1cL1y5amFOzBf9NXtxD7N88"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_vm1db", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "pfyqqn", "is_robot_indexable": true, "report_reasons": null, "author": "worstnerd", "discussion_type": null, "num_comments": 15991, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/RedditSafety/comments/pfyqqn/covid_denialism_and_policy_clarifications/", "stickied": false, "url": "https://www.reddit.com/r/RedditSafety/comments/pfyqqn/covid_denialism_and_policy_clarifications/", "subreddit_subscribers": 40199, "created_utc": 1630517431.0, "num_crossposts": 209, "media": null, "is_video": false}], "created": 1630521123.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.redditsecurity", "allow_live_comments": true, "selftext_html": null, "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "url_overridden_by_dest": "/r/redditsecurity/comments/pfyqqn/covid_denialism_and_policy_clarifications/", "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?auto=webp&amp;s=a6f7aad4b389e0fc0326529d5c942c1a96c671b5", "width": 1200, "height": 627}, "resolutions": [{"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=fad6502851946dfa4c06d16daeebd7302b49362d", "width": 108, "height": 56}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=66c9b46928c9d4d9030e475cbdd64f39bdc9ae97", "width": 216, "height": 112}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=8d9e52aa3e8020f804c3a53427514f962e7de0c5", "width": 320, "height": 167}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=04df43c21aa54b0e6b25dd7e3003bbcdea341546", "width": 640, "height": 334}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=145d991b603842f4d2507d013964490442172d0c", "width": 960, "height": 501}, {"url": "https://external-preview.redd.it/0wKhfnBpPATUl3wOK_t9wixlJgdMEKwii0yTEdCvcgg.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=f0f7c7a2c8e56eadc134b1ee8753d9df6f5de495", "width": 1080, "height": 564}], "variants": {}, "id": "4GF2ZTGSM89ofXwffptE1cL1y5amFOzBf9NXtxD7N88"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "pg006s", "is_robot_indexable": true, "report_reasons": null, "author": "worstnerd", "discussion_type": null, "num_comments": 1, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "crosspost_parent": "t3_pfyqqn", "author_flair_text_color": null, "permalink": "/r/announcements/comments/pg006s/covid_denialism_and_policy_clarifications/", "stickied": false, "url": "/r/redditsecurity/comments/pfyqqn/covid_denialism_and_policy_clarifications/", "subreddit_subscribers": *********, "created_utc": 1630521123.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "Hey everyone–\n\nThe pandemic has been extremely hard on many of us. We believe the best way forward for everyone is to [get vaccinated](https://www.cdc.gov/coronavirus/2019-ncov/index.html) and continue to follow [CDC guidance on masking](https://www.cdc.gov/coronavirus/2019-ncov/prevent-getting-sick/about-face-coverings.html). Throughout the pandemic, we have also provided COVID-related resources to support our volunteer moderators, users, and communities, including a dedicated AMA series connecting users with [authoritative experts on coronavirus](https://redditblog.com/2020/03/02/expert-conversation-on-coronavirus/) and [vaccines](https://redditblog.com/2021/01/28/introducing-a-new-reddit-ama-series-everything-you-want-to-know-about-covid-19-vaccines/), as well as deploying homepage and search page banners directing users to the CDC and r/Coronavirus.\n\nWe appreciate that not everyone agrees with the current approach to getting us all through the pandemic, and some are still wary of vaccinations. Dissent is a part of Reddit and the foundation of democracy. Reddit is a place for open and authentic discussion and debate. This includes conversations that question or disagree with popular consensus. This includes conversations that criticize those that disagree with the majority opinion. This includes protests that criticize or object to our decisions on which communities to ban from the platform.\n\nWhile we appreciate the sentiment of those demanding that we ban more communities that challenge consensus views on the pandemic, we continue to believe in the good of our communities and hope that we collectively approach the challenges of the pandemic with empathy, compassion, and a willingness to understand what others are going through, even when their viewpoint on the pandemic is different from yours. \n\nWhen it comes to COVID-19 specifically, what we know and what are the current best practices from authoritative sources, like the CDC, evolve continuously with new learnings. Given the rapid state of change, we believe it is best to enable communities to engage in debate and dissent, and for us to link to the CDC wherever appropriate. While we believe the CDC is the best and most up to date source of information regarding COVID-19, disagreeing with them is not against our policies.\n\nHowever, manipulating or cheating Reddit to amplify any particular viewpoint is against our policies, and we will continue to action communities that do so or that violate any of our other rules, including those dedicated to fraud (e.g. fake vaccine cards) or encouraging harm (e.g. consuming bleach); and we will continue to use our quarantine tool to link to authoritative sources and warn people they may encounter unsound advice. We humbly ask and encourage everyone to report content that may violate our policies.\n\n\\*We are using our [political ads system](https://www.reddit.com/r/announcements/comments/ipitt0/today_were_testing_a_new_way_to_discuss_political/) to facilitate discussion of this post within the context of your own communities.", "author_fullname": "t2_1w72", "saved": false, "mod_reason_title": null, "gilded": 16, "clicked": false, "title": "Debate, dissent, and protest on Reddit", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_pbmy5y", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.34, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 0, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 0, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1629933302.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Hey everyone–&lt;/p&gt;\n\n&lt;p&gt;The pandemic has been extremely hard on many of us. We believe the best way forward for everyone is to &lt;a href=\"https://www.cdc.gov/coronavirus/2019-ncov/index.html\"&gt;get vaccinated&lt;/a&gt; and continue to follow &lt;a href=\"https://www.cdc.gov/coronavirus/2019-ncov/prevent-getting-sick/about-face-coverings.html\"&gt;CDC guidance on masking&lt;/a&gt;. Throughout the pandemic, we have also provided COVID-related resources to support our volunteer moderators, users, and communities, including a dedicated AMA series connecting users with &lt;a href=\"https://redditblog.com/2020/03/02/expert-conversation-on-coronavirus/\"&gt;authoritative experts on coronavirus&lt;/a&gt; and &lt;a href=\"https://redditblog.com/2021/01/28/introducing-a-new-reddit-ama-series-everything-you-want-to-know-about-covid-19-vaccines/\"&gt;vaccines&lt;/a&gt;, as well as deploying homepage and search page banners directing users to the CDC and &lt;a href=\"/r/Coronavirus\"&gt;r/Coronavirus&lt;/a&gt;.&lt;/p&gt;\n\n&lt;p&gt;We appreciate that not everyone agrees with the current approach to getting us all through the pandemic, and some are still wary of vaccinations. Dissent is a part of Reddit and the foundation of democracy. Reddit is a place for open and authentic discussion and debate. This includes conversations that question or disagree with popular consensus. This includes conversations that criticize those that disagree with the majority opinion. This includes protests that criticize or object to our decisions on which communities to ban from the platform.&lt;/p&gt;\n\n&lt;p&gt;While we appreciate the sentiment of those demanding that we ban more communities that challenge consensus views on the pandemic, we continue to believe in the good of our communities and hope that we collectively approach the challenges of the pandemic with empathy, compassion, and a willingness to understand what others are going through, even when their viewpoint on the pandemic is different from yours. &lt;/p&gt;\n\n&lt;p&gt;When it comes to COVID-19 specifically, what we know and what are the current best practices from authoritative sources, like the CDC, evolve continuously with new learnings. Given the rapid state of change, we believe it is best to enable communities to engage in debate and dissent, and for us to link to the CDC wherever appropriate. While we believe the CDC is the best and most up to date source of information regarding COVID-19, disagreeing with them is not against our policies.&lt;/p&gt;\n\n&lt;p&gt;However, manipulating or cheating Reddit to amplify any particular viewpoint is against our policies, and we will continue to action communities that do so or that violate any of our other rules, including those dedicated to fraud (e.g. fake vaccine cards) or encouraging harm (e.g. consuming bleach); and we will continue to use our quarantine tool to link to authoritative sources and warn people they may encounter unsound advice. We humbly ask and encourage everyone to report content that may violate our policies.&lt;/p&gt;\n\n&lt;p&gt;*We are using our &lt;a href=\"https://www.reddit.com/r/announcements/comments/ipitt0/today_were_testing_a_new_way_to_discuss_political/\"&gt;political ads system&lt;/a&gt; to facilitate discussion of this post within the context of your own communities.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/GLDCmgkhRu-V_AZtW1qmzmjXQVHmUtxrdcjgpfNMxiY.jpg?auto=webp&amp;s=2a417f75732a3097c0f8e34996a51315d16107ea", "width": 1200, "height": 675}, "resolutions": [{"url": "https://external-preview.redd.it/GLDCmgkhRu-V_AZtW1qmzmjXQVHmUtxrdcjgpfNMxiY.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=89c9caa8ef9dbe6a9bfa8e8f6f43dbb8cd5246dd", "width": 108, "height": 60}, {"url": "https://external-preview.redd.it/GLDCmgkhRu-V_AZtW1qmzmjXQVHmUtxrdcjgpfNMxiY.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=fd6ec4f1337ea84fdf241b0edbc157ad5390a0fb", "width": 216, "height": 121}, {"url": "https://external-preview.redd.it/GLDCmgkhRu-V_AZtW1qmzmjXQVHmUtxrdcjgpfNMxiY.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=aa3946b6d216ea711e059924f21c31368221c17f", "width": 320, "height": 180}, {"url": "https://external-preview.redd.it/GLDCmgkhRu-V_AZtW1qmzmjXQVHmUtxrdcjgpfNMxiY.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=284fceb840f2330c67515155a4006281ef776bb4", "width": 640, "height": 360}, {"url": "https://external-preview.redd.it/GLDCmgkhRu-V_AZtW1qmzmjXQVHmUtxrdcjgpfNMxiY.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=502fb02be6d2fadf86dbed5625890fe1956c1214", "width": 960, "height": 540}, {"url": "https://external-preview.redd.it/GLDCmgkhRu-V_AZtW1qmzmjXQVHmUtxrdcjgpfNMxiY.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=35497922002b90d470d1bf1f45a1148ce11a0a13", "width": 1080, "height": 607}], "variants": {}, "id": "1chpp4Zm7eqa-IHzAknRysFORBMmXXiMzjyZxmjBBZI"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "pbmy5y", "is_robot_indexable": true, "report_reasons": null, "author": "spez", "discussion_type": null, "num_comments": 1, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/pbmy5y/debate_dissent_and_protest_on_reddit/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/pbmy5y/debate_dissent_and_protest_on_reddit/", "subreddit_subscribers": *********, "created_utc": 1629933302.0, "num_crossposts": 155, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "Today is a difficult one:. 2021 will be the last year of Reddit Gifts.  We will continue to run exchanges through the end of the year -- including the last ever Arbitrary Day (signups are [now open](https://www.redditgifts.com/exchanges/a-day-21/)) -- and will end with Secret Santa 2021. \n\n**We didn’t make this decision lightly.** \n\nWe made the difficult decision to shut down Reddit Gifts and put more focus on enhancing the [user experience](https://www.reddit.com/r/blog/comments/mqcpcg/you_want_a_better_reddit_search_ok_were_on_it/) on Reddit - this includes [investing](https://www.reddit.com/r/blog/comments/ldgmfc/diamond_hands_on_the_data/) in the [foundation](https://www.reddit.com/r/RedditEng/comments/nudfv1/the_rollout_of_reputation_service/) [of our](https://www.reddit.com/r/blog/comments/lb2p7a/a_new_video_player_updated_email_designs_mobile/) [platform](https://www.reddit.com/r/RedditEng/comments/neknjg/evolving_beyond_100_billion_recommendations_a_day/) and [moderator tools](https://www.reddit.com/r/blog/comments/nk7p7j/new_updates_to_help_moderators_your_monthly/), making it more [accessible](https://www.reddit.com/r/blog/comments/mzse3p/control_over_your_followers_spring_avatar_gear_a/) for people around the world and evolving how people [engage](https://www.reddit.com/r/redditsecurity/comments/nmhmj0/q1_safety_security_report_may_27_2021/) with one another. \n\nThe power of Reddit Gifts was never in the software, and has always belonged to the r/secretsanta community of gifters around the world, which has connected people and been an extension of our mission to bring community and belonging to everyone in the world. We’re hopeful that spirit will continue in the future. \n\n**What this means for future exchanges in 2021**\n\nIn preparation for retiring Reddit Gifts after the final exchange at the end of 2021, we will be taking the following actions: \n\n* In order to limit incomplete exchanges, we have disabled the creation of any new Reddit Gifts accounts. If you have an existing Reddit Gifts account, we would love it if you would participate with us in these final exchanges. \n* Any incomplete exchanges will result in a ban from the remaining Reddit Gifts exchanges.\n* This morning, we turned off the ability to buy [Elves](https://www.redditgifts.com/Elves/). If you purchased an Elves membership and have remaining months after the 2021 Secret Santa Exchange, we will email you about your refund options then. If you have specific concerns about your Elves membership, please reach out to Reddit Gifts [support](https://reddit.zendesk.com/hc/en-us/requests/new?ticket_form_id=************). \n\nThese changes have been put in place to ensure that these last exchanges are enjoyable for the legacy Reddit Gifts users. We want to celebrate the end of Reddit Gifts with the community that we’ve built so far. \n\nCountless acts of love, heroism, compassion, support, growth and hilarity happened through Reddit Gifts, and those memories will live on in the hearts of our community. We’re working on ways to capture these moments and look forward to seeing how the spirit and connection of exchanging gifts with strangers will live on. I’m sure you will all have a ton of questions, and we will be here to answer them.", "author_fullname": "t2_1wjm", "saved": false, "mod_reason_title": null, "gilded": 2, "clicked": false, "title": "Sunsetting Secret Santa and Reddit Gifts", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_nw2hs6", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.28, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 0, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 0, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1623262631.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Today is a difficult one:. 2021 will be the last year of Reddit Gifts.  We will continue to run exchanges through the end of the year -- including the last ever Arbitrary Day (signups are &lt;a href=\"https://www.redditgifts.com/exchanges/a-day-21/\"&gt;now open&lt;/a&gt;) -- and will end with Secret Santa 2021. &lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;We didn’t make this decision lightly.&lt;/strong&gt; &lt;/p&gt;\n\n&lt;p&gt;We made the difficult decision to shut down Reddit Gifts and put more focus on enhancing the &lt;a href=\"https://www.reddit.com/r/blog/comments/mqcpcg/you_want_a_better_reddit_search_ok_were_on_it/\"&gt;user experience&lt;/a&gt; on Reddit - this includes &lt;a href=\"https://www.reddit.com/r/blog/comments/ldgmfc/diamond_hands_on_the_data/\"&gt;investing&lt;/a&gt; in the &lt;a href=\"https://www.reddit.com/r/RedditEng/comments/nudfv1/the_rollout_of_reputation_service/\"&gt;foundation&lt;/a&gt; &lt;a href=\"https://www.reddit.com/r/blog/comments/lb2p7a/a_new_video_player_updated_email_designs_mobile/\"&gt;of our&lt;/a&gt; &lt;a href=\"https://www.reddit.com/r/RedditEng/comments/neknjg/evolving_beyond_100_billion_recommendations_a_day/\"&gt;platform&lt;/a&gt; and &lt;a href=\"https://www.reddit.com/r/blog/comments/nk7p7j/new_updates_to_help_moderators_your_monthly/\"&gt;moderator tools&lt;/a&gt;, making it more &lt;a href=\"https://www.reddit.com/r/blog/comments/mzse3p/control_over_your_followers_spring_avatar_gear_a/\"&gt;accessible&lt;/a&gt; for people around the world and evolving how people &lt;a href=\"https://www.reddit.com/r/redditsecurity/comments/nmhmj0/q1_safety_security_report_may_27_2021/\"&gt;engage&lt;/a&gt; with one another. &lt;/p&gt;\n\n&lt;p&gt;The power of Reddit Gifts was never in the software, and has always belonged to the &lt;a href=\"/r/secretsanta\"&gt;r/secretsanta&lt;/a&gt; community of gifters around the world, which has connected people and been an extension of our mission to bring community and belonging to everyone in the world. We’re hopeful that spirit will continue in the future. &lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;What this means for future exchanges in 2021&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;In preparation for retiring Reddit Gifts after the final exchange at the end of 2021, we will be taking the following actions: &lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;In order to limit incomplete exchanges, we have disabled the creation of any new Reddit Gifts accounts. If you have an existing Reddit Gifts account, we would love it if you would participate with us in these final exchanges. &lt;/li&gt;\n&lt;li&gt;Any incomplete exchanges will result in a ban from the remaining Reddit Gifts exchanges.&lt;/li&gt;\n&lt;li&gt;This morning, we turned off the ability to buy &lt;a href=\"https://www.redditgifts.com/Elves/\"&gt;Elves&lt;/a&gt;. If you purchased an Elves membership and have remaining months after the 2021 Secret Santa Exchange, we will email you about your refund options then. If you have specific concerns about your Elves membership, please reach out to Reddit Gifts &lt;a href=\"https://reddit.zendesk.com/hc/en-us/requests/new?ticket_form_id=************\"&gt;support&lt;/a&gt;. &lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;These changes have been put in place to ensure that these last exchanges are enjoyable for the legacy Reddit Gifts users. We want to celebrate the end of Reddit Gifts with the community that we’ve built so far. &lt;/p&gt;\n\n&lt;p&gt;Countless acts of love, heroism, compassion, support, growth and hilarity happened through Reddit Gifts, and those memories will live on in the hearts of our community. We’re working on ways to capture these moments and look forward to seeing how the spirit and connection of exchanging gifts with strangers will live on. I’m sure you will all have a ton of questions, and we will be here to answer them.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/sdsFG2KUr9oFeHlJJlcOwgEHdr51rgSyhTOR3TCalJ0.jpg?auto=webp&amp;s=ac4106afa1ebcf3023698b4905e942e18434d80a", "width": 114, "height": 105}, "resolutions": [{"url": "https://external-preview.redd.it/sdsFG2KUr9oFeHlJJlcOwgEHdr51rgSyhTOR3TCalJ0.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=74e5407e4d9995e8cbb3b0da2a9acb6865d26434", "width": 108, "height": 99}], "variants": {}, "id": "Wg80f_8B21wXp5mEGCHO1tYGclDEpElnFrpzr2kH-5E"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "nw2hs6", "is_robot_indexable": true, "report_reasons": null, "author": "<PERSON><PERSON><PERSON><PERSON>", "discussion_type": null, "num_comments": 3427, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/nw2hs6/sunsetting_secret_santa_and_reddit_gifts/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/nw2hs6/sunsetting_secret_santa_and_reddit_gifts/", "subreddit_subscribers": *********, "created_utc": 1623262631.0, "num_crossposts": 32, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "Picking a winner is easy.\n\nBut what if second was first instead,\n\nAnd we celebrated the second guessers?\n\n--\n\nTo play, visit r/Second on [iOS](https://www.reddit.com/mobile/download), [Android](https://www.reddit.com/mobile/download), or your [browser](http://new.reddit.com/r/second),", "author_fullname": "t2_5wqa4", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Second", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_mi01fg", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.24, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 0, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 0, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1617297252.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Picking a winner is easy.&lt;/p&gt;\n\n&lt;p&gt;But what if second was first instead,&lt;/p&gt;\n\n&lt;p&gt;And we celebrated the second guessers?&lt;/p&gt;\n\n&lt;h2&gt;&lt;/h2&gt;\n\n&lt;p&gt;To play, visit &lt;a href=\"/r/Second\"&gt;r/Second&lt;/a&gt; on &lt;a href=\"https://www.reddit.com/mobile/download\"&gt;iOS&lt;/a&gt;, &lt;a href=\"https://www.reddit.com/mobile/download\"&gt;Android&lt;/a&gt;, or your &lt;a href=\"http://new.reddit.com/r/second\"&gt;browser&lt;/a&gt;,&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "mi01fg", "is_robot_indexable": true, "report_reasons": null, "author": "powerlanguage", "discussion_type": null, "num_comments": 1, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/mi01fg/second/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/mi01fg/second/", "subreddit_subscribers": *********, "created_utc": 1617297252.0, "num_crossposts": 21, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "We would like to give you all an update on the recent issues that have transpired concerning a specific Reddit employee, as well as provide you with context into actions that we took to prevent doxxing and harassment.\n\nAs of today, the employee in question is no longer employed by Reddit. We built a relationship with her first as a mod and then through her contractor work on RPAN. We did not adequately vet her background before formally hiring her. \n\nWe’ve put significant effort into improving how we handle doxxing and harassment, and this employee was the subject of both. In this case, we over-indexed on protection, which had serious consequences in terms of enforcement actions. \n\n* On March 9th, we added extra protections for this employee, including actioning content that mentioned the employee’s name or shared personal information on third-party sites, which we reserve for serious cases of harassment and doxxing.\n* On March 22nd, a news article about this employee was posted by a mod of r/ukpolitics. The article was removed and the submitter banned by the aforementioned rules. When contacted by the moderators of r/ukpolitics, we reviewed the actions, and reversed the ban on the moderator, and we informed the r/ukpolitics moderation team that we had restored the mod.\n* We updated our rules to flag potential harassment for human review.\n\nDebate and criticism have always been and always will be central to conversation on Reddit—including discussion about public figures and Reddit itself—as long as they are not used as vehicles for harassment. Mentioning a public figure’s name should not get you banned.\n\nWe care deeply for Reddit and appreciate that you do too. We understand the anger and confusion about these issues and their bigger implications. The employee is no longer with Reddit, and we’ll be evolving a number of relevant internal policies.\n\nWe did not operate to our own standards here. We will do our best to do better for you.", "author_fullname": "t2_1w72", "saved": false, "mod_reason_title": null, "gilded": 10, "clicked": false, "title": "An update on the recent issues surrounding a Reddit employee", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_mcisdf", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.63, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 107364, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 107364, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1616625467.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;We would like to give you all an update on the recent issues that have transpired concerning a specific Reddit employee, as well as provide you with context into actions that we took to prevent doxxing and harassment.&lt;/p&gt;\n\n&lt;p&gt;As of today, the employee in question is no longer employed by Reddit. We built a relationship with her first as a mod and then through her contractor work on RPAN. We did not adequately vet her background before formally hiring her. &lt;/p&gt;\n\n&lt;p&gt;We’ve put significant effort into improving how we handle doxxing and harassment, and this employee was the subject of both. In this case, we over-indexed on protection, which had serious consequences in terms of enforcement actions. &lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;On March 9th, we added extra protections for this employee, including actioning content that mentioned the employee’s name or shared personal information on third-party sites, which we reserve for serious cases of harassment and doxxing.&lt;/li&gt;\n&lt;li&gt;On March 22nd, a news article about this employee was posted by a mod of &lt;a href=\"/r/ukpolitics\"&gt;r/ukpolitics&lt;/a&gt;. The article was removed and the submitter banned by the aforementioned rules. When contacted by the moderators of &lt;a href=\"/r/ukpolitics\"&gt;r/ukpolitics&lt;/a&gt;, we reviewed the actions, and reversed the ban on the moderator, and we informed the &lt;a href=\"/r/ukpolitics\"&gt;r/ukpolitics&lt;/a&gt; moderation team that we had restored the mod.&lt;/li&gt;\n&lt;li&gt;We updated our rules to flag potential harassment for human review.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;Debate and criticism have always been and always will be central to conversation on Reddit—including discussion about public figures and Reddit itself—as long as they are not used as vehicles for harassment. Mentioning a public figure’s name should not get you banned.&lt;/p&gt;\n\n&lt;p&gt;We care deeply for Reddit and appreciate that you do too. We understand the anger and confusion about these issues and their bigger implications. The employee is no longer with Reddit, and we’ll be evolving a number of relevant internal policies.&lt;/p&gt;\n\n&lt;p&gt;We did not operate to our own standards here. We will do our best to do better for you.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "mcisdf", "is_robot_indexable": true, "report_reasons": null, "author": "spez", "discussion_type": null, "num_comments": 35831, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/mcisdf/an_update_on_the_recent_issues_surrounding_a/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/mcisdf/an_update_on_the_recent_issues_surrounding_a/", "subreddit_subscribers": *********, "created_utc": 1616625467.0, "num_crossposts": 147, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "In case you missed [the](https://www.reddit.com/r/mildlyinteresting/comments/ikxsst/this_reddit_billboard_advertisement_for_their/) [billboards](https://www.reddit.com/r/pics/comments/ilhck2/saw_one_in_the_wild_make_sure_to_vote_illinois/), [blog posts](https://redditblog.com/2020/09/01/its-national-poll-worker-recruitment-day-become-a-poll-worker-this-election-and-help-power-the-polls-in-your-community/), and [AMAs](https://redditblog.com/2020/08/25/voting-in-america/), we’re doing our best to encourage people on and off Reddit to vote this year. Along with our [Up the Vote campaign](https://redditblog.com/2020/07/29/up-the-vote-reddits-irl-2020-voting-campaign/) and [ongoing security and safety work](https://www.reddit.com/r/redditsecurity/) to guard against shenanigans, we’ve also been evolving how we handle another important aspect of the election process: **political ads**.\n\n## First, some background\n\nPolitical ads have been a relatively quiet part of Reddit for many years. Last year, in thinking through what the right balance was between unfettered political ads and prohibiting them altogether for 2020 (both approaches that other platforms have taken), we decided on [a policy we felt was the best approach for Reddit](https://www.reddit.com/r/announcements/comments/g0s6tn/changes_to_reddits_political_ads_policy/): no misinformation, human review of the ads and where they link, a subreddit listing all political ads ([r/RedditPoliticalAds](https://www.reddit.com/r/RedditPoliticalAds/)), and a requirement to keep comments on for 24 hours.\n\nSince debuting this policy earlier this year, the last condition (requiring comments to remain on) has enabled redditors to discuss political ads—providing more context in the comments and even offering counterarguments—but so far it’s only been lightly used. As we get closer to November, however, the prominence of and discussion around political ads will increase, and, with it, the need for a clear moderation system for these comments.\n\n## The problem we’re addressing\n\nAs I mentioned [a couple months back](https://www.reddit.com/r/announcements/comments/hi3oht/update_to_our_content_policy/fwdxoty/?utm_source=reddit&amp;utm_medium=web2x&amp;context=3&amp;depth=2), unmoderated spaces on Reddit are an area we want to improve, from Modmail to PM’s, and political ads pose a unique challenge.\n\nIf the OP of a political ad (i.e., a campaign) moderates the comments, it’s problematic: they might remove dissenting perspectives. And if we (the admins) moderate the comments of a political ad, it’s even more problematic, putting us in the position of either moderating too much or too little, with inevitable accusations of bias either way.\n\nThe problem, we realized, is similar to what we see in r/announcements: lots of people commenting on a highly visible post outside the context of a community. It’s fair to say that r/announcements isn’t really a community; it lacks the culture, cohesion, and moderation that benefit most other subreddits, and as a result, the quality of conversation has deteriorated as the audience has grown.\n\nUltimately, conversations really only happen in the context of a community, and neither r/announcements nor political ads with comments on provide this. We believe we can foster better discussion on both with a different approach.\n\n## What we’re testing today\n\nInstead of having the usual free-for-all of comments on the r/announcements post itself, we are trying out a new experience today that encourages discussion of this post within other communities—an approach we hope works for political ads as well.\n\nBelow is a stickied comment with a link to submit this post to other communities on Reddit and a list of those discussion posts. The stickied comment will update automatically with new posts.\n\nA few other details to note for this test:\n\n* **The discussion posts are like any other post**, which means they can be voted on by users and removed by mods.\n* **Communities that don’t want to participate don’t have to.** (If you’re a mod of a community where a user attempts to crosspost this, you will get a Modmail alerting you to this with [opt-out instructions](https://www.reddit.com/r/modnews/comments/ilc4zc/testing_a_new_admin_post_type/).)\n* **Individual community rules apply to these posts just as any other**, so read the rules before attempting to bring the discussion into a completely unrelated community.\n* **Our stickied comment will link to discussions only from communities subject to our ads allow list.** Communities that have already opted not to appear in r/all won’t appear in the comment either, even if there is a discussion of this post there.\n* **After today’s test, we will likely test this system with political ads.**\n\nThis test will be a success if there are a variety of posts and conversations about this post, even—and perhaps particularly—if they are critical.\n\n## How we’re answering questions\n\nr/announcements posts have an important difference from political ads: I treat them as AMAs and do my best to answer questions and respond to criticism (both of which I appreciate). With this approach, I expect doing so will be more difficult (at least this first time). However, the point of this test is not to make you hunt for our answers or for us to reply to fewer questions, and we don’t intend to use this approach for all our admin posts (e.g., in r/ModNews, r/changelog, r/ModSupport, and others, which are smaller subreddits that still work well).\n\nFor today, we’re going to make the first link of this post to r/ModNews and start by answering mods’ questions there. In a future announcement, we may ask a specific community if they would host us for the discussion that day (depending on what the announcement is) and set that as an official destination for discussion, like a regular AMA.\n\nAdditionally, I’ll do my best to find other places to respond, and we’ll maintain another comment below this post to list replies we’ve given outside of r/announcements so you can easily find our responses (which was a feature request from [our post](https://www.reddit.com/r/modnews/comments/ilc4zc/testing_a_new_admin_post_type/g3r4xpm/) last week).\n\nUltimately, the goal of this test is to enable Reddit to do what Reddit does best: facilitate conversations (within the context of a community), provide commentary on political ads the way redditors already do on news and politics daily (sharing more information and calling bullshit in the comments when necessary), and extend the discussion well beyond the scope of the original post.\n\nThank you for participating. We hope this approach sets the stage for successful commentary not just today but down the road on political ads as well.\n\nSteve\n\n&amp;#x200B;\n\n**Edit (9/28/20):** Update: After initial testing and a few small tweaks to the sorting and score display of the links in stickied comments like the one below, we’ll be using this discussion system on political ads moving forward. \n\nAs I mentioned, our goal with this approach was to encourage these types of discussions to happen within the context of a community. While this feature is completely optional for communities (opt-out instructions [here](https://www.reddit.com/r/modnews/comments/ilc4zc/testing_a_new_admin_post_type/)), we were pleased overall with the level of engagement that we saw from communities and users on this test post.\n\nWe’re still exploring how we’ll use this feature for r/announcements posts and how we can work with specific communities to have discussions about them. In the meantime, you can see our updated political ads policy on our [Advertising Policy Help Page](https://advertising.reddithelp.com/en/categories/reddit-advertising-policy/reddit-advertising-policy-overview).", "author_fullname": "t2_1w72", "saved": false, "mod_reason_title": null, "gilded": 3, "clicked": false, "title": "Today we’re testing a new way to discuss political ads (and announcements)", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_ipitt0", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.6, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 17311, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 17311, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": 1601325504.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1599667576.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;In case you missed &lt;a href=\"https://www.reddit.com/r/mildlyinteresting/comments/ikxsst/this_reddit_billboard_advertisement_for_their/\"&gt;the&lt;/a&gt; &lt;a href=\"https://www.reddit.com/r/pics/comments/ilhck2/saw_one_in_the_wild_make_sure_to_vote_illinois/\"&gt;billboards&lt;/a&gt;, &lt;a href=\"https://redditblog.com/2020/09/01/its-national-poll-worker-recruitment-day-become-a-poll-worker-this-election-and-help-power-the-polls-in-your-community/\"&gt;blog posts&lt;/a&gt;, and &lt;a href=\"https://redditblog.com/2020/08/25/voting-in-america/\"&gt;AMAs&lt;/a&gt;, we’re doing our best to encourage people on and off Reddit to vote this year. Along with our &lt;a href=\"https://redditblog.com/2020/07/29/up-the-vote-reddits-irl-2020-voting-campaign/\"&gt;Up the Vote campaign&lt;/a&gt; and &lt;a href=\"https://www.reddit.com/r/redditsecurity/\"&gt;ongoing security and safety work&lt;/a&gt; to guard against shenanigans, we’ve also been evolving how we handle another important aspect of the election process: &lt;strong&gt;political ads&lt;/strong&gt;.&lt;/p&gt;\n\n&lt;h2&gt;First, some background&lt;/h2&gt;\n\n&lt;p&gt;Political ads have been a relatively quiet part of Reddit for many years. Last year, in thinking through what the right balance was between unfettered political ads and prohibiting them altogether for 2020 (both approaches that other platforms have taken), we decided on &lt;a href=\"https://www.reddit.com/r/announcements/comments/g0s6tn/changes_to_reddits_political_ads_policy/\"&gt;a policy we felt was the best approach for Reddit&lt;/a&gt;: no misinformation, human review of the ads and where they link, a subreddit listing all political ads (&lt;a href=\"https://www.reddit.com/r/RedditPoliticalAds/\"&gt;r/RedditPoliticalAds&lt;/a&gt;), and a requirement to keep comments on for 24 hours.&lt;/p&gt;\n\n&lt;p&gt;Since debuting this policy earlier this year, the last condition (requiring comments to remain on) has enabled redditors to discuss political ads—providing more context in the comments and even offering counterarguments—but so far it’s only been lightly used. As we get closer to November, however, the prominence of and discussion around political ads will increase, and, with it, the need for a clear moderation system for these comments.&lt;/p&gt;\n\n&lt;h2&gt;The problem we’re addressing&lt;/h2&gt;\n\n&lt;p&gt;As I mentioned &lt;a href=\"https://www.reddit.com/r/announcements/comments/hi3oht/update_to_our_content_policy/fwdxoty/?utm_source=reddit&amp;amp;utm_medium=web2x&amp;amp;context=3&amp;amp;depth=2\"&gt;a couple months back&lt;/a&gt;, unmoderated spaces on Reddit are an area we want to improve, from Modmail to PM’s, and political ads pose a unique challenge.&lt;/p&gt;\n\n&lt;p&gt;If the OP of a political ad (i.e., a campaign) moderates the comments, it’s problematic: they might remove dissenting perspectives. And if we (the admins) moderate the comments of a political ad, it’s even more problematic, putting us in the position of either moderating too much or too little, with inevitable accusations of bias either way.&lt;/p&gt;\n\n&lt;p&gt;The problem, we realized, is similar to what we see in &lt;a href=\"/r/announcements\"&gt;r/announcements&lt;/a&gt;: lots of people commenting on a highly visible post outside the context of a community. It’s fair to say that &lt;a href=\"/r/announcements\"&gt;r/announcements&lt;/a&gt; isn’t really a community; it lacks the culture, cohesion, and moderation that benefit most other subreddits, and as a result, the quality of conversation has deteriorated as the audience has grown.&lt;/p&gt;\n\n&lt;p&gt;Ultimately, conversations really only happen in the context of a community, and neither &lt;a href=\"/r/announcements\"&gt;r/announcements&lt;/a&gt; nor political ads with comments on provide this. We believe we can foster better discussion on both with a different approach.&lt;/p&gt;\n\n&lt;h2&gt;What we’re testing today&lt;/h2&gt;\n\n&lt;p&gt;Instead of having the usual free-for-all of comments on the &lt;a href=\"/r/announcements\"&gt;r/announcements&lt;/a&gt; post itself, we are trying out a new experience today that encourages discussion of this post within other communities—an approach we hope works for political ads as well.&lt;/p&gt;\n\n&lt;p&gt;Below is a stickied comment with a link to submit this post to other communities on Reddit and a list of those discussion posts. The stickied comment will update automatically with new posts.&lt;/p&gt;\n\n&lt;p&gt;A few other details to note for this test:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;&lt;strong&gt;The discussion posts are like any other post&lt;/strong&gt;, which means they can be voted on by users and removed by mods.&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Communities that don’t want to participate don’t have to.&lt;/strong&gt; (If you’re a mod of a community where a user attempts to crosspost this, you will get a Modmail alerting you to this with &lt;a href=\"https://www.reddit.com/r/modnews/comments/ilc4zc/testing_a_new_admin_post_type/\"&gt;opt-out instructions&lt;/a&gt;.)&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Individual community rules apply to these posts just as any other&lt;/strong&gt;, so read the rules before attempting to bring the discussion into a completely unrelated community.&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Our stickied comment will link to discussions only from communities subject to our ads allow list.&lt;/strong&gt; Communities that have already opted not to appear in &lt;a href=\"/r/all\"&gt;r/all&lt;/a&gt; won’t appear in the comment either, even if there is a discussion of this post there.&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;After today’s test, we will likely test this system with political ads.&lt;/strong&gt;&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;This test will be a success if there are a variety of posts and conversations about this post, even—and perhaps particularly—if they are critical.&lt;/p&gt;\n\n&lt;h2&gt;How we’re answering questions&lt;/h2&gt;\n\n&lt;p&gt;&lt;a href=\"/r/announcements\"&gt;r/announcements&lt;/a&gt; posts have an important difference from political ads: I treat them as AMAs and do my best to answer questions and respond to criticism (both of which I appreciate). With this approach, I expect doing so will be more difficult (at least this first time). However, the point of this test is not to make you hunt for our answers or for us to reply to fewer questions, and we don’t intend to use this approach for all our admin posts (e.g., in &lt;a href=\"/r/ModNews\"&gt;r/ModNews&lt;/a&gt;, &lt;a href=\"/r/changelog\"&gt;r/changelog&lt;/a&gt;, &lt;a href=\"/r/ModSupport\"&gt;r/ModSupport&lt;/a&gt;, and others, which are smaller subreddits that still work well).&lt;/p&gt;\n\n&lt;p&gt;For today, we’re going to make the first link of this post to &lt;a href=\"/r/ModNews\"&gt;r/ModNews&lt;/a&gt; and start by answering mods’ questions there. In a future announcement, we may ask a specific community if they would host us for the discussion that day (depending on what the announcement is) and set that as an official destination for discussion, like a regular AMA.&lt;/p&gt;\n\n&lt;p&gt;Additionally, I’ll do my best to find other places to respond, and we’ll maintain another comment below this post to list replies we’ve given outside of &lt;a href=\"/r/announcements\"&gt;r/announcements&lt;/a&gt; so you can easily find our responses (which was a feature request from &lt;a href=\"https://www.reddit.com/r/modnews/comments/ilc4zc/testing_a_new_admin_post_type/g3r4xpm/\"&gt;our post&lt;/a&gt; last week).&lt;/p&gt;\n\n&lt;p&gt;Ultimately, the goal of this test is to enable Reddit to do what Reddit does best: facilitate conversations (within the context of a community), provide commentary on political ads the way redditors already do on news and politics daily (sharing more information and calling bullshit in the comments when necessary), and extend the discussion well beyond the scope of the original post.&lt;/p&gt;\n\n&lt;p&gt;Thank you for participating. We hope this approach sets the stage for successful commentary not just today but down the road on political ads as well.&lt;/p&gt;\n\n&lt;p&gt;Steve&lt;/p&gt;\n\n&lt;p&gt;&amp;#x200B;&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Edit (9/28/20):&lt;/strong&gt; Update: After initial testing and a few small tweaks to the sorting and score display of the links in stickied comments like the one below, we’ll be using this discussion system on political ads moving forward. &lt;/p&gt;\n\n&lt;p&gt;As I mentioned, our goal with this approach was to encourage these types of discussions to happen within the context of a community. While this feature is completely optional for communities (opt-out instructions &lt;a href=\"https://www.reddit.com/r/modnews/comments/ilc4zc/testing_a_new_admin_post_type/\"&gt;here&lt;/a&gt;), we were pleased overall with the level of engagement that we saw from communities and users on this test post.&lt;/p&gt;\n\n&lt;p&gt;We’re still exploring how we’ll use this feature for &lt;a href=\"/r/announcements\"&gt;r/announcements&lt;/a&gt; posts and how we can work with specific communities to have discussions about them. In the meantime, you can see our updated political ads policy on our &lt;a href=\"https://advertising.reddithelp.com/en/categories/reddit-advertising-policy/reddit-advertising-policy-overview\"&gt;Advertising Policy Help Page&lt;/a&gt;.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/yUVJES7lfSU0eoyc_-DTP-D3a-Y0KuIey4dJporC-sg.jpg?auto=webp&amp;s=d65b6caeaabed4c23bc7dddf7d92bc88e26963e8", "width": 600, "height": 315}, "resolutions": [{"url": "https://external-preview.redd.it/yUVJES7lfSU0eoyc_-DTP-D3a-Y0KuIey4dJporC-sg.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=480b349c3a9c3a1951654289dab4204922059b8c", "width": 108, "height": 56}, {"url": "https://external-preview.redd.it/yUVJES7lfSU0eoyc_-DTP-D3a-Y0KuIey4dJporC-sg.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=ea160607d5a877151036b4cd4467277663356803", "width": 216, "height": 113}, {"url": "https://external-preview.redd.it/yUVJES7lfSU0eoyc_-DTP-D3a-Y0KuIey4dJporC-sg.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=580fba8a46527044565ff0c3a019e49b4fa09271", "width": 320, "height": 168}], "variants": {}, "id": "rPXqOxQmd_KGI3L_9bW6tC-XHM-5s7QVVyDiz1mdcas"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "ipitt0", "is_robot_indexable": true, "report_reasons": null, "author": "spez", "discussion_type": null, "num_comments": 2, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/ipitt0/today_were_testing_a_new_way_to_discuss_political/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/ipitt0/today_were_testing_a_new_way_to_discuss_political/", "subreddit_subscribers": *********, "created_utc": 1599667576.0, "num_crossposts": 83, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "", "author_fullname": "t2_o30pnrf", "saved": false, "mod_reason_title": null, "gilded": 2, "clicked": false, "is_gallery": true, "title": "Now you can make posts with multiple images.", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "media_metadata": {"fs12v3j0z1b51": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 108, "x": 108, "u": "https://preview.redd.it/fs12v3j0z1b51.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=3f70354f72f64fd75b63234ba5f0c928efec56de"}, {"y": 216, "x": 216, "u": "https://preview.redd.it/fs12v3j0z1b51.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=d262799d1b80039ec6ae46a7a825039a6e1d7abd"}, {"y": 320, "x": 320, "u": "https://preview.redd.it/fs12v3j0z1b51.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=db3212c84bc4435978e5d03a1480e3e62d95e36d"}], "s": {"y": 512, "x": 512, "u": "https://preview.redd.it/fs12v3j0z1b51.png?width=512&amp;format=png&amp;auto=webp&amp;s=d636dcb2029782ddd7084e2628582379c817a985"}, "id": "fs12v3j0z1b51"}, "xl7jo6j0z1b51": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 108, "x": 108, "u": "https://preview.redd.it/xl7jo6j0z1b51.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=6209252f3e7b56293100b546c4483711cc65f4ba"}, {"y": 216, "x": 216, "u": "https://preview.redd.it/xl7jo6j0z1b51.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=492d9b5eb4e757af189328a96268bc4c11aa8d64"}, {"y": 320, "x": 320, "u": "https://preview.redd.it/xl7jo6j0z1b51.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=e0fa5bfbbeb4afd045ee5c7efef3387bff77910c"}], "s": {"y": 512, "x": 512, "u": "https://preview.redd.it/xl7jo6j0z1b51.png?width=512&amp;format=png&amp;auto=webp&amp;s=1be06611ba66b3417a86bfd70bdbed4f604f7b46"}, "id": "xl7jo6j0z1b51"}, "mbr8q5j0z1b51": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 108, "x": 108, "u": "https://preview.redd.it/mbr8q5j0z1b51.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=1154b4dd31f95134f5952d30cbd5ace23ea19515"}, {"y": 216, "x": 216, "u": "https://preview.redd.it/mbr8q5j0z1b51.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=a451a2460d34b18c5993dedd57602385df91552e"}, {"y": 320, "x": 320, "u": "https://preview.redd.it/mbr8q5j0z1b51.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=b6694749981bc9a5394e40fd88a7cca884ba4acc"}], "s": {"y": 512, "x": 512, "u": "https://preview.redd.it/mbr8q5j0z1b51.png?width=512&amp;format=png&amp;auto=webp&amp;s=58d37a6ce564d565d263c3e83f10c360e5e020d3"}, "id": "mbr8q5j0z1b51"}}, "name": "t3_hrrh23", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.88, "author_flair_background_color": null, "ups": 87260, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "gallery_data": {"items": [{"caption": "And by 'multiple' we mean 'up to 20.'", "media_id": "fs12v3j0z1b51", "id": 1340}, {"caption": "Just like this one!", "media_id": "mbr8q5j0z1b51", "id": 1341}, {"caption": "Perfect for gentlepersons of culture.", "outbound_url": "https://redditblog.com/2020/07/15/introducing-reddit-image-galleries-now-redditors-can-share-multiple-images-and-gifs-in-one-post/", "media_id": "xl7jo6j0z1b51", "id": 1342}]}, "link_flair_text": null, "can_mod_post": false, "score": 87260, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "default", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "subreddit_type": "archived", "created": 1594832829.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "reddit.com", "allow_live_comments": true, "selftext_html": null, "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "url_overridden_by_dest": "https://www.reddit.com/gallery/hrrh23", "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "mod_note": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "num_reports": null, "removal_reason": null, "link_flair_background_color": "", "id": "hrrh23", "is_robot_indexable": true, "report_reasons": null, "author": "LanterneRougeOG", "discussion_type": null, "num_comments": 4052, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/hrrh23/now_you_can_make_posts_with_multiple_images/", "stickied": false, "url": "https://www.reddit.com/gallery/hrrh23", "subreddit_subscribers": *********, "created_utc": 1594832829.0, "num_crossposts": 131, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "[A few weeks ago](https://www.reddit.com/r/announcements/comments/gxas21/upcoming_changes_to_our_content_policy_our_board/), we committed to closing the gap between our values and our policies to explicitly address hate. After talking extensively with mods, outside organizations, and our own teams, we’re updating our content policy today and enforcing it (with your help).\n\n## First, a quick recap\n\nSince our last post, here’s what we’ve been doing:\n\n* **We brought on a** [**new Board member**](https://redditblog.com/2020/06/10/reddit-welcomes-michael-seibel-to-board-of-directors/).\n* **We held policy calls with mods**—both from established Mod Councils and from communities disproportionately targeted with hate—and discussed areas where we can do better to action bad actors, clarify our policies, make mods' lives easier, and concretely reduce hate.\n   * You can find [detailed notes from our All-Council mod call here](https://www.reddit.com/r/modnews/comments/hi3nkr/the_mod_conversations_that_went_into_todays), including specific product work we discussed.\n* **We developed our enforcement plan**, including both our immediate actions (e.g., today’s bans) and long-term investments (tackling the most critical work discussed in our mod calls, sustainably enforcing the new policies, and advancing Reddit’s community governance).\n\nFrom our conversations with mods and outside experts, it’s clear that while we’ve gotten better in some areas—like actioning violations at the community level, [scaling enforcement efforts](https://www.reddit.com/r/redditsecurity/comments/hbiuas/reddit_security_report_june_18_2020/), measurably reducing hateful experiences like harassment [year over year](https://www.adl.org/online-hate-2020#results)—we still have a long way to go to address the gaps in our policies and enforcement to date.\n\nThese include addressing questions our policies have left unanswered (like whether hate speech is allowed or even protected on Reddit), aspects of our product and mod tools that are still too easy for individual bad actors to abuse (inboxes, chats, modmail), and areas where we can do better to partner with our mods and communities who want to combat the same hateful conduct we do.\n\nUltimately, it’s our responsibility to support our communities by taking stronger action against those who try to weaponize parts of Reddit against other people. In the near term, this support will translate into some of the product work we discussed with mods. But it starts with dealing squarely with the hate we can mitigate today through our policies and enforcement.\n\n## New Policy\n\nThis is the [new content policy](https://www.redditinc.com/policies/content-policy). Here’s what’s different:\n\n* It starts with a statement of our vision for Reddit and our communities, including the basic expectations we have for all communities and users.\n* **Rule 1** explicitly states that communities and users that promote hate based on identity or vulnerability will be banned.\n   * There is an expanded definition of what constitutes a violation of this rule, along with specific examples, in our [Help Center article](https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/promoting-hate-based-identity-or).\n* **Rule 2** ties together our previous rules on prohibited behavior with an ask to abide by community rules and post with authentic, personal interest.\n   * Debate and creativity are welcome, but spam and malicious attempts to interfere with other communities are not.\n* The other rules are the same in spirit but have been rewritten for clarity and inclusiveness.\n\nAlongside the change to the content policy, we are initially banning about 2000 subreddits, the vast majority of which are inactive. Of these communities, about [200](https://www.redditstatic.com/banned-subreddits-june-2020.txt) have more than 10 daily users. Both r/The_Donald and r/ChapoTrapHouse were included.\n\nAll communities on Reddit must abide by our content policy in good faith. We banned r/The_Donald because it has not done so, despite every opportunity. The community has consistently hosted and upvoted more rule-breaking content than average (Rule 1), antagonized us and other communities (Rules 2 and 8), and its mods have refused to meet our most basic expectations. Until now, we’ve worked in good faith to help them preserve the community as a space for its users—through warnings, mod changes, quarantining, and more.\n\nThough smaller, r/ChapoTrapHouse was banned for similar reasons: They consistently host rule-breaking content and their mods have demonstrated no intention of reining in their community.\n\nTo be clear, views across the political spectrum are allowed on Reddit—but all communities must work within our policies and do so in good faith, without exception.\n\n## Our commitment\n\nOur policies will never be perfect, with new edge cases that inevitably lead us to evolve them in the future. And as users, you will always have more context, community vernacular, and cultural values to inform the standards set within your communities than we as site admins or any AI ever could.\n\nBut just as our content moderation cannot scale effectively without your support, you need more support from us as well, and we admit we have fallen short towards this end. We are committed to working with you to combat the bad actors, abusive behaviors, and toxic communities that undermine our mission and get in the way of the creativity, discussions, and communities that bring us all to Reddit in the first place. We hope that our progress towards this commitment, with today’s update and those to come, makes Reddit a place you enjoy and are proud to be a part of for many years to come.\n\nEdit: After digesting feedback, we made a clarifying change to our help center article for [Promoting Hate Based on Identity or Vulnerability](https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/promoting-hate-based-identity-or).", "author_fullname": "t2_1w72", "saved": false, "mod_reason_title": null, "gilded": 10, "clicked": false, "title": "Update to Our Content Policy", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_hi3oht", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.5, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 21311, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 21311, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": 1593565818.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1593450006.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;&lt;a href=\"https://www.reddit.com/r/announcements/comments/gxas21/upcoming_changes_to_our_content_policy_our_board/\"&gt;A few weeks ago&lt;/a&gt;, we committed to closing the gap between our values and our policies to explicitly address hate. After talking extensively with mods, outside organizations, and our own teams, we’re updating our content policy today and enforcing it (with your help).&lt;/p&gt;\n\n&lt;h2&gt;First, a quick recap&lt;/h2&gt;\n\n&lt;p&gt;Since our last post, here’s what we’ve been doing:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;&lt;strong&gt;We brought on a&lt;/strong&gt; &lt;a href=\"https://redditblog.com/2020/06/10/reddit-welcomes-michael-seibel-to-board-of-directors/\"&gt;&lt;strong&gt;new Board member&lt;/strong&gt;&lt;/a&gt;.&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;We held policy calls with mods&lt;/strong&gt;—both from established Mod Councils and from communities disproportionately targeted with hate—and discussed areas where we can do better to action bad actors, clarify our policies, make mods&amp;#39; lives easier, and concretely reduce hate.\n\n&lt;ul&gt;\n&lt;li&gt;You can find &lt;a href=\"https://www.reddit.com/r/modnews/comments/hi3nkr/the_mod_conversations_that_went_into_todays\"&gt;detailed notes from our All-Council mod call here&lt;/a&gt;, including specific product work we discussed.&lt;/li&gt;\n&lt;/ul&gt;&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;We developed our enforcement plan&lt;/strong&gt;, including both our immediate actions (e.g., today’s bans) and long-term investments (tackling the most critical work discussed in our mod calls, sustainably enforcing the new policies, and advancing Reddit’s community governance).&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;From our conversations with mods and outside experts, it’s clear that while we’ve gotten better in some areas—like actioning violations at the community level, &lt;a href=\"https://www.reddit.com/r/redditsecurity/comments/hbiuas/reddit_security_report_june_18_2020/\"&gt;scaling enforcement efforts&lt;/a&gt;, measurably reducing hateful experiences like harassment &lt;a href=\"https://www.adl.org/online-hate-2020#results\"&gt;year over year&lt;/a&gt;—we still have a long way to go to address the gaps in our policies and enforcement to date.&lt;/p&gt;\n\n&lt;p&gt;These include addressing questions our policies have left unanswered (like whether hate speech is allowed or even protected on Reddit), aspects of our product and mod tools that are still too easy for individual bad actors to abuse (inboxes, chats, modmail), and areas where we can do better to partner with our mods and communities who want to combat the same hateful conduct we do.&lt;/p&gt;\n\n&lt;p&gt;Ultimately, it’s our responsibility to support our communities by taking stronger action against those who try to weaponize parts of Reddit against other people. In the near term, this support will translate into some of the product work we discussed with mods. But it starts with dealing squarely with the hate we can mitigate today through our policies and enforcement.&lt;/p&gt;\n\n&lt;h2&gt;New Policy&lt;/h2&gt;\n\n&lt;p&gt;This is the &lt;a href=\"https://www.redditinc.com/policies/content-policy\"&gt;new content policy&lt;/a&gt;. Here’s what’s different:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;It starts with a statement of our vision for Reddit and our communities, including the basic expectations we have for all communities and users.&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Rule 1&lt;/strong&gt; explicitly states that communities and users that promote hate based on identity or vulnerability will be banned.\n\n&lt;ul&gt;\n&lt;li&gt;There is an expanded definition of what constitutes a violation of this rule, along with specific examples, in our &lt;a href=\"https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/promoting-hate-based-identity-or\"&gt;Help Center article&lt;/a&gt;.&lt;/li&gt;\n&lt;/ul&gt;&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Rule 2&lt;/strong&gt; ties together our previous rules on prohibited behavior with an ask to abide by community rules and post with authentic, personal interest.\n\n&lt;ul&gt;\n&lt;li&gt;Debate and creativity are welcome, but spam and malicious attempts to interfere with other communities are not.&lt;/li&gt;\n&lt;/ul&gt;&lt;/li&gt;\n&lt;li&gt;The other rules are the same in spirit but have been rewritten for clarity and inclusiveness.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;Alongside the change to the content policy, we are initially banning about 2000 subreddits, the vast majority of which are inactive. Of these communities, about &lt;a href=\"https://www.redditstatic.com/banned-subreddits-june-2020.txt\"&gt;200&lt;/a&gt; have more than 10 daily users. Both &lt;a href=\"/r/The_Donald\"&gt;r/The_Donald&lt;/a&gt; and &lt;a href=\"/r/ChapoTrapHouse\"&gt;r/ChapoTrapHouse&lt;/a&gt; were included.&lt;/p&gt;\n\n&lt;p&gt;All communities on Reddit must abide by our content policy in good faith. We banned &lt;a href=\"/r/The_Donald\"&gt;r/The_Donald&lt;/a&gt; because it has not done so, despite every opportunity. The community has consistently hosted and upvoted more rule-breaking content than average (Rule 1), antagonized us and other communities (Rules 2 and 8), and its mods have refused to meet our most basic expectations. Until now, we’ve worked in good faith to help them preserve the community as a space for its users—through warnings, mod changes, quarantining, and more.&lt;/p&gt;\n\n&lt;p&gt;Though smaller, &lt;a href=\"/r/ChapoTrapHouse\"&gt;r/ChapoTrapHouse&lt;/a&gt; was banned for similar reasons: They consistently host rule-breaking content and their mods have demonstrated no intention of reining in their community.&lt;/p&gt;\n\n&lt;p&gt;To be clear, views across the political spectrum are allowed on Reddit—but all communities must work within our policies and do so in good faith, without exception.&lt;/p&gt;\n\n&lt;h2&gt;Our commitment&lt;/h2&gt;\n\n&lt;p&gt;Our policies will never be perfect, with new edge cases that inevitably lead us to evolve them in the future. And as users, you will always have more context, community vernacular, and cultural values to inform the standards set within your communities than we as site admins or any AI ever could.&lt;/p&gt;\n\n&lt;p&gt;But just as our content moderation cannot scale effectively without your support, you need more support from us as well, and we admit we have fallen short towards this end. We are committed to working with you to combat the bad actors, abusive behaviors, and toxic communities that undermine our mission and get in the way of the creativity, discussions, and communities that bring us all to Reddit in the first place. We hope that our progress towards this commitment, with today’s update and those to come, makes Reddit a place you enjoy and are proud to be a part of for many years to come.&lt;/p&gt;\n\n&lt;p&gt;Edit: After digesting feedback, we made a clarifying change to our help center article for &lt;a href=\"https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/promoting-hate-based-identity-or\"&gt;Promoting Hate Based on Identity or Vulnerability&lt;/a&gt;.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/YkGt7d1HX5G7ksWcJ3CWE0_p-Nlky6D1dcGbmtFmm-A.jpg?auto=webp&amp;s=7c76bf2d6db3ccf394e48613d14856600a48ccf2", "width": 600, "height": 315}, "resolutions": [{"url": "https://external-preview.redd.it/YkGt7d1HX5G7ksWcJ3CWE0_p-Nlky6D1dcGbmtFmm-A.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=fcb5cfc2880c031448b9d1d04d212fdf98c5b205", "width": 108, "height": 56}, {"url": "https://external-preview.redd.it/YkGt7d1HX5G7ksWcJ3CWE0_p-Nlky6D1dcGbmtFmm-A.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=515cb439ca23401ab7f1e8bae66e706130a7fba5", "width": 216, "height": 113}, {"url": "https://external-preview.redd.it/YkGt7d1HX5G7ksWcJ3CWE0_p-Nlky6D1dcGbmtFmm-A.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=fe6d9a78c0da81c7e8d8f21e23ba3df9c8f27dd0", "width": 320, "height": 168}], "variants": {}, "id": "DBM8bKEwHknB9_9Dnu0Uc5QGrHxu2XgotWfSJQ3Zhy4"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "hi3oht", "is_robot_indexable": true, "report_reasons": null, "author": "spez", "discussion_type": null, "num_comments": 38400, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/hi3oht/update_to_our_content_policy/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/hi3oht/update_to_our_content_policy/", "subreddit_subscribers": *********, "created_utc": 1593450006.0, "num_crossposts": 89, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "**TL;DR: We’re working with mods to change our content policy to explicitly address hate.** u/kn0thing **has resigned from our board to fill his seat with a Black candidate, a request we will honor. I want to take responsibility for the history of our policies over the years that got us here, and we still have work to do.**\n\nAfter watching people across the country mourn and demand an end to centuries of murder and violent discrimination against Black people, I wanted to speak out. I wanted to do this both as a human being, who sees this grief and pain and knows I have been spared from it myself because of the color of my skin, and as someone who literally has a platform and, with it, a duty to speak out.\n\nEarlier this week, I wrote an email to our company addressing this crisis and a few ways Reddit will respond. When we shared it, many of the responses said something like, “How can a company that has faced racism from users on its own platform over the years credibly take such a position?”\n\nThese questions, which I know are coming from a place of real pain and which I take to heart, are really a statement: There is an unacceptable gap between our beliefs as people and a company, and what you see in our content policy. \n\nOver the last fifteen years, hundreds of millions of people have come to Reddit for things that I believe are fundamentally good: user-driven communities—across a wider spectrum of interests and passions than I could’ve imagined when we first created subreddits—and the kinds of content and conversations that keep people coming back day after day. It's why we come to Reddit as users, as mods, and as employees who want to bring this sort of community and belonging to the world and make it better daily.\n\nHowever, as Reddit has grown, alongside much good, it is facing its own challenges around hate and racism. We have to acknowledge and accept responsibility for the role we have played. Here are three problems we are most focused on:\n\n* Parts of Reddit reflect an unflattering but real resemblance to the world in the hate that Black users and communities see daily, despite the progress we have made in improving our tooling and enforcement. \n* Users and moderators genuinely do not have enough clarity as to where we as administrators stand on racism. \n* Our moderators are frustrated and need a real seat at the table to help shape the policies that they help us enforce.\n\nWe are already working to fix these problems, and this is a promise for more urgency. Our current [content policy](https://www.redditinc.com/policies/content-policy) is effectively nine rules for what you cannot do on Reddit. In many respects, it’s served us well. Under it, we have made meaningful progress cleaning up the platform (and done so without undermining the free expression and authenticity that fuels Reddit). That said, we still have work to do. This current policy lists only what you cannot do, articulates none of the values behind the rules, and does not explicitly take a stance on hate or racism.\n\nWe will update our content policy to include a vision for Reddit and its communities to aspire to, a statement on hate, the context for the rules, and a principle that Reddit isn’t to be used as a weapon. We have details to work through, and while we will move quickly, I do want to be thoughtful and also gather feedback from our moderators (through our Mod Councils). With more moderator engagement, the timeline is weeks, not months.\n\nAnd just this morning, Alexis Ohanian (u/kn0thing), my Reddit cofounder, announced that he is resigning from our board and that he wishes for his seat to be filled with a Black candidate, a request that the board and I will honor. We thank Alexis for this meaningful gesture and all that he’s done for us over the years.\n\nAt the risk of making this unreadably long, I'd like to take this moment to share how we got here in the first place, where we have made progress, and where, despite our best intentions, we have fallen short.\n\nIn the early days of Reddit, 2005–2006, our idealistic “policy” was that, excluding spam, we would not remove content. We were small and did not face many hard decisions. When this ideal was tested, we [banned racist users](https://www.reddit.com/r/reddit.com/comments/6m87a/can_we_ban_this_extremely_racist_asshole/c0497kd/?context=3) anyway. In the end, we acted based on our beliefs, despite our “policy.” \n\nI left Reddit from 2010–2015. During this time, in addition to rapid user growth, Reddit’s no-removal policy [ossified](https://redditblog.com/2014/09/06/every-man-is-responsible-for-his-own-soul/) and its content policy took [no position on hate](https://web.archive.org/web/20150710230652/https://www.reddit.com/rules/). \n\nWhen I returned in 2015, my top priority was creating a content policy to do two things: deal with hateful communities I had been immediately confronted with (like r/CoonTown, which was explicitly designed to spread racist hate) and provide a clear policy of what’s acceptable on Reddit and what’s not. [We banned that community and others](https://www.reddit.com/r/announcements/comments/3fx2au/content_policy_update/ctsqobs/) because they were “making Reddit worse” but were not clear and direct about their role in sowing hate. We crafted our [2015 policy](https://web.archive.org/web/20150806171017/https://www.reddit.com/help/contentpolicy) around behaviors adjacent to hate that were actionable and objective: violence and harassment, because we struggled to create a definition of hate and racism that we could defend and enforce at our scale. Through continual updates to these policies [2017](https://www.reddit.com/r/modnews/comments/78p7bz/update_on_sitewide_rules_regarding_violent_content/), [2018](https://www.reddit.com/r/announcements/comments/863xcj/new_addition_to_sitewide_rules_regarding_the_use/), [2019](https://www.reddit.com/r/announcements/comments/dbf9nj/changes_to_our_policy_against_bullying_and/), [2020](https://www.reddit.com/r/redditsecurity/comments/emd7yx/updates_to_our_policy_around_impersonation/) (and a broader definition of violence), we have removed thousands of hateful communities. \n\nWhile we dealt with many communities themselves, we still did not provide the clarity—and it showed, both in our enforcement and in confusion about where we stand. In 2018, I confusingly said [racism is not against the rules, but also isn’t welcome on Reddit](https://www.reddit.com/r/announcements/comments/3fx2au/content_policy_update/ctsqobs/). This gap between our content policy and our values has eroded our effectiveness in combating hate and racism on Reddit; I accept full responsibility for this.\n\nThis inconsistency has hurt our trust with our users and moderators and has made us slow to respond to problems. This was also true with r/the_donald, a community that relished in exploiting and detracting from the best of Reddit and that is now nearly disintegrated on their own accord. As we looked to our policies, “Breaking Reddit” was not a sufficient explanation for actioning a political subreddit, and I fear we let being technically correct get in the way of doing the right thing. Clearly, we should have quarantined it sooner.\n\nThe majority of our top communities have a rule banning hate and racism, which makes us proud, and is evidence why a [community-led approach is the only way to scale moderation online](https://docs.house.gov/meetings/IF/IF16/20191016/110075/HHRG-116-IF16-Wstate-HuffmanS-20191016.pdf). That said, this is not a rule communities should have to write for themselves and we need to rebalance the burden of enforcement. I also accept responsibility for this. \n\nDespite making significant progress over the years, we have to turn a mirror on ourselves and be willing to do the hard work of making sure we are living up to our values in our product and policies. This is a significant moment. We have a choice: return to the status quo or use this opportunity for change. We at Reddit are opting for the latter, and we will do our very best to be a part of the progress.\n\nI will be sticking around for a while to answer questions as usual, but I also know that our policies and actions will speak louder than our comments.\n\nThanks,\n\nSteve", "author_fullname": "t2_1w72", "saved": false, "mod_reason_title": null, "gilded": 19, "clicked": false, "title": "Upcoming changes to our content policy, our board, and where we’re going from here", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_gxas21", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.55, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 40929, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 40929, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1591383887.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;&lt;strong&gt;TL;DR: We’re working with mods to change our content policy to explicitly address hate.&lt;/strong&gt; &lt;a href=\"/u/kn0thing\"&gt;u/kn0thing&lt;/a&gt; &lt;strong&gt;has resigned from our board to fill his seat with a Black candidate, a request we will honor. I want to take responsibility for the history of our policies over the years that got us here, and we still have work to do.&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;After watching people across the country mourn and demand an end to centuries of murder and violent discrimination against Black people, I wanted to speak out. I wanted to do this both as a human being, who sees this grief and pain and knows I have been spared from it myself because of the color of my skin, and as someone who literally has a platform and, with it, a duty to speak out.&lt;/p&gt;\n\n&lt;p&gt;Earlier this week, I wrote an email to our company addressing this crisis and a few ways Reddit will respond. When we shared it, many of the responses said something like, “How can a company that has faced racism from users on its own platform over the years credibly take such a position?”&lt;/p&gt;\n\n&lt;p&gt;These questions, which I know are coming from a place of real pain and which I take to heart, are really a statement: There is an unacceptable gap between our beliefs as people and a company, and what you see in our content policy. &lt;/p&gt;\n\n&lt;p&gt;Over the last fifteen years, hundreds of millions of people have come to Reddit for things that I believe are fundamentally good: user-driven communities—across a wider spectrum of interests and passions than I could’ve imagined when we first created subreddits—and the kinds of content and conversations that keep people coming back day after day. It&amp;#39;s why we come to Reddit as users, as mods, and as employees who want to bring this sort of community and belonging to the world and make it better daily.&lt;/p&gt;\n\n&lt;p&gt;However, as Reddit has grown, alongside much good, it is facing its own challenges around hate and racism. We have to acknowledge and accept responsibility for the role we have played. Here are three problems we are most focused on:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Parts of Reddit reflect an unflattering but real resemblance to the world in the hate that Black users and communities see daily, despite the progress we have made in improving our tooling and enforcement. &lt;/li&gt;\n&lt;li&gt;Users and moderators genuinely do not have enough clarity as to where we as administrators stand on racism. &lt;/li&gt;\n&lt;li&gt;Our moderators are frustrated and need a real seat at the table to help shape the policies that they help us enforce.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;We are already working to fix these problems, and this is a promise for more urgency. Our current &lt;a href=\"https://www.redditinc.com/policies/content-policy\"&gt;content policy&lt;/a&gt; is effectively nine rules for what you cannot do on Reddit. In many respects, it’s served us well. Under it, we have made meaningful progress cleaning up the platform (and done so without undermining the free expression and authenticity that fuels Reddit). That said, we still have work to do. This current policy lists only what you cannot do, articulates none of the values behind the rules, and does not explicitly take a stance on hate or racism.&lt;/p&gt;\n\n&lt;p&gt;We will update our content policy to include a vision for Reddit and its communities to aspire to, a statement on hate, the context for the rules, and a principle that Reddit isn’t to be used as a weapon. We have details to work through, and while we will move quickly, I do want to be thoughtful and also gather feedback from our moderators (through our Mod Councils). With more moderator engagement, the timeline is weeks, not months.&lt;/p&gt;\n\n&lt;p&gt;And just this morning, Alexis Ohanian (&lt;a href=\"/u/kn0thing\"&gt;u/kn0thing&lt;/a&gt;), my Reddit cofounder, announced that he is resigning from our board and that he wishes for his seat to be filled with a Black candidate, a request that the board and I will honor. We thank Alexis for this meaningful gesture and all that he’s done for us over the years.&lt;/p&gt;\n\n&lt;p&gt;At the risk of making this unreadably long, I&amp;#39;d like to take this moment to share how we got here in the first place, where we have made progress, and where, despite our best intentions, we have fallen short.&lt;/p&gt;\n\n&lt;p&gt;In the early days of Reddit, 2005–2006, our idealistic “policy” was that, excluding spam, we would not remove content. We were small and did not face many hard decisions. When this ideal was tested, we &lt;a href=\"https://www.reddit.com/r/reddit.com/comments/6m87a/can_we_ban_this_extremely_racist_asshole/c0497kd/?context=3\"&gt;banned racist users&lt;/a&gt; anyway. In the end, we acted based on our beliefs, despite our “policy.” &lt;/p&gt;\n\n&lt;p&gt;I left Reddit from 2010–2015. During this time, in addition to rapid user growth, Reddit’s no-removal policy &lt;a href=\"https://redditblog.com/2014/09/06/every-man-is-responsible-for-his-own-soul/\"&gt;ossified&lt;/a&gt; and its content policy took &lt;a href=\"https://web.archive.org/web/20150710230652/https://www.reddit.com/rules/\"&gt;no position on hate&lt;/a&gt;. &lt;/p&gt;\n\n&lt;p&gt;When I returned in 2015, my top priority was creating a content policy to do two things: deal with hateful communities I had been immediately confronted with (like &lt;a href=\"/r/CoonTown\"&gt;r/CoonTown&lt;/a&gt;, which was explicitly designed to spread racist hate) and provide a clear policy of what’s acceptable on Reddit and what’s not. &lt;a href=\"https://www.reddit.com/r/announcements/comments/3fx2au/content_policy_update/ctsqobs/\"&gt;We banned that community and others&lt;/a&gt; because they were “making Reddit worse” but were not clear and direct about their role in sowing hate. We crafted our &lt;a href=\"https://web.archive.org/web/20150806171017/https://www.reddit.com/help/contentpolicy\"&gt;2015 policy&lt;/a&gt; around behaviors adjacent to hate that were actionable and objective: violence and harassment, because we struggled to create a definition of hate and racism that we could defend and enforce at our scale. Through continual updates to these policies &lt;a href=\"https://www.reddit.com/r/modnews/comments/78p7bz/update_on_sitewide_rules_regarding_violent_content/\"&gt;2017&lt;/a&gt;, &lt;a href=\"https://www.reddit.com/r/announcements/comments/863xcj/new_addition_to_sitewide_rules_regarding_the_use/\"&gt;2018&lt;/a&gt;, &lt;a href=\"https://www.reddit.com/r/announcements/comments/dbf9nj/changes_to_our_policy_against_bullying_and/\"&gt;2019&lt;/a&gt;, &lt;a href=\"https://www.reddit.com/r/redditsecurity/comments/emd7yx/updates_to_our_policy_around_impersonation/\"&gt;2020&lt;/a&gt; (and a broader definition of violence), we have removed thousands of hateful communities. &lt;/p&gt;\n\n&lt;p&gt;While we dealt with many communities themselves, we still did not provide the clarity—and it showed, both in our enforcement and in confusion about where we stand. In 2018, I confusingly said &lt;a href=\"https://www.reddit.com/r/announcements/comments/3fx2au/content_policy_update/ctsqobs/\"&gt;racism is not against the rules, but also isn’t welcome on Reddit&lt;/a&gt;. This gap between our content policy and our values has eroded our effectiveness in combating hate and racism on Reddit; I accept full responsibility for this.&lt;/p&gt;\n\n&lt;p&gt;This inconsistency has hurt our trust with our users and moderators and has made us slow to respond to problems. This was also true with &lt;a href=\"/r/the_donald\"&gt;r/the_donald&lt;/a&gt;, a community that relished in exploiting and detracting from the best of Reddit and that is now nearly disintegrated on their own accord. As we looked to our policies, “Breaking Reddit” was not a sufficient explanation for actioning a political subreddit, and I fear we let being technically correct get in the way of doing the right thing. Clearly, we should have quarantined it sooner.&lt;/p&gt;\n\n&lt;p&gt;The majority of our top communities have a rule banning hate and racism, which makes us proud, and is evidence why a &lt;a href=\"https://docs.house.gov/meetings/IF/IF16/20191016/110075/HHRG-116-IF16-Wstate-HuffmanS-20191016.pdf\"&gt;community-led approach is the only way to scale moderation online&lt;/a&gt;. That said, this is not a rule communities should have to write for themselves and we need to rebalance the burden of enforcement. I also accept responsibility for this. &lt;/p&gt;\n\n&lt;p&gt;Despite making significant progress over the years, we have to turn a mirror on ourselves and be willing to do the hard work of making sure we are living up to our values in our product and policies. This is a significant moment. We have a choice: return to the status quo or use this opportunity for change. We at Reddit are opting for the latter, and we will do our very best to be a part of the progress.&lt;/p&gt;\n\n&lt;p&gt;I will be sticking around for a while to answer questions as usual, but I also know that our policies and actions will speak louder than our comments.&lt;/p&gt;\n\n&lt;p&gt;Thanks,&lt;/p&gt;\n\n&lt;p&gt;Steve&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?auto=webp&amp;s=f65608a0e50b80501b09dd4e6d4256a073bddaf7", "width": 1920, "height": 1080}, "resolutions": [{"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=f58daaa031b1949d7fba33ff04daa716f834c5fa", "width": 108, "height": 60}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=ef7c929673f1ddd3f2104d8d8eeb87c06e8c594b", "width": 216, "height": 121}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=1fb00ddb4a2d99782e7de7cde51676f0db742639", "width": 320, "height": 180}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=f7ed52b21d8431d206e4858a89b65eb9a4630955", "width": 640, "height": 360}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=b0580c69aeaa769a86051f67d7385a6c5bee2db4", "width": 960, "height": 540}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=747dd834ecf741b4de8a13d5a4f725220bd91edb", "width": 1080, "height": 607}], "variants": {}, "id": "kSMg4tuE5uFPfCQ-p6TNLozpkw7k_rxRNpv2tvuvhbY"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "gxas21", "is_robot_indexable": true, "report_reasons": null, "author": "spez", "discussion_type": null, "num_comments": 40685, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/gxas21/upcoming_changes_to_our_content_policy_our_board/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/gxas21/upcoming_changes_to_our_content_policy_our_board/", "subreddit_subscribers": *********, "created_utc": 1591383887.0, "num_crossposts": 97, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "As the 2020 election approaches, we are updating our [policy on political advertising](https://www.reddithelp.com/en/categories/advertising/policy-guidelines/reddit-advertising-policy) to better reflect the role Reddit plays in the political conversation and bring high quality political ads to Redditors.\n\nAs a reminder, Reddit’s advertising policy already forbids deceptive, untrue, or misleading advertising (political advertisers included). Further, each political ad is manually reviewed for messaging and creative content, we do not accept political ads from advertisers and candidates based outside the United States, and we only allow political ads at the federal level.\n\nThat said, beginning today, we will *also* require political advertisers to work directly with our sales team and leave comments “on” for (at least) the first 24 hours of any given campaign. We will strongly encourage political advertisers to use this opportunity to engage directly with users in the comments.\n\nIn tandem, we are launching a subreddit dedicated to political ads transparency, which will list all political ad campaigns running on Reddit dating back to January 1, 2019. In this community, you will find information on the individual advertiser, their targeting, impressions, and spend on a per-campaign basis. We plan to consistently update this subreddit as new political ads run on Reddit, so we can provide transparency into our political advertisers and the conversation their ad(s) inspires. If you would like to follow along, please subscribe to [r/RedditPoliticalAds](https://www.reddit.com/r/RedditPoliticalAds/) for more information.\n\nWe hope this update will give you a chance to engage directly and transparently with political advertisers around important political issues, and provide a line of sight into the campaigns and political organizations seeking your attention. By requiring political advertisers to work closely with the Reddit Sales team, ensuring comments remain enabled for 24 hours, and establishing a political ads transparency subreddit, we believe we can better serve the Reddit ecosystem by spurring important conversation, enabling our users to provide their own feedback on political ads, and better protecting the community from inappropriate political ads, bad actors, and misinformation.\n\nPlease see the full updated political ads policy below:\n\n*All political advertisements must be manually approved by Reddit. In order to be approved, the advertiser must be actively working with a Reddit Sales Representative (for more information on the managed sales process, please see “Advertising at Scale”* [*here*](https://www.redditinc.com/advertising)*.) Political advertisers will also be asked to present additional information to verify their identity and/or authorization to place such advertisements.*\n\n*Political advertisements on Reddit include, but are not limited to, the following:*\n\n* *Ads related to campaigns or elections, or that solicit political donations;*\n* *Ads that promote voting or voter registration (discouraging voting or voter registration is not allowed);*\n* *Ads promoting political merchandise (for example, products featuring a public office holder or candidate, political slogans, etc);*\n* *Issue ads or advocacy ads pertaining to topics of potential legislative or political importance or placed by political organizations*\n\n*Advertisements in this category must include clear \"paid for by\" disclosures within the ad copy and/or creative, and must comply with all applicable laws and regulations, including those promulgated by the Federal Elections Commission. All political advertisements must also have comments enabled for at least the first 24 hours of the ad run. The advertiser is strongly encouraged to engage with Reddit users directly in these comments. The advertisement and any comments must still adhere to Reddit’s* [*Content Policy*](https://www.redditinc.com/policies/content-policy)*.*\n\n*Please note additionally that information regarding political ad campaigns and their purchasing individuals or entities may be publicly disclosed by Reddit for transparency purposes.*\n\n*Finally, Reddit only accepts political advertisements within the United States, at the federal level. Political advertisements at the state and local level, or outside of the United States are not allowed.*\n\n\\--------------\n\nPlease read our full advertising policy [here](https://www.reddithelp.com/en/categories/advertising/policy-guidelines/reddit-advertising-policy).", "author_fullname": "t2_4xyfab5r", "saved": false, "mod_reason_title": null, "gilded": 1, "clicked": false, "title": "Changes to Reddit’s Political Ads Policy", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_g0s6tn", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.72, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 21150, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 21150, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1586813738.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;As the 2020 election approaches, we are updating our &lt;a href=\"https://www.reddithelp.com/en/categories/advertising/policy-guidelines/reddit-advertising-policy\"&gt;policy on political advertising&lt;/a&gt; to better reflect the role Reddit plays in the political conversation and bring high quality political ads to Redditors.&lt;/p&gt;\n\n&lt;p&gt;As a reminder, Reddit’s advertising policy already forbids deceptive, untrue, or misleading advertising (political advertisers included). Further, each political ad is manually reviewed for messaging and creative content, we do not accept political ads from advertisers and candidates based outside the United States, and we only allow political ads at the federal level.&lt;/p&gt;\n\n&lt;p&gt;That said, beginning today, we will &lt;em&gt;also&lt;/em&gt; require political advertisers to work directly with our sales team and leave comments “on” for (at least) the first 24 hours of any given campaign. We will strongly encourage political advertisers to use this opportunity to engage directly with users in the comments.&lt;/p&gt;\n\n&lt;p&gt;In tandem, we are launching a subreddit dedicated to political ads transparency, which will list all political ad campaigns running on Reddit dating back to January 1, 2019. In this community, you will find information on the individual advertiser, their targeting, impressions, and spend on a per-campaign basis. We plan to consistently update this subreddit as new political ads run on Reddit, so we can provide transparency into our political advertisers and the conversation their ad(s) inspires. If you would like to follow along, please subscribe to &lt;a href=\"https://www.reddit.com/r/RedditPoliticalAds/\"&gt;r/RedditPoliticalAds&lt;/a&gt; for more information.&lt;/p&gt;\n\n&lt;p&gt;We hope this update will give you a chance to engage directly and transparently with political advertisers around important political issues, and provide a line of sight into the campaigns and political organizations seeking your attention. By requiring political advertisers to work closely with the Reddit Sales team, ensuring comments remain enabled for 24 hours, and establishing a political ads transparency subreddit, we believe we can better serve the Reddit ecosystem by spurring important conversation, enabling our users to provide their own feedback on political ads, and better protecting the community from inappropriate political ads, bad actors, and misinformation.&lt;/p&gt;\n\n&lt;p&gt;Please see the full updated political ads policy below:&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;All political advertisements must be manually approved by Reddit. In order to be approved, the advertiser must be actively working with a Reddit Sales Representative (for more information on the managed sales process, please see “Advertising at Scale”&lt;/em&gt; &lt;a href=\"https://www.redditinc.com/advertising\"&gt;&lt;em&gt;here&lt;/em&gt;&lt;/a&gt;&lt;em&gt;.) Political advertisers will also be asked to present additional information to verify their identity and/or authorization to place such advertisements.&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Political advertisements on Reddit include, but are not limited to, the following:&lt;/em&gt;&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;&lt;em&gt;Ads related to campaigns or elections, or that solicit political donations;&lt;/em&gt;&lt;/li&gt;\n&lt;li&gt;&lt;em&gt;Ads that promote voting or voter registration (discouraging voting or voter registration is not allowed);&lt;/em&gt;&lt;/li&gt;\n&lt;li&gt;&lt;em&gt;Ads promoting political merchandise (for example, products featuring a public office holder or candidate, political slogans, etc);&lt;/em&gt;&lt;/li&gt;\n&lt;li&gt;&lt;em&gt;Issue ads or advocacy ads pertaining to topics of potential legislative or political importance or placed by political organizations&lt;/em&gt;&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;&lt;em&gt;Advertisements in this category must include clear &amp;quot;paid for by&amp;quot; disclosures within the ad copy and/or creative, and must comply with all applicable laws and regulations, including those promulgated by the Federal Elections Commission. All political advertisements must also have comments enabled for at least the first 24 hours of the ad run. The advertiser is strongly encouraged to engage with Reddit users directly in these comments. The advertisement and any comments must still adhere to Reddit’s&lt;/em&gt; &lt;a href=\"https://www.redditinc.com/policies/content-policy\"&gt;&lt;em&gt;Content Policy&lt;/em&gt;&lt;/a&gt;&lt;em&gt;.&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Please note additionally that information regarding political ad campaigns and their purchasing individuals or entities may be publicly disclosed by Reddit for transparency purposes.&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Finally, Reddit only accepts political advertisements within the United States, at the federal level. Political advertisements at the state and local level, or outside of the United States are not allowed.&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;--------------&lt;/p&gt;\n\n&lt;p&gt;Please read our full advertising policy &lt;a href=\"https://www.reddithelp.com/en/categories/advertising/policy-guidelines/reddit-advertising-policy\"&gt;here&lt;/a&gt;.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "g0s6tn", "is_robot_indexable": true, "report_reasons": null, "author": "con_commenter", "discussion_type": null, "num_comments": 99493, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/g0s6tn/changes_to_reddits_political_ads_policy/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/g0s6tn/changes_to_reddits_political_ads_policy/", "subreddit_subscribers": *********, "created_utc": 1586813738.0, "num_crossposts": 23, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "It’s been incredible to witness the ways in which the Reddit community has come together to raise awareness, share information and resources, and support each other during a time of universal need. Across the platform, existing communities like [r/science](https://www.reddit.com/r/science/), [r/askscience](https://www.reddit.com/r/askscience/), and [r/worldnews](https://www.reddit.com/r/worldnews/) have joined newly established communities like [r/Coronavirus](https://www.reddit.com/r/Coronavirus/) and [r/COVID19](https://www.reddit.com/r/COVID19/) to share authoritative content and welcome important discussion every day.\n\nAt Reddit Inc., we’ve also been [working](https://redditblog.com/2020/03/02/expert-conversation-on-coronavirus/) to curate expert discussions and surface the most reliable information for you. And today, we’re excited to launch the Solidarity Award, which seeks to raise funds for fighting the COVID-19 pandemic via the [COVID-19 Solidarity Response Fund](https://covid19responsefund.org/) for the [World Health Organization (WHO)](https://www.who.int/). The fund -- which is powered by the [United Nations Foundation](https://unfoundation.org/) and the [Swiss Philanthropy Foundation](https://www.swissphilanthropy.ch/en/) \\-- supports [WHO’s](https://www.who.int/) work to track and understand the spread of COVID-19, ensure patients get the care they need, frontline workers get essential supplies and information, and accelerate efforts to develop vaccines, tests, and treatments for the pandemic.\n\nStarting today, you can purchase the Solidarity Award directly on Reddit desktop and mobile web (via PayPal or Stripe), and **100% of the proceeds** will benefit the [COVID-19 Solidarity Response Fund](https://covid19responsefund.org/) for WHO.\\*\n\n**Here are a few details on the Solidarity Award**:\n\n* How to find the Award: The Solidarity Award can only be given on Reddit desktop and mobile web (not currently available to give on Mobile apps). You'll find the award towards the bottom of the Medals section in our Award dialog. \n* The full price of the Award ($3.99) will be donated by Reddit to the United Nation Foundation’s COVID-19 Solidarity Response Fund for the [World Health Organization](https://www.who.int/). More information on the fund is available at [www.covid19responsefund.org](http://www.covid19responsefund.org/)\n* Donors will receive a special Reddit Trophy, which will be added to users’ trophy cases on their profile page (on or before 4/30/20)\n* Awards given are visible across all platforms\n\nSee the award here:\n\n&amp;#x200B;\n\n[Solidarity Award](https://preview.redd.it/9erskau1mgq41.png?width=96&amp;format=png&amp;auto=webp&amp;s=e19ac0910c2c11697f56868fcd6084f6e038008a)\n\n**Why are we doing this?**\n\nWe’ve never felt more urgency or responsibility to fulfill our mission of bringing community and belonging to everyone in the world. The Solidarity Award is meant to complement the efforts of our users, moderators, and employees at Reddit by enabling community-wide charitable giving during a time of great need.\n\n**A Heads Up:**\n\nThe team at Reddit worked quickly to enable the Solidarity Award. As with all new things at this scale, we are keeping an eye out for any bugs and issues that may arise, and will update the experience accordingly.\n\n**From Reddit to all of our users:** Stay safe, be vigilant, and take care of one another.\n\n\\**Reddit is covering the transaction fees associated with the purchase of the Solidarity Award*", "author_fullname": "t2_4son7qz1", "saved": false, "mod_reason_title": null, "gilded": 3, "clicked": false, "title": "Introducing the Solidarity Award — A 100% contribution to the COVID-19 Solidarity Response Fund for WHO", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": 70, "top_awarded_type": null, "hide_score": false, "media_metadata": {"9erskau1mgq41": {"status": "valid", "e": "Image", "m": "image/png", "p": [], "s": {"y": 96, "x": 96, "u": "https://preview.redd.it/9erskau1mgq41.png?width=96&amp;format=png&amp;auto=webp&amp;s=e19ac0910c2c11697f56868fcd6084f6e038008a"}, "id": "9erskau1mgq41"}}, "name": "t3_fub7xo", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.61, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 19216, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": 70, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 19216, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "https://b.thumbs.redditmedia.com/RROcLjDSUc0QV1UeQ1txXdyl1C_7233mmsMgtYXxQzo.jpg", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1585930496.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;It’s been incredible to witness the ways in which the Reddit community has come together to raise awareness, share information and resources, and support each other during a time of universal need. Across the platform, existing communities like &lt;a href=\"https://www.reddit.com/r/science/\"&gt;r/science&lt;/a&gt;, &lt;a href=\"https://www.reddit.com/r/askscience/\"&gt;r/askscience&lt;/a&gt;, and &lt;a href=\"https://www.reddit.com/r/worldnews/\"&gt;r/worldnews&lt;/a&gt; have joined newly established communities like &lt;a href=\"https://www.reddit.com/r/Coronavirus/\"&gt;r/Coronavirus&lt;/a&gt; and &lt;a href=\"https://www.reddit.com/r/COVID19/\"&gt;r/COVID19&lt;/a&gt; to share authoritative content and welcome important discussion every day.&lt;/p&gt;\n\n&lt;p&gt;At Reddit Inc., we’ve also been &lt;a href=\"https://redditblog.com/2020/03/02/expert-conversation-on-coronavirus/\"&gt;working&lt;/a&gt; to curate expert discussions and surface the most reliable information for you. And today, we’re excited to launch the Solidarity Award, which seeks to raise funds for fighting the COVID-19 pandemic via the &lt;a href=\"https://covid19responsefund.org/\"&gt;COVID-19 Solidarity Response Fund&lt;/a&gt; for the &lt;a href=\"https://www.who.int/\"&gt;World Health Organization (WHO)&lt;/a&gt;. The fund -- which is powered by the &lt;a href=\"https://unfoundation.org/\"&gt;United Nations Foundation&lt;/a&gt; and the &lt;a href=\"https://www.swissphilanthropy.ch/en/\"&gt;Swiss Philanthropy Foundation&lt;/a&gt; -- supports &lt;a href=\"https://www.who.int/\"&gt;WHO’s&lt;/a&gt; work to track and understand the spread of COVID-19, ensure patients get the care they need, frontline workers get essential supplies and information, and accelerate efforts to develop vaccines, tests, and treatments for the pandemic.&lt;/p&gt;\n\n&lt;p&gt;Starting today, you can purchase the Solidarity Award directly on Reddit desktop and mobile web (via PayPal or Stripe), and &lt;strong&gt;100% of the proceeds&lt;/strong&gt; will benefit the &lt;a href=\"https://covid19responsefund.org/\"&gt;COVID-19 Solidarity Response Fund&lt;/a&gt; for WHO.*&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Here are a few details on the Solidarity Award&lt;/strong&gt;:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;How to find the Award: The Solidarity Award can only be given on Reddit desktop and mobile web (not currently available to give on Mobile apps). You&amp;#39;ll find the award towards the bottom of the Medals section in our Award dialog. &lt;/li&gt;\n&lt;li&gt;The full price of the Award ($3.99) will be donated by Reddit to the United Nation Foundation’s COVID-19 Solidarity Response Fund for the &lt;a href=\"https://www.who.int/\"&gt;World Health Organization&lt;/a&gt;. More information on the fund is available at &lt;a href=\"http://www.covid19responsefund.org/\"&gt;www.covid19responsefund.org&lt;/a&gt;&lt;/li&gt;\n&lt;li&gt;Donors will receive a special Reddit Trophy, which will be added to users’ trophy cases on their profile page (on or before 4/30/20)&lt;/li&gt;\n&lt;li&gt;Awards given are visible across all platforms&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;See the award here:&lt;/p&gt;\n\n&lt;p&gt;&amp;#x200B;&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/9erskau1mgq41.png?width=96&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=e19ac0910c2c11697f56868fcd6084f6e038008a\"&gt;Solidarity Award&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Why are we doing this?&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;We’ve never felt more urgency or responsibility to fulfill our mission of bringing community and belonging to everyone in the world. The Solidarity Award is meant to complement the efforts of our users, moderators, and employees at Reddit by enabling community-wide charitable giving during a time of great need.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;A Heads Up:&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;The team at Reddit worked quickly to enable the Solidarity Award. As with all new things at this scale, we are keeping an eye out for any bugs and issues that may arise, and will update the experience accordingly.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;From Reddit to all of our users:&lt;/strong&gt; Stay safe, be vigilant, and take care of one another.&lt;/p&gt;\n\n&lt;p&gt;*&lt;em&gt;Reddit is covering the transaction fees associated with the purchase of the Solidarity Award&lt;/em&gt;&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/GNb-heqOnXBKjrihGyzTP_xUAUeg_fgVbchNY-mIsm8.jpg?auto=webp&amp;s=cde885f09411d4855ba0557577bc3797a5a8af1a", "width": 600, "height": 315}, "resolutions": [{"url": "https://external-preview.redd.it/GNb-heqOnXBKjrihGyzTP_xUAUeg_fgVbchNY-mIsm8.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=ce5b803439ad780d41fc63d178153e4634ce0220", "width": 108, "height": 56}, {"url": "https://external-preview.redd.it/GNb-heqOnXBKjrihGyzTP_xUAUeg_fgVbchNY-mIsm8.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=3e352c224f57e9d5f4abc7f91e1bbc1f821f552b", "width": 216, "height": 113}, {"url": "https://external-preview.redd.it/GNb-heqOnXBKjrihGyzTP_xUAUeg_fgVbchNY-mIsm8.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=e4b7e6f2d42ba4ea58486d4b982a96774a446ac1", "width": 320, "height": 168}], "variants": {}, "id": "-c2HlyAbxiWJ3c9ugdE9jA08gOSgFPm0b9T5NtDNenk"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "fub7xo", "is_robot_indexable": true, "report_reasons": null, "author": "plgrmonedge", "discussion_type": null, "num_comments": 2691, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/fub7xo/introducing_the_solidarity_award_a_100/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/fub7xo/introducing_the_solidarity_award_a_100/", "subreddit_subscribers": *********, "created_utc": 1585930496.0, "num_crossposts": 72, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "If you’ve participated in Reddit’s April Fools’ Day tradition before, you'll know that this is the point where we normally share a confusing/cryptic message before pointing you toward some weird experience that we’ve created for your enjoyment.\n\nWhile we still plan to do that, we think it’s important to acknowledge that this year, things feel quite a bit different. The world is experiencing a moment of incredible uncertainty and stress; and throughout this time, it’s become even more clear how valuable Reddit is to millions of people looking for community, a place to seek and share information, provide support to one another, or simply to escape the reality of our collective ‘new normal.’\n\nOver the past 5 years at Reddit, April Fools’ Day has emerged as a time for us to create and discover new things with our community (that’s all of you). It's also a chance for us to celebrate you. Reddit only succeeds because millions of humans come together each day to make this collective system work. We create a project each April Fools’ Day to say thank you, and think it’s important to continue that tradition this year too. We hope this year’s experience will provide some insight and moments of delight during this strange and difficult time.\n\nWith that said, as promised:\n\n&gt;*What makes you human?*  \n&gt;  \n&gt;*Can you recognize it in others?*  \n&gt;  \n&gt;*Are you sure?*\n\nVisit r/Imposter in your browser, [iOS](https://apps.apple.com/us/app/reddit/id1064216828), and [Android](https://play.google.com/store/apps/details?id=com.reddit.frontpage).\n\nHave fun and be safe,\n\nThe Reddit Admins.", "author_fullname": "t2_5wqa4", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Imposter", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_ft3e3q", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.75, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 26896, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 26896, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1585759697.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;If you’ve participated in Reddit’s April Fools’ Day tradition before, you&amp;#39;ll know that this is the point where we normally share a confusing/cryptic message before pointing you toward some weird experience that we’ve created for your enjoyment.&lt;/p&gt;\n\n&lt;p&gt;While we still plan to do that, we think it’s important to acknowledge that this year, things feel quite a bit different. The world is experiencing a moment of incredible uncertainty and stress; and throughout this time, it’s become even more clear how valuable Reddit is to millions of people looking for community, a place to seek and share information, provide support to one another, or simply to escape the reality of our collective ‘new normal.’&lt;/p&gt;\n\n&lt;p&gt;Over the past 5 years at Reddit, April Fools’ Day has emerged as a time for us to create and discover new things with our community (that’s all of you). It&amp;#39;s also a chance for us to celebrate you. Reddit only succeeds because millions of humans come together each day to make this collective system work. We create a project each April Fools’ Day to say thank you, and think it’s important to continue that tradition this year too. We hope this year’s experience will provide some insight and moments of delight during this strange and difficult time.&lt;/p&gt;\n\n&lt;p&gt;With that said, as promised:&lt;/p&gt;\n\n&lt;blockquote&gt;\n&lt;p&gt;&lt;em&gt;What makes you human?&lt;/em&gt;  &lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Can you recognize it in others?&lt;/em&gt;  &lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Are you sure?&lt;/em&gt;&lt;/p&gt;\n&lt;/blockquote&gt;\n\n&lt;p&gt;Visit &lt;a href=\"/r/Imposter\"&gt;r/Imposter&lt;/a&gt; in your browser, &lt;a href=\"https://apps.apple.com/us/app/reddit/id1064216828\"&gt;iOS&lt;/a&gt;, and &lt;a href=\"https://play.google.com/store/apps/details?id=com.reddit.frontpage\"&gt;Android&lt;/a&gt;.&lt;/p&gt;\n\n&lt;p&gt;Have fun and be safe,&lt;/p&gt;\n\n&lt;p&gt;The Reddit Admins.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/Z06HXHvJRlF7Rd4v3aw29HFGtoZWZ_gaEKmc2xevsPM.jpg?auto=webp&amp;s=a75b2876fac9d403ea38e8ec1b8c0325d4994925", "width": 1200, "height": 630}, "resolutions": [{"url": "https://external-preview.redd.it/Z06HXHvJRlF7Rd4v3aw29HFGtoZWZ_gaEKmc2xevsPM.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=4fb8231f325bee40a064e7d28df164f677e61f65", "width": 108, "height": 56}, {"url": "https://external-preview.redd.it/Z06HXHvJRlF7Rd4v3aw29HFGtoZWZ_gaEKmc2xevsPM.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=97bdc0e8df84e882c92122baed2b76db9bb6a56a", "width": 216, "height": 113}, {"url": "https://external-preview.redd.it/Z06HXHvJRlF7Rd4v3aw29HFGtoZWZ_gaEKmc2xevsPM.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=89e584bef5490e9152ae1344f063c8ccb09fd0b0", "width": 320, "height": 168}, {"url": "https://external-preview.redd.it/Z06HXHvJRlF7Rd4v3aw29HFGtoZWZ_gaEKmc2xevsPM.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=d74e400d8a2e95a04f47968cd0188d6c309d42f9", "width": 640, "height": 336}, {"url": "https://external-preview.redd.it/Z06HXHvJRlF7Rd4v3aw29HFGtoZWZ_gaEKmc2xevsPM.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=19f1a8527f1e3a2a1f479f0726e2edae5a846238", "width": 960, "height": 504}, {"url": "https://external-preview.redd.it/Z06HXHvJRlF7Rd4v3aw29HFGtoZWZ_gaEKmc2xevsPM.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=827a778ebae73641dbf0823ae296e7bada3fede8", "width": 1080, "height": 567}], "variants": {}, "id": "g2V_g5zVFX3-bqx0aKoXIjw4gUM8exgLPrNlGAJgVi8"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "ft3e3q", "is_robot_indexable": true, "report_reasons": null, "author": "powerlanguage", "discussion_type": null, "num_comments": 1495, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/ft3e3q/imposter/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/ft3e3q/imposter/", "subreddit_subscribers": *********, "created_utc": 1585759697.0, "num_crossposts": 27, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "If you’re looking for an opinion on anything — the most underrated TV show of the nineties; the very best drugstore mascara; the most athletic NFL player of all-time — there’s no better place to get honest answers and gauge consensus, than on Reddit.\n\nToday, in an effort to elevate Reddit’s diverse opinion-based content, we’re excited to introduce Polls: a brand new post type that encourages redditors to share their opinion via voting. We’ve been testing Polls with a dozen communities over the past couple months, and have gotten a lot of great feedback. We are excited to now release this post type to everyone!\n\n**Why Polls?**\n\nIt can sometimes be tough for new redditors and lurkers to know where to start on Reddit, , and to feel a sense of community. We believe a simple post type that reduces the posting barrier will make it easier than ever for everyone to contribute to their favorite communities and engage in different ways.\n\nHere’s a look at some of our recent test polls\n\n[Viewing the results of a poll on new Reddit](https://preview.redd.it/z45s3p889no41.png?width=2356&amp;format=png&amp;auto=webp&amp;s=cbd040f5e79e93b503c0a2d61378470c478e2305)\n\n&amp;#x200B;\n\n[Trunks...the people have spoken](https://preview.redd.it/ptccsbp89no41.png?width=1181&amp;format=png&amp;auto=webp&amp;s=4c561ac017d3db3cfed95a49b5ec5dba5cc751d0)\n\n**Platform Support**\n\n* iOS: Supports poll creation and voting\n* Android: Supports poll creation and voting **(EDIT: there is a bug on old versions of Android that cause the app to crash for some redditors when they vote. Updating the app to the new version will fix it.)**\n* New Reddit (web): Supports poll creation and voting\n* Old Reddit (web): Does not support creation. At the bottom of a poll, redditors will see a link to view the poll. Clicking the link will open a new tab where they can view results and vote in the poll\n* Mobile web: Supports voting. No plans for poll creation support\n\nAnd now a poll...\n\n**With everything going on in the world, how are you feeling?**\n\n[View Poll](https://www.reddit.com/poll/fo7p5b)", "author_fullname": "t2_o30pnrf", "saved": false, "mod_reason_title": null, "gilded": 1, "clicked": false, "title": "Introducing Reddit Polls, An All-New Post Type", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": 100, "top_awarded_type": null, "hide_score": false, "media_metadata": {"z45s3p889no41": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 77, "x": 108, "u": "https://preview.redd.it/z45s3p889no41.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=488ced3444198af9a7b2764ba5ef7bc4740ad328"}, {"y": 154, "x": 216, "u": "https://preview.redd.it/z45s3p889no41.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=89145438c17e8f1d0d8e9ad46fe95245e0dc6844"}, {"y": 229, "x": 320, "u": "https://preview.redd.it/z45s3p889no41.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=c779a13ce60bf27862da3d9d9b65d379759047ce"}, {"y": 458, "x": 640, "u": "https://preview.redd.it/z45s3p889no41.png?width=640&amp;crop=smart&amp;auto=webp&amp;s=4daa684f85e49fc35b523d120f64e941b59341d7"}, {"y": 687, "x": 960, "u": "https://preview.redd.it/z45s3p889no41.png?width=960&amp;crop=smart&amp;auto=webp&amp;s=d017c529631f1e4b6c28dd1a459460210ffa2eb3"}, {"y": 773, "x": 1080, "u": "https://preview.redd.it/z45s3p889no41.png?width=1080&amp;crop=smart&amp;auto=webp&amp;s=e9003760d68ddff62106760e7ab94665c4b5f953"}], "s": {"y": 1688, "x": 2356, "u": "https://preview.redd.it/z45s3p889no41.png?width=2356&amp;format=png&amp;auto=webp&amp;s=cbd040f5e79e93b503c0a2d61378470c478e2305"}, "id": "z45s3p889no41"}, "ptccsbp89no41": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 89, "x": 108, "u": "https://preview.redd.it/ptccsbp89no41.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=51610b781bf627713cb8e628973a3dec292dc1fe"}, {"y": 178, "x": 216, "u": "https://preview.redd.it/ptccsbp89no41.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=3b806416f83f66f26956fdc5a998dbea74afd987"}, {"y": 264, "x": 320, "u": "https://preview.redd.it/ptccsbp89no41.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=82b49f66050e971166db9708bba35bbbadbca7f7"}, {"y": 528, "x": 640, "u": "https://preview.redd.it/ptccsbp89no41.png?width=640&amp;crop=smart&amp;auto=webp&amp;s=a6db051b466f6cbae9ade781153c32ae87e1b74c"}, {"y": 793, "x": 960, "u": "https://preview.redd.it/ptccsbp89no41.png?width=960&amp;crop=smart&amp;auto=webp&amp;s=59d615c9115cd866094cf1b0d87f0989aff6d778"}, {"y": 892, "x": 1080, "u": "https://preview.redd.it/ptccsbp89no41.png?width=1080&amp;crop=smart&amp;auto=webp&amp;s=21e7740d8705a26be97444a004062afc9841e202"}], "s": {"y": 976, "x": 1181, "u": "https://preview.redd.it/ptccsbp89no41.png?width=1181&amp;format=png&amp;auto=webp&amp;s=4c561ac017d3db3cfed95a49b5ec5dba5cc751d0"}, "id": "ptccsbp89no41"}}, "name": "t3_fo7p5b", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.77, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 67909, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": 140, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 67909, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "https://a.thumbs.redditmedia.com/N-frnMqRYp_XiaGds7kPbHf82a876RNKSQ1iw0LzHR0.jpg", "edited": 1585091424.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1585066228.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;If you’re looking for an opinion on anything — the most underrated TV show of the nineties; the very best drugstore mascara; the most athletic NFL player of all-time — there’s no better place to get honest answers and gauge consensus, than on Reddit.&lt;/p&gt;\n\n&lt;p&gt;Today, in an effort to elevate Reddit’s diverse opinion-based content, we’re excited to introduce Polls: a brand new post type that encourages redditors to share their opinion via voting. We’ve been testing Polls with a dozen communities over the past couple months, and have gotten a lot of great feedback. We are excited to now release this post type to everyone!&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Why Polls?&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;It can sometimes be tough for new redditors and lurkers to know where to start on Reddit, , and to feel a sense of community. We believe a simple post type that reduces the posting barrier will make it easier than ever for everyone to contribute to their favorite communities and engage in different ways.&lt;/p&gt;\n\n&lt;p&gt;Here’s a look at some of our recent test polls&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/z45s3p889no41.png?width=2356&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=cbd040f5e79e93b503c0a2d61378470c478e2305\"&gt;Viewing the results of a poll on new Reddit&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&amp;#x200B;&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/ptccsbp89no41.png?width=1181&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=4c561ac017d3db3cfed95a49b5ec5dba5cc751d0\"&gt;Trunks...the people have spoken&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Platform Support&lt;/strong&gt;&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;iOS: Supports poll creation and voting&lt;/li&gt;\n&lt;li&gt;Android: Supports poll creation and voting &lt;strong&gt;(EDIT: there is a bug on old versions of Android that cause the app to crash for some redditors when they vote. Updating the app to the new version will fix it.)&lt;/strong&gt;&lt;/li&gt;\n&lt;li&gt;New Reddit (web): Supports poll creation and voting&lt;/li&gt;\n&lt;li&gt;Old Reddit (web): Does not support creation. At the bottom of a poll, redditors will see a link to view the poll. Clicking the link will open a new tab where they can view results and vote in the poll&lt;/li&gt;\n&lt;li&gt;Mobile web: Supports voting. No plans for poll creation support&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;And now a poll...&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;With everything going on in the world, how are you feeling?&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://www.reddit.com/poll/fo7p5b\"&gt;View Poll&lt;/a&gt;&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "fo7p5b", "is_robot_indexable": true, "report_reasons": null, "author": "LanterneRougeOG", "discussion_type": null, "num_comments": 3817, "send_replies": false, "contest_mode": false, "poll_data": {"prediction_status": null, "total_stake_amount": null, "voting_end_timestamp": 1585325428313, "options": [{"text": "Hopeful", "vote_count": 1675, "id": "1883"}, {"text": "Inspired by others", "vote_count": 343, "id": "1884"}, {"text": "Anxious", "vote_count": 3059, "id": "1885"}, {"text": "Angry", "vote_count": 652, "id": "1886"}, {"text": "Meh...mostly bored", "vote_count": 2839, "id": "1887"}, {"text": "All of the above", "vote_count": 2681, "id": "1888"}], "vote_updates_remained": null, "is_prediction": false, "resolved_option_id": null, "user_won_amount": null, "user_selection": null, "total_vote_count": 11249, "tournament_id": null}, "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/fo7p5b/introducing_reddit_polls_an_allnew_post_type/", "stickied": false, "mod_reports": [], "url": "https://www.reddit.com/r/announcements/comments/fo7p5b/introducing_reddit_polls_an_allnew_post_type/", "subreddit_subscribers": *********, "created_utc": 1585066228.0, "num_crossposts": 49, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "", "user_reports": [], "saved": false, "mod_reason_title": null, "gilded": 1, "clicked": false, "title": "Announcing our partnership and AMA with Crisis Text Line", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": 140, "top_awarded_type": null, "hide_score": false, "name": "t3_fdi1rw", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.73, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 15682, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": 140, "author_flair_template_id": null, "is_original_content": false, "author_fullname": "t2_y6bok2c", "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 15682, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "https://b.thumbs.redditmedia.com/KH2o_q4MXufUdRChsfwLtl6gnEnT8MJtZn3Tyj5ZmBU.jpg", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "link", "content_categories": null, "is_self": false, "mod_note": null, "crosspost_parent_list": [{"approved_at_utc": null, "subreddit": "modnews", "selftext": "***\\[Edit\\] This is now*** [***live***](https://www.reddit.com/r/modnews/comments/fdh4rr/announcing_our_partnership_and_ama_with_crisis/fkbzsmv)\n\nHi Mods,\n\nAs we all know, Reddit provides a home for an infinite number of people and communities. From awws and memes, to politics, fantasy leagues, and book clubs, people have created communities for just about everything. There are also entire communities dedicated solely to finding someone to talk to like r/KindVoice and r/CasualConversation. But it’s not all funny memes and gaming—as an anonymous platform, Reddit is also a space for people to express the most vulnerable parts of themselves.\n\nPeople on Reddit find help in support communities that address a broad range of challenges from quitting smoking or drinking, struggling to get pregnant, or addressing abuse, anxiety, depression, or thoughts of suicide. Even communities that don’t directly relate to serious topics can get deep into serious issues, and the person you turn to in a time of need may be someone you bonded with over a game, a shared sense of humor, or the same taste in music.\n\nWhen you see a post or comment about suicidal feelings in a community, it can be overwhelming. Especially if you’re a moderator in that community, and feel a sense of responsibility for both the people in your community and making sure it's the type of place you want it to be.\n\nHere at Reddit, we’ve been working on finding a thoughtful approach to self-harm and suicide response that does a few key things:\n\n1. Connects people considering suicide or serious self-harm with with trusted resources and real-time support that can help them as soon as possible.\n2. Takes the pressure of responding to people considering suicide or serious self-harm off of moderators and redditors.\n3. Continues to uphold our high standards for protecting and respecting user privacy and anonymity.\n\nTo help us with that new approach, today we’re announcing a partnership with [Crisis Text Line](https://www.crisistextline.org/) to provide redditors who may be considering serious self-harm or suicide with free, confidential, 24/7 support from trained Crisis Counselors.\n\nCrisis Text Line is a free, confidential, text-based support line for people in the U.S. who may be struggling with any type of mental health crisis. Their Crisis Counselors are trained to put people at ease and help them make a plan to stay safe. If you’d like to learn more about Crisis Text Line, they have a helpful [summary video of their work](https://www.crisistextline.org/) on their website and the complete story of how they were founded was covered in-depth in the [New Yorker article, *R U There?*](https://www.newyorker.com/magazine/2015/02/09/r-u)\n\n**How It Will Work**\n\nMoving forward, when you’re worried about someone in your community, or anywhere on Reddit, you can let us know in two ways:\n\n1. Report the specific post or comment that worried you and select, *Someone is considering suicide or serious self-harm.*\n2. Visit the person’s profile and select, *Get them help and support.* (If you’re using Reddit on the web, click *More Options* first.)\n\nWe’ll reach out to tell the person a fellow redditor is worried about them and put them in touch with Crisis Text Line’s trained Crisis Counselors. Don’t worry, we’ll have some rate-limiting behind the scenes so people in crisis won’t get multiple messages in short succession, regardless of the amount of requests we receive. And because responding to someone who is considering suicide or serious self-harm can bring up hard emotions or may be triggering, Crisis Text Line is also available to people who are reporting someone. This new flow will be launching next week.\n\nHere’s what it will look like:\n\n&amp;#x200B;\n\nhttps://preview.redd.it/7h5gpnc26pk41.png?width=375&amp;format=png&amp;auto=webp&amp;s=e3af1518a61ffbc2f5d001db5fce32b57ab41256\n\nhttps://preview.redd.it/9205nrc26pk41.png?width=375&amp;format=png&amp;auto=webp&amp;s=de45d0e3d76239f45c8ab61c4230be3b28f0981d\n\nAs part of our partnership, we’re hosting a joint AMA between Reddit’s group product manager of safety u/jkohhey and Crisis Text Line’s Co-Founder &amp; Chief Data Scientist, Bob Filbin u/Crisis_Text_Line, to answer questions about their approach to online suicide response, how the partnership will work, and what this all means for you and your communities.\n\nHere’s a little bit more about Bob:As Co-Founder &amp; Chief Data Scientist of Crisis Text Line, Bob leads all things data including developing new avenues of data collection, storing data in a way that makes it universally accessible, and leading the Data, Ethics, and Research Advisory Board. Bob has given keynote lectures on using data to drive action at the YMCA National CIOs Conference, American Association of Suicidology Conference, MIT Solve, and SXSW. While he is not permitted to share the details, Bob is occasionally tapped by the FBI to provide insight in data science, AI, ethics, and trends. Bob graduated from Colgate University and has an MA in Quantitative Methods from Columbia.\n\n&amp;#x200B;\n\n*Edit: formatting*\n\n*Edit 2: This flow will be launching next week*", "author_fullname": "t2_y6bok2c", "saved": false, "mod_reason_title": null, "gilded": 4, "clicked": false, "title": "Announcing our partnership and AMA with Crisis Text Line", "link_flair_richtext": [], "subreddit_name_prefixed": "r/modnews", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": 140, "top_awarded_type": null, "hide_score": false, "media_metadata": {"9205nrc26pk41": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 216, "x": 108, "u": "https://preview.redd.it/9205nrc26pk41.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=1386db3d67f01918b102800cb67c6f6a4e007703"}, {"y": 432, "x": 216, "u": "https://preview.redd.it/9205nrc26pk41.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=0ecc3d13fbc45583bf54c1185b9f05c1abc723e7"}, {"y": 640, "x": 320, "u": "https://preview.redd.it/9205nrc26pk41.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=40179025df2c78dd50dbd5325c7cf086a34b595d"}], "s": {"y": 812, "x": 375, "u": "https://preview.redd.it/9205nrc26pk41.png?width=375&amp;format=png&amp;auto=webp&amp;s=de45d0e3d76239f45c8ab61c4230be3b28f0981d"}, "id": "9205nrc26pk41"}, "7h5gpnc26pk41": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 216, "x": 108, "u": "https://preview.redd.it/7h5gpnc26pk41.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=ece212431c4e7595b4529c6a8fd2d25d9e476765"}, {"y": 432, "x": 216, "u": "https://preview.redd.it/7h5gpnc26pk41.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=f596c30ac1ce83a23fd8342d6a384371c7afc6bc"}, {"y": 640, "x": 320, "u": "https://preview.redd.it/7h5gpnc26pk41.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=6be44f898c89afb97db3e7f2787ee73be6a99595"}], "s": {"y": 812, "x": 375, "u": "https://preview.redd.it/7h5gpnc26pk41.png?width=375&amp;format=png&amp;auto=webp&amp;s=e3af1518a61ffbc2f5d001db5fce32b57ab41256"}, "id": "7h5gpnc26pk41"}}, "name": "t3_fdh4rr", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.83, "author_flair_background_color": null, "subreddit_type": "restricted", "ups": 4056, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": 140, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 4056, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "self", "edited": 1584043129.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1583345717.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.modnews", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;[Edit] This is now&lt;/em&gt;&lt;/strong&gt; &lt;a href=\"https://www.reddit.com/r/modnews/comments/fdh4rr/announcing_our_partnership_and_ama_with_crisis/fkbzsmv\"&gt;&lt;strong&gt;&lt;em&gt;live&lt;/em&gt;&lt;/strong&gt;&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;Hi Mods,&lt;/p&gt;\n\n&lt;p&gt;As we all know, Reddit provides a home for an infinite number of people and communities. From awws and memes, to politics, fantasy leagues, and book clubs, people have created communities for just about everything. There are also entire communities dedicated solely to finding someone to talk to like &lt;a href=\"/r/KindVoice\"&gt;r/KindVoice&lt;/a&gt; and &lt;a href=\"/r/CasualConversation\"&gt;r/CasualConversation&lt;/a&gt;. But it’s not all funny memes and gaming—as an anonymous platform, Reddit is also a space for people to express the most vulnerable parts of themselves.&lt;/p&gt;\n\n&lt;p&gt;People on Reddit find help in support communities that address a broad range of challenges from quitting smoking or drinking, struggling to get pregnant, or addressing abuse, anxiety, depression, or thoughts of suicide. Even communities that don’t directly relate to serious topics can get deep into serious issues, and the person you turn to in a time of need may be someone you bonded with over a game, a shared sense of humor, or the same taste in music.&lt;/p&gt;\n\n&lt;p&gt;When you see a post or comment about suicidal feelings in a community, it can be overwhelming. Especially if you’re a moderator in that community, and feel a sense of responsibility for both the people in your community and making sure it&amp;#39;s the type of place you want it to be.&lt;/p&gt;\n\n&lt;p&gt;Here at Reddit, we’ve been working on finding a thoughtful approach to self-harm and suicide response that does a few key things:&lt;/p&gt;\n\n&lt;ol&gt;\n&lt;li&gt;Connects people considering suicide or serious self-harm with with trusted resources and real-time support that can help them as soon as possible.&lt;/li&gt;\n&lt;li&gt;Takes the pressure of responding to people considering suicide or serious self-harm off of moderators and redditors.&lt;/li&gt;\n&lt;li&gt;Continues to uphold our high standards for protecting and respecting user privacy and anonymity.&lt;/li&gt;\n&lt;/ol&gt;\n\n&lt;p&gt;To help us with that new approach, today we’re announcing a partnership with &lt;a href=\"https://www.crisistextline.org/\"&gt;Crisis Text Line&lt;/a&gt; to provide redditors who may be considering serious self-harm or suicide with free, confidential, 24/7 support from trained Crisis Counselors.&lt;/p&gt;\n\n&lt;p&gt;Crisis Text Line is a free, confidential, text-based support line for people in the U.S. who may be struggling with any type of mental health crisis. Their Crisis Counselors are trained to put people at ease and help them make a plan to stay safe. If you’d like to learn more about Crisis Text Line, they have a helpful &lt;a href=\"https://www.crisistextline.org/\"&gt;summary video of their work&lt;/a&gt; on their website and the complete story of how they were founded was covered in-depth in the &lt;a href=\"https://www.newyorker.com/magazine/2015/02/09/r-u\"&gt;New Yorker article, &lt;em&gt;R U There?&lt;/em&gt;&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;How It Will Work&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;Moving forward, when you’re worried about someone in your community, or anywhere on Reddit, you can let us know in two ways:&lt;/p&gt;\n\n&lt;ol&gt;\n&lt;li&gt;Report the specific post or comment that worried you and select, &lt;em&gt;Someone is considering suicide or serious self-harm.&lt;/em&gt;&lt;/li&gt;\n&lt;li&gt;Visit the person’s profile and select, &lt;em&gt;Get them help and support.&lt;/em&gt; (If you’re using Reddit on the web, click &lt;em&gt;More Options&lt;/em&gt; first.)&lt;/li&gt;\n&lt;/ol&gt;\n\n&lt;p&gt;We’ll reach out to tell the person a fellow redditor is worried about them and put them in touch with Crisis Text Line’s trained Crisis Counselors. Don’t worry, we’ll have some rate-limiting behind the scenes so people in crisis won’t get multiple messages in short succession, regardless of the amount of requests we receive. And because responding to someone who is considering suicide or serious self-harm can bring up hard emotions or may be triggering, Crisis Text Line is also available to people who are reporting someone. This new flow will be launching next week.&lt;/p&gt;\n\n&lt;p&gt;Here’s what it will look like:&lt;/p&gt;\n\n&lt;p&gt;&amp;#x200B;&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/7h5gpnc26pk41.png?width=375&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=e3af1518a61ffbc2f5d001db5fce32b57ab41256\"&gt;https://preview.redd.it/7h5gpnc26pk41.png?width=375&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=e3af1518a61ffbc2f5d001db5fce32b57ab41256&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/9205nrc26pk41.png?width=375&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=de45d0e3d76239f45c8ab61c4230be3b28f0981d\"&gt;https://preview.redd.it/9205nrc26pk41.png?width=375&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=de45d0e3d76239f45c8ab61c4230be3b28f0981d&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;As part of our partnership, we’re hosting a joint AMA between Reddit’s group product manager of safety &lt;a href=\"/u/jkohhey\"&gt;u/jkohhey&lt;/a&gt; and Crisis Text Line’s Co-Founder &amp;amp; Chief Data Scientist, Bob Filbin &lt;a href=\"/u/Crisis_Text_Line\"&gt;u/Crisis_Text_Line&lt;/a&gt;, to answer questions about their approach to online suicide response, how the partnership will work, and what this all means for you and your communities.&lt;/p&gt;\n\n&lt;p&gt;Here’s a little bit more about Bob:As Co-Founder &amp;amp; Chief Data Scientist of Crisis Text Line, Bob leads all things data including developing new avenues of data collection, storing data in a way that makes it universally accessible, and leading the Data, Ethics, and Research Advisory Board. Bob has given keynote lectures on using data to drive action at the YMCA National CIOs Conference, American Association of Suicidology Conference, MIT Solve, and SXSW. While he is not permitted to share the details, Bob is occasionally tapped by the FBI to provide insight in data science, AI, ethics, and trends. Bob graduated from Colgate University and has an MA in Quantitative Methods from Columbia.&lt;/p&gt;\n\n&lt;p&gt;&amp;#x200B;&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Edit: formatting&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Edit 2: This flow will be launching next week&lt;/em&gt;&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?auto=webp&amp;s=d48cf4abba4216020dd968b6c65ed4c0400ff5eb", "width": 667, "height": 667}, "resolutions": [{"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=5d17a9dc5a031863b96deb8ae85e14cb1cf48cdf", "width": 108, "height": 108}, {"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=4beb1eaaeedce64a9cd9eb22c682a6934cbe824d", "width": 216, "height": 216}, {"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=354c8755b5316da7288edc92c4821a13d144062a", "width": 320, "height": 320}, {"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=017736ec3cc402d9a59767efc3e7b724fee0e09a", "width": 640, "height": 640}], "variants": {}, "id": "GSdihJbbgBEDj_qvhQQ0oINnE38MCovkszIiMtzwpkk"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2ro4m", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "fdh4rr", "is_robot_indexable": true, "report_reasons": null, "author": "jkohhey", "discussion_type": null, "num_comments": 955, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/modnews/comments/fdh4rr/announcing_our_partnership_and_ama_with_crisis/", "stickied": false, "url": "https://www.reddit.com/r/modnews/comments/fdh4rr/announcing_our_partnership_and_ama_with_crisis/", "subreddit_subscribers": 228452, "created_utc": 1583345717.0, "num_crossposts": 52, "media": null, "is_video": false}], "created": 1583349287.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.modnews", "allow_live_comments": true, "selftext_html": null, "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "url_overridden_by_dest": "/r/modnews/comments/fdh4rr/announcing_our_partnership_and_ama_with_crisis/", "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?auto=webp&amp;s=d48cf4abba4216020dd968b6c65ed4c0400ff5eb", "width": 667, "height": 667}, "resolutions": [{"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=5d17a9dc5a031863b96deb8ae85e14cb1cf48cdf", "width": 108, "height": 108}, {"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=4beb1eaaeedce64a9cd9eb22c682a6934cbe824d", "width": 216, "height": 216}, {"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=354c8755b5316da7288edc92c4821a13d144062a", "width": 320, "height": 320}, {"url": "https://external-preview.redd.it/j_mwjX8nYUxWt_qPlOPXzOAKAyIAjjdofsp-HilzPuQ.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=017736ec3cc402d9a59767efc3e7b724fee0e09a", "width": 640, "height": 640}], "variants": {}, "id": "GSdihJbbgBEDj_qvhQQ0oINnE38MCovkszIiMtzwpkk"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "fdi1rw", "is_robot_indexable": true, "report_reasons": null, "author": "jkohhey", "discussion_type": null, "num_comments": 1, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "crosspost_parent": "t3_fdh4rr", "author_flair_text_color": null, "permalink": "/r/announcements/comments/fdi1rw/announcing_our_partnership_and_ama_with_crisis/", "stickied": false, "url": "/r/modnews/comments/fdh4rr/announcing_our_partnership_and_ama_with_crisis/", "subreddit_subscribers": *********, "created_utc": 1583349287.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "**TL;DR: Today we published our 2019** [**Transparency Report**](https://www.redditinc.com/policies/transparency-report-2019)**. I’ll stick around to answer your questions about the report (and other topics) in the comments.**\n\nHi all,\n\nIt’s that time of year again when we share Reddit’s annual transparency report.\n\nWe share this report each year because you have a right to know how user data is being managed by Reddit, and how it’s both shared and not shared with government and non-government parties.\n\nYou’ll find information on content removed from Reddit and requests for user information. This year, we’ve expanded the report to include new data—specifically, a breakdown of content policy removals, content manipulation removals, subreddit removals, and subreddit quarantines.\n\n## By the numbers\n\nSince the [full report](https://www.redditinc.com/policies/transparency-report-2019) is rather long, I’ll call out a few stats below:\n\n**ADMIN REMOVALS**\n\n* In 2019, we removed **\\~53M** pieces of content in total, mostly for spam and content manipulation (e.g. brigading and vote cheating), exclusive of legal/copyright removals, which we track separately.\n* For [Content Policy](https://www.redditinc.com/policies/content-policy) violations, we removed\n   * **222k** **pieces of content**,\n   * **55.9k accounts**, and\n   * **21.9k** **subreddits** (**87%** of which were removed for being unmoderated).\n* Additionally, we quarantined **256 subreddits**.\n\n**LEGAL REMOVALS**\n\n* Reddit received **110 requests** from government entities to remove content, of which we complied with **37.3%**.\n* In 2019 we removed about **5x more** content for copyright infringement than in 2018, largely due to copyright notices for adult-entertainment and notices targeting pieces of content that had already been removed.\n\n**REQUESTS FOR USER INFORMATION**\n\n* We received a total of **772 requests** for user account information from law enforcement and government entities.\n   * **366** of these were emergency disclosure requests, mostly from US law enforcement (**68%** of which we complied with).\n   * **406** were non-emergency requests (**73%** of which we complied with); most were US subpoenas.\n   * Reddit received an additional **224 requests** to temporarily preserve certain user account information (**86%** of which we complied with).\n* Note: We carefully review each request for compliance with applicable laws and regulations. If we determine that a request is not legally valid, Reddit will challenge or reject it. (You can read more in our [Privacy Policy](https://www.redditinc.com/policies/privacy-policy) and [Guidelines for Law Enforcement](https://www.redditinc.com/policies/guidelines-for-law-enforcement).)\n\n## While I have your attention...\n\nI’d like to share an update about our thinking around quarantined communities.\n\nWhen we [expanded our quarantine policy](https://www.reddit.com/r/announcements/comments/9jf8nh/revamping_the_quarantine_function/), we created an appeals process for sanctioned communities. One of the goals was to “force subscribers to reconsider their behavior and incentivize moderators to make changes.” While the policy attempted to hold moderators more accountable for enforcing healthier rules and norms, it didn’t address the role that each member plays in the health of their community.\n\nToday, we’re making an update to address this gap: Users who consistently upvote policy-breaking content within quarantined communities will receive automated warnings, followed by further consequences like a temporary or permanent suspension. We hope this will encourage healthier behavior across these communities.\n\n## If you’ve read this far\n\nIn addition to this report, we share news throughout the year from teams across Reddit, and if you like posts about what we’re doing, you can stay up to date and talk to our teams in r/RedditSecurity, r/ModNews, r/redditmobile, and r/changelog.\n\n**As usual, I’ll be sticking around to answer your questions in the comments. AMA.**\n\nUpdate: I'm off for now. Thanks for questions, everyone.", "author_fullname": "t2_1w72", "saved": false, "mod_reason_title": null, "gilded": 4, "clicked": false, "title": "Spring forward… into Reddit’s 2019 transparency report", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_f8y9nx", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.65, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 36567, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 36567, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": 1582588922.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1582578742.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;&lt;strong&gt;TL;DR: Today we published our 2019&lt;/strong&gt; &lt;a href=\"https://www.redditinc.com/policies/transparency-report-2019\"&gt;&lt;strong&gt;Transparency Report&lt;/strong&gt;&lt;/a&gt;&lt;strong&gt;. I’ll stick around to answer your questions about the report (and other topics) in the comments.&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;Hi all,&lt;/p&gt;\n\n&lt;p&gt;It’s that time of year again when we share Reddit’s annual transparency report.&lt;/p&gt;\n\n&lt;p&gt;We share this report each year because you have a right to know how user data is being managed by Reddit, and how it’s both shared and not shared with government and non-government parties.&lt;/p&gt;\n\n&lt;p&gt;You’ll find information on content removed from Reddit and requests for user information. This year, we’ve expanded the report to include new data—specifically, a breakdown of content policy removals, content manipulation removals, subreddit removals, and subreddit quarantines.&lt;/p&gt;\n\n&lt;h2&gt;By the numbers&lt;/h2&gt;\n\n&lt;p&gt;Since the &lt;a href=\"https://www.redditinc.com/policies/transparency-report-2019\"&gt;full report&lt;/a&gt; is rather long, I’ll call out a few stats below:&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;ADMIN REMOVALS&lt;/strong&gt;&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;In 2019, we removed &lt;strong&gt;~53M&lt;/strong&gt; pieces of content in total, mostly for spam and content manipulation (e.g. brigading and vote cheating), exclusive of legal/copyright removals, which we track separately.&lt;/li&gt;\n&lt;li&gt;For &lt;a href=\"https://www.redditinc.com/policies/content-policy\"&gt;Content Policy&lt;/a&gt; violations, we removed\n\n&lt;ul&gt;\n&lt;li&gt;&lt;strong&gt;222k&lt;/strong&gt; &lt;strong&gt;pieces of content&lt;/strong&gt;,&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;55.9k accounts&lt;/strong&gt;, and&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;21.9k&lt;/strong&gt; &lt;strong&gt;subreddits&lt;/strong&gt; (&lt;strong&gt;87%&lt;/strong&gt; of which were removed for being unmoderated).&lt;/li&gt;\n&lt;/ul&gt;&lt;/li&gt;\n&lt;li&gt;Additionally, we quarantined &lt;strong&gt;256 subreddits&lt;/strong&gt;.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;&lt;strong&gt;LEGAL REMOVALS&lt;/strong&gt;&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Reddit received &lt;strong&gt;110 requests&lt;/strong&gt; from government entities to remove content, of which we complied with &lt;strong&gt;37.3%&lt;/strong&gt;.&lt;/li&gt;\n&lt;li&gt;In 2019 we removed about &lt;strong&gt;5x more&lt;/strong&gt; content for copyright infringement than in 2018, largely due to copyright notices for adult-entertainment and notices targeting pieces of content that had already been removed.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;&lt;strong&gt;REQUESTS FOR USER INFORMATION&lt;/strong&gt;&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;We received a total of &lt;strong&gt;772 requests&lt;/strong&gt; for user account information from law enforcement and government entities.\n\n&lt;ul&gt;\n&lt;li&gt;&lt;strong&gt;366&lt;/strong&gt; of these were emergency disclosure requests, mostly from US law enforcement (&lt;strong&gt;68%&lt;/strong&gt; of which we complied with).&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;406&lt;/strong&gt; were non-emergency requests (&lt;strong&gt;73%&lt;/strong&gt; of which we complied with); most were US subpoenas.&lt;/li&gt;\n&lt;li&gt;Reddit received an additional &lt;strong&gt;224 requests&lt;/strong&gt; to temporarily preserve certain user account information (&lt;strong&gt;86%&lt;/strong&gt; of which we complied with).&lt;/li&gt;\n&lt;/ul&gt;&lt;/li&gt;\n&lt;li&gt;Note: We carefully review each request for compliance with applicable laws and regulations. If we determine that a request is not legally valid, Reddit will challenge or reject it. (You can read more in our &lt;a href=\"https://www.redditinc.com/policies/privacy-policy\"&gt;Privacy Policy&lt;/a&gt; and &lt;a href=\"https://www.redditinc.com/policies/guidelines-for-law-enforcement\"&gt;Guidelines for Law Enforcement&lt;/a&gt;.)&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;h2&gt;While I have your attention...&lt;/h2&gt;\n\n&lt;p&gt;I’d like to share an update about our thinking around quarantined communities.&lt;/p&gt;\n\n&lt;p&gt;When we &lt;a href=\"https://www.reddit.com/r/announcements/comments/9jf8nh/revamping_the_quarantine_function/\"&gt;expanded our quarantine policy&lt;/a&gt;, we created an appeals process for sanctioned communities. One of the goals was to “force subscribers to reconsider their behavior and incentivize moderators to make changes.” While the policy attempted to hold moderators more accountable for enforcing healthier rules and norms, it didn’t address the role that each member plays in the health of their community.&lt;/p&gt;\n\n&lt;p&gt;Today, we’re making an update to address this gap: Users who consistently upvote policy-breaking content within quarantined communities will receive automated warnings, followed by further consequences like a temporary or permanent suspension. We hope this will encourage healthier behavior across these communities.&lt;/p&gt;\n\n&lt;h2&gt;If you’ve read this far&lt;/h2&gt;\n\n&lt;p&gt;In addition to this report, we share news throughout the year from teams across Reddit, and if you like posts about what we’re doing, you can stay up to date and talk to our teams in &lt;a href=\"/r/RedditSecurity\"&gt;r/RedditSecurity&lt;/a&gt;, &lt;a href=\"/r/ModNews\"&gt;r/ModNews&lt;/a&gt;, &lt;a href=\"/r/redditmobile\"&gt;r/redditmobile&lt;/a&gt;, and &lt;a href=\"/r/changelog\"&gt;r/changelog&lt;/a&gt;.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;As usual, I’ll be sticking around to answer your questions in the comments. AMA.&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;Update: I&amp;#39;m off for now. Thanks for questions, everyone.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?auto=webp&amp;s=f65608a0e50b80501b09dd4e6d4256a073bddaf7", "width": 1920, "height": 1080}, "resolutions": [{"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=f58daaa031b1949d7fba33ff04daa716f834c5fa", "width": 108, "height": 60}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=ef7c929673f1ddd3f2104d8d8eeb87c06e8c594b", "width": 216, "height": 121}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=1fb00ddb4a2d99782e7de7cde51676f0db742639", "width": 320, "height": 180}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=f7ed52b21d8431d206e4858a89b65eb9a4630955", "width": 640, "height": 360}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=b0580c69aeaa769a86051f67d7385a6c5bee2db4", "width": 960, "height": 540}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=747dd834ecf741b4de8a13d5a4f725220bd91edb", "width": 1080, "height": 607}], "variants": {}, "id": "kSMg4tuE5uFPfCQ-p6TNLozpkw7k_rxRNpv2tvuvhbY"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "f8y9nx", "is_robot_indexable": true, "report_reasons": null, "author": "spez", "discussion_type": null, "num_comments": 16166, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/f8y9nx/spring_forward_into_reddits_2019_transparency/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/f8y9nx/spring_forward_into_reddits_2019_transparency/", "subreddit_subscribers": *********, "created_utc": 1582578742.0, "num_crossposts": 60, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "", "user_reports": [], "saved": false, "mod_reason_title": null, "gilded": 4, "clicked": false, "title": "Suspected Campaign from Russia on Reddit", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_e75f07", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.84, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 76393, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "author_fullname": "t2_qromv", "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 76393, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "default", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "link", "content_categories": null, "is_self": false, "mod_note": null, "crosspost_parent_list": [{"approved_at_utc": null, "subreddit": "RedditSafety", "selftext": "We were recently made aware of a [post on Reddit](https://www.reddit.com/r/worldpolitics/comments/dkzlfc/officialsensitive_great_britain_is_practically/) that included leaked documents from the UK. We investigated this account and the accounts connected to it, and today we believe this was part of a campaign that has been reported as originating from Russia.\n\nEarlier this year Facebook discovered a [Russian campaign](https://about.fb.com/news/2019/05/more-cib-from-russia/) on its platform, which was further analyzed by the Atlantic Council and dubbed “[Secondary Infektion](https://medium.com/dfrlab/top-takes-suspected-russian-intelligence-operation-39212367d2f0).” Suspect accounts on Reddit were recently reported to us, along with indicators from law enforcement, and we were able to confirm that they did indeed show a pattern of coordination. We were then able to use these accounts to identify additional suspect accounts that were part of the campaign on Reddit. This group provides us with important attribution for the recent posting of the leaked UK documents, as well as insights into how adversaries are adapting their tactics.\n\nIn late October, an account [u/gregoratior](https://www.reddit.com/user/gregoratior/) posted the leaked documents and later reposted by an additional account [u/ostermaxnn](https://www.reddit.com/user/ostermaxnn?). Additionally, we were able to find a pocket of accounts participating in [vote manipulation](https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/what-constitutes-vote-cheating-or) on the original post. All of these accounts have the same shared pattern as the original Secondary Infektion group detected, causing us to believe that this was indeed tied to the original group.\n\nOutside of the post by u/gregoratior, none of these accounts or posts received much attention on the platform, and many of the posts were removed either by moderators or as part of normal content manipulation operations. The accounts posted in different regional subreddits, and in several different languages.\n\nKarma distribution:\n\n* 0 or less: 42\n* 1 - 9: 13\n* 10 or greater: 6\n* Max Karma: 48\n\nAs a result of this investigation, we are banning [1 subreddit](https://www.reddit.com/r/ukwhistleblower/) and 61 accounts under our policies against vote manipulation and misuse of the platform. As we have done with previous influence operations, we will also preserve these accounts for a time, so that researchers and the public can scrutinize them to see for themselves how these accounts operated.\n\nEDIT: I'm signing off for the evening. Thanks for the comments and questions. \n\n&amp;#x200B;\n\n|[gregoratior](https://www.reddit.com/user/gregoratior)|[LuzRun](https://www.reddit.com/user/LuzRun)|[McDownes](https://www.reddit.com/user/McDownes)|[davidjglover](https://www.reddit.com/user/davidjglover)|[HarrisonBriggs](https://www.reddit.com/user/HarrisonBriggs)|\n|:-|:-|:-|:-|:-|\n|[BillieFolmar](https://www.reddit.com/user/BillieFolmar)|[jaimeibanez](https://www.reddit.com/user/jaimeibanez)|[robeharty](https://www.reddit.com/user/robeharty)|[feliciahogg](https://www.reddit.com/user/feliciahogg)|[KlausSteiner](https://www.reddit.com/user/KlausSteiner)|\n|[alabelm](https://www.reddit.com/user/alabelm)|[bernturmann](https://www.reddit.com/user/bernturmann)|[AntonioDiazz](https://www.reddit.com/user/AntonioDiazz)|[ciawahhed](https://www.reddit.com/user/ciawahhed)|[krakodoc](https://www.reddit.com/user/krakodoc)|\n|[PeterMurtaugh](https://www.reddit.com/user/PeterMurtaugh)|[blancoaless](https://www.reddit.com/user/blancoaless)|[zurabagriashvili](https://www.reddit.com/user/zurabagriashvili)|[saliahwhite](https://www.reddit.com/user/saliahwhite)|[fullekyl](https://www.reddit.com/user/fullekyl)|\n|[Rinzoog](https://www.reddit.com/user/Rinzoog)|[almanzamary](https://www.reddit.com/user/almanzamary)|[Defiant\\_Emu](https://www.reddit.com/user/Defiant_Emu)|[Ostermaxnn](https://www.reddit.com/user/Ostermaxnn)|[LauraKnecht](https://www.reddit.com/user/LauraKnecht)|\n|[MikeHanon](https://www.reddit.com/user/MikeHanon)|[estellatorres](https://www.reddit.com/user/estellatorres)|[PastJournalist](https://www.reddit.com/user/PastJournalist)|[KattyTorr](https://www.reddit.com/user/KattyTorr)|[TomSallee](https://www.reddit.com/user/TomSallee)|\n|[uzunadnan](https://www.reddit.com/user/uzunadnan)|[EllisonRedfall](https://www.reddit.com/user/EllisonRedfall)|[vasiliskus](https://www.reddit.com/user/vasiliskus)|[KimJjj](https://www.reddit.com/user/KimJjj)|[NicSchum](https://www.reddit.com/user/NicSchum)|\n|[lauraferrojo](https://www.reddit.com/user/lauraferrojo)|[chavezserg](https://www.reddit.com/user/chavezserg)|[MaryCWolf](https://www.reddit.com/user/MaryCWolf)|[CharlesRichardson](https://www.reddit.com/user/CharlesRichardson)|[brigittemaur](https://www.reddit.com/user/brigittemaur)|\n|[MilitaryObserver](https://www.reddit.com/user/MilitaryObserver)|[bellagara](https://www.reddit.com/user/bellagara)|[StevtBell](https://www.reddit.com/user/StevtBell)|[SherryNuno](https://www.reddit.com/user/SherryNuno)|[delmaryang](https://www.reddit.com/user/delmaryang)|\n|[RuffMoulton](https://www.reddit.com/user/RuffMoulton)|[francovaz](https://www.reddit.com/user/francovaz)|[victoriasanches](https://www.reddit.com/user/victoriasanches)|[PushyFrank](https://www.reddit.com/user/PushyFrank)||\n|[kempnaomi](https://www.reddit.com/user/kempnaomi)|[claudialopezz](https://www.reddit.com/user/claudialopezz)|[FeistyWedding](https://www.reddit.com/user/FeistyWedding)|[demomanz](https://www.reddit.com/user/demomanz)||\n|[MaxKasyan](https://www.reddit.com/user/MaxKasyan)|[garrypugh](https://www.reddit.com/user/garrypugh)|[Party\\_Actuary](https://www.reddit.com/user/Party_Actuary)|[rabbier](https://www.reddit.com/user/rabbier)||\n|[davecooperr](https://www.reddit.com/user/davecooperr)|[gilbmedina84](https://www.reddit.com/user/gilbmedina84)|[ZayasLiTel](https://www.reddit.com/user/ZayasLiTel)|[Ritterc](https://www.reddit.com/user/Ritterc)||\n\nedit:added subreddit link", "author_fullname": "t2_qromv", "saved": false, "mod_reason_title": null, "gilded": 9, "clicked": false, "title": "Suspected Campaign from Russia on Reddit", "link_flair_richtext": [], "subreddit_name_prefixed": "r/RedditSafety", "hidden": false, "pwls": null, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_e74nml", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.86, "author_flair_background_color": null, "subreddit_type": "restricted", "ups": 54254, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 54254, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "self", "edited": 1575680428.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": **********.0, "link_flair_type": "text", "wls": null, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.RedditSafety", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;We were recently made aware of a &lt;a href=\"https://www.reddit.com/r/worldpolitics/comments/dkzlfc/officialsensitive_great_britain_is_practically/\"&gt;post on Reddit&lt;/a&gt; that included leaked documents from the UK. We investigated this account and the accounts connected to it, and today we believe this was part of a campaign that has been reported as originating from Russia.&lt;/p&gt;\n\n&lt;p&gt;Earlier this year Facebook discovered a &lt;a href=\"https://about.fb.com/news/2019/05/more-cib-from-russia/\"&gt;Russian campaign&lt;/a&gt; on its platform, which was further analyzed by the Atlantic Council and dubbed “&lt;a href=\"https://medium.com/dfrlab/top-takes-suspected-russian-intelligence-operation-39212367d2f0\"&gt;Secondary Infektion&lt;/a&gt;.” Suspect accounts on Reddit were recently reported to us, along with indicators from law enforcement, and we were able to confirm that they did indeed show a pattern of coordination. We were then able to use these accounts to identify additional suspect accounts that were part of the campaign on Reddit. This group provides us with important attribution for the recent posting of the leaked UK documents, as well as insights into how adversaries are adapting their tactics.&lt;/p&gt;\n\n&lt;p&gt;In late October, an account &lt;a href=\"https://www.reddit.com/user/gregoratior/\"&gt;u/gregoratior&lt;/a&gt; posted the leaked documents and later reposted by an additional account &lt;a href=\"https://www.reddit.com/user/ostermaxnn?\"&gt;u/ostermaxnn&lt;/a&gt;. Additionally, we were able to find a pocket of accounts participating in &lt;a href=\"https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/what-constitutes-vote-cheating-or\"&gt;vote manipulation&lt;/a&gt; on the original post. All of these accounts have the same shared pattern as the original Secondary Infektion group detected, causing us to believe that this was indeed tied to the original group.&lt;/p&gt;\n\n&lt;p&gt;Outside of the post by &lt;a href=\"/u/gregoratior\"&gt;u/gregoratior&lt;/a&gt;, none of these accounts or posts received much attention on the platform, and many of the posts were removed either by moderators or as part of normal content manipulation operations. The accounts posted in different regional subreddits, and in several different languages.&lt;/p&gt;\n\n&lt;p&gt;Karma distribution:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;0 or less: 42&lt;/li&gt;\n&lt;li&gt;1 - 9: 13&lt;/li&gt;\n&lt;li&gt;10 or greater: 6&lt;/li&gt;\n&lt;li&gt;Max Karma: 48&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;As a result of this investigation, we are banning &lt;a href=\"https://www.reddit.com/r/ukwhistleblower/\"&gt;1 subreddit&lt;/a&gt; and 61 accounts under our policies against vote manipulation and misuse of the platform. As we have done with previous influence operations, we will also preserve these accounts for a time, so that researchers and the public can scrutinize them to see for themselves how these accounts operated.&lt;/p&gt;\n\n&lt;p&gt;EDIT: I&amp;#39;m signing off for the evening. Thanks for the comments and questions. &lt;/p&gt;\n\n&lt;p&gt;&amp;#x200B;&lt;/p&gt;\n\n&lt;table&gt;&lt;thead&gt;\n&lt;tr&gt;\n&lt;th align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/gregoratior\"&gt;gregoratior&lt;/a&gt;&lt;/th&gt;\n&lt;th align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/LuzRun\"&gt;LuzRun&lt;/a&gt;&lt;/th&gt;\n&lt;th align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/McDownes\"&gt;McDownes&lt;/a&gt;&lt;/th&gt;\n&lt;th align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/davidjglover\"&gt;davidjglover&lt;/a&gt;&lt;/th&gt;\n&lt;th align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/HarrisonBriggs\"&gt;HarrisonBriggs&lt;/a&gt;&lt;/th&gt;\n&lt;/tr&gt;\n&lt;/thead&gt;&lt;tbody&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/BillieFolmar\"&gt;BillieFolmar&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/jaimeibanez\"&gt;jaimeibanez&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/robeharty\"&gt;robeharty&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/feliciahogg\"&gt;feliciahogg&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/KlausSteiner\"&gt;KlausSteiner&lt;/a&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/alabelm\"&gt;alabelm&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/bernturmann\"&gt;bernturmann&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/AntonioDiazz\"&gt;AntonioDiazz&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/ciawahhed\"&gt;ciawahhed&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/krakodoc\"&gt;krakodoc&lt;/a&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/PeterMurtaugh\"&gt;PeterMurtaugh&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/blancoaless\"&gt;blancoaless&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/zurabagriashvili\"&gt;zurabagriashvili&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/saliahwhite\"&gt;saliahwhite&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/fullekyl\"&gt;fullekyl&lt;/a&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/Rinzoog\"&gt;Rinzoog&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/almanzamary\"&gt;almanzamary&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/Defiant_Emu\"&gt;Defiant_Emu&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/Ostermaxnn\"&gt;Ostermaxnn&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/LauraKnecht\"&gt;LauraKnecht&lt;/a&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/MikeHanon\"&gt;MikeHanon&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/estellatorres\"&gt;estellatorres&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/PastJournalist\"&gt;PastJournalist&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/KattyTorr\"&gt;KattyTorr&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/TomSallee\"&gt;TomSallee&lt;/a&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/uzunadnan\"&gt;uzunadnan&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/EllisonRedfall\"&gt;EllisonRedfall&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/vasiliskus\"&gt;vasiliskus&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/KimJjj\"&gt;KimJjj&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/NicSchum\"&gt;NicSchum&lt;/a&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/lauraferrojo\"&gt;lauraferrojo&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/chavezserg\"&gt;chavezserg&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/MaryCWolf\"&gt;MaryCWolf&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/CharlesRichardson\"&gt;CharlesRichardson&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/brigittemaur\"&gt;brigittemaur&lt;/a&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/MilitaryObserver\"&gt;MilitaryObserver&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/bellagara\"&gt;bellagara&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/StevtBell\"&gt;StevtBell&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/SherryNuno\"&gt;SherryNuno&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/delmaryang\"&gt;delmaryang&lt;/a&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/RuffMoulton\"&gt;RuffMoulton&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/francovaz\"&gt;francovaz&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/victoriasanches\"&gt;victoriasanches&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/PushyFrank\"&gt;PushyFrank&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/kempnaomi\"&gt;kempnaomi&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/claudialopezz\"&gt;claudialopezz&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/FeistyWedding\"&gt;FeistyWedding&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/demomanz\"&gt;demomanz&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/MaxKasyan\"&gt;MaxKasyan&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/garrypugh\"&gt;garrypugh&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/Party_Actuary\"&gt;Party_Actuary&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/rabbier\"&gt;rabbier&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/davecooperr\"&gt;davecooperr&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/gilbmedina84\"&gt;gilbmedina84&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/ZayasLiTel\"&gt;ZayasLiTel&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;a href=\"https://www.reddit.com/user/Ritterc\"&gt;Ritterc&lt;/a&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;/tbody&gt;&lt;/table&gt;\n\n&lt;p&gt;edit:added subreddit link&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?auto=webp&amp;s=cbfcffe54f3de8aba305626b2d9c7057fe1a292a", "width": 1778, "height": 1000}, "resolutions": [{"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=7e60639d4339894ef781516959cb24757132c11c", "width": 108, "height": 60}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=783e0a56b5d22b084e35a8e19f27faefa17a854d", "width": 216, "height": 121}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=2d391ab3687bdd2dbc8f63290ae2e1984644cf29", "width": 320, "height": 179}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=e46fd35eb34e0b623283ef1f5281a49aec86a742", "width": 640, "height": 359}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=3c1e823f4eee34c8f405fe40ddba1c39c37de3e8", "width": 960, "height": 539}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=d5920fcc727df5dcfb329d5994ed21e3fc4df990", "width": 1080, "height": 607}], "variants": {}, "id": "vC5jUSWF02Uwo4N52VsG4foGmVn-q9qmXXPBKQM4yvs"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_vm1db", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "e74nml", "is_robot_indexable": true, "report_reasons": null, "author": "worstnerd", "discussion_type": null, "num_comments": 2816, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/RedditSafety/comments/e74nml/suspected_campaign_from_russia_on_reddit/", "stickied": false, "url": "https://www.reddit.com/r/RedditSafety/comments/e74nml/suspected_campaign_from_russia_on_reddit/", "subreddit_subscribers": 40199, "created_utc": **********.0, "num_crossposts": 166, "media": null, "is_video": false}], "created": 1575670275.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.redditsecurity", "allow_live_comments": true, "selftext_html": null, "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "url_overridden_by_dest": "https://www.reddit.com/r/redditsecurity/comments/e74nml/suspected_campaign_from_russia_on_reddit/", "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?auto=webp&amp;s=cbfcffe54f3de8aba305626b2d9c7057fe1a292a", "width": 1778, "height": 1000}, "resolutions": [{"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=7e60639d4339894ef781516959cb24757132c11c", "width": 108, "height": 60}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=783e0a56b5d22b084e35a8e19f27faefa17a854d", "width": 216, "height": 121}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=2d391ab3687bdd2dbc8f63290ae2e1984644cf29", "width": 320, "height": 179}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=e46fd35eb34e0b623283ef1f5281a49aec86a742", "width": 640, "height": 359}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=3c1e823f4eee34c8f405fe40ddba1c39c37de3e8", "width": 960, "height": 539}, {"url": "https://external-preview.redd.it/FOkt6VENjblUWcjpG-QOLuNqb49iyKKsoeKNM4-Kb4k.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=d5920fcc727df5dcfb329d5994ed21e3fc4df990", "width": 1080, "height": 607}], "variants": {}, "id": "vC5jUSWF02Uwo4N52VsG4foGmVn-q9qmXXPBKQM4yvs"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "e75f07", "is_robot_indexable": true, "report_reasons": null, "author": "worstnerd", "discussion_type": null, "num_comments": 3, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "crosspost_parent": "t3_e74nml", "author_flair_text_color": null, "permalink": "/r/announcements/comments/e75f07/suspected_campaign_from_russia_on_reddit/", "stickied": false, "url": "https://www.reddit.com/r/redditsecurity/comments/e74nml/suspected_campaign_from_russia_on_reddit/", "subreddit_subscribers": *********, "created_utc": 1575670275.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "**TL;DR** Today we launched [an Extra Life Award](https://i.redd.it/4xw2jtnwlqu31.png) to help raise money and awareness for [Extra Life](https://www.extra-life.org/reddit), a 24-hour gaming marathon charity benefiting Children's Miracle Network Hospitals! This new award is available alongside Silver, Gold, and Platinum from now through Nov. 2, and Reddit will match the first $15,000 of ALL Coins purchased during this time.\n\n[**Purchase Coins today and help support children's hospitals!**](https://www.reddit.com/coins/)\n\nHere are a few details about the limited Extra Life Award:\n\n* The award costs 500 [Coins](https://www.reddit.com/coins/)—the same cost as the Gold award\n* The recipient receives a week of [Premium](https://www.reddit.com/premium) and 100 coins—the same benefits as Gold!\n* Anyone who gives this award, I'm told, has a heart of gold! (And also a shiny, new trophy at a later date!)\n* Reddit will match the first $15,000 of ALL Coin purchases from now through Nov. 2.\n\nSee the award here in all its snazziness:\n\n&amp;#x200B;\n\nhttps://preview.redd.it/rinm7004pwv31.png?width=570&amp;format=png&amp;auto=webp&amp;s=b1bac7303a32d91eeac4b56bf7e6c3052e5bd462\n\n**But why?**\n\nLast week [we announced](https://www.reddit.com/r/blog/comments/dmj0sb/join_us_in_supporting_extra_life_a_24hour_gaming/) our 8th year partnering with Extra Life for our favorite annual tradition: playing ~~24~~ 25 hours of video games to help raise money for sick kids. We're not doing this alone! Thanks to some truly heroic redditors, we have already raised over $40,000 of our $150,000 goal!\n\nHowever, we recognize not everyone can relinquish the majority of their weekend to play video games (we totally had other plans, we swear). We made this award to make it easier for even more people to get involved and help support one of our favorite charity events.\n\nHave the opposite problem? If your wallet is feeling thin, you can also help by signing up to fundraise! Check out [our recent post](https://www.reddit.com/r/blog/comments/dmj0sb/join_us_in_supporting_extra_life_a_24hour_gaming/) for more details about joining Team Reddit.\n\n**Reminder: Extra Life Game Day is November 2nd!**\n\nOn this coming Saturday a raiding party of staffers here at Reddit HQ will be streaming our fundraising efforts live on our [Twitch stream](https://www.twitch.tv/reddit). Tune in and join us for 25 hours of mind-melting gaming and delirious, sleep-deprived antics. From Fortnite to Untitled Goose Game, we'll be playing a variety of games, so join us and you may even get to play head-to-head against an admin in your favorite game!", "author_fullname": "t2_39hzo", "saved": false, "mod_reason_title": null, "gilded": 9, "clicked": false, "title": "The Extra Life Charity Award — Raise awareness for children's hospitals through gilding!", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": 133, "top_awarded_type": null, "hide_score": false, "media_metadata": {"rinm7004pwv31": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 102, "x": 108, "u": "https://preview.redd.it/rinm7004pwv31.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=427fcb3285ae163e5f5affe03d20ff9be3a4d9a6"}, {"y": 205, "x": 216, "u": "https://preview.redd.it/rinm7004pwv31.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=244f9098ad7aa4880c5b423671ed7d3a0123d32e"}, {"y": 304, "x": 320, "u": "https://preview.redd.it/rinm7004pwv31.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=922cb91e2b79234e87750a3b74d9d74916f973bf"}], "s": {"y": 543, "x": 570, "u": "https://preview.redd.it/rinm7004pwv31.png?width=570&amp;format=png&amp;auto=webp&amp;s=b1bac7303a32d91eeac4b56bf7e6c3052e5bd462"}, "id": "rinm7004pwv31"}}, "name": "t3_dpqd0z", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.8, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 23325, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": 140, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 23325, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "https://b.thumbs.redditmedia.com/MGaOJeZl7UsmdtroJopYU_36HUNvQ9LUV5WFH_FJ2vI.jpg", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1572540889.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;&lt;strong&gt;TL;DR&lt;/strong&gt; Today we launched &lt;a href=\"https://i.redd.it/4xw2jtnwlqu31.png\"&gt;an Extra Life Award&lt;/a&gt; to help raise money and awareness for &lt;a href=\"https://www.extra-life.org/reddit\"&gt;Extra Life&lt;/a&gt;, a 24-hour gaming marathon charity benefiting Children&amp;#39;s Miracle Network Hospitals! This new award is available alongside Silver, Gold, and Platinum from now through Nov. 2, and Reddit will match the first $15,000 of ALL Coins purchased during this time.&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://www.reddit.com/coins/\"&gt;&lt;strong&gt;Purchase Coins today and help support children&amp;#39;s hospitals!&lt;/strong&gt;&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;Here are a few details about the limited Extra Life Award:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;The award costs 500 &lt;a href=\"https://www.reddit.com/coins/\"&gt;Coins&lt;/a&gt;—the same cost as the Gold award&lt;/li&gt;\n&lt;li&gt;The recipient receives a week of &lt;a href=\"https://www.reddit.com/premium\"&gt;Premium&lt;/a&gt; and 100 coins—the same benefits as Gold!&lt;/li&gt;\n&lt;li&gt;Anyone who gives this award, I&amp;#39;m told, has a heart of gold! (And also a shiny, new trophy at a later date!)&lt;/li&gt;\n&lt;li&gt;Reddit will match the first $15,000 of ALL Coin purchases from now through Nov. 2.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;See the award here in all its snazziness:&lt;/p&gt;\n\n&lt;p&gt;&amp;#x200B;&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/rinm7004pwv31.png?width=570&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=b1bac7303a32d91eeac4b56bf7e6c3052e5bd462\"&gt;https://preview.redd.it/rinm7004pwv31.png?width=570&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=b1bac7303a32d91eeac4b56bf7e6c3052e5bd462&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;But why?&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;Last week &lt;a href=\"https://www.reddit.com/r/blog/comments/dmj0sb/join_us_in_supporting_extra_life_a_24hour_gaming/\"&gt;we announced&lt;/a&gt; our 8th year partnering with Extra Life for our favorite annual tradition: playing &lt;del&gt;24&lt;/del&gt; 25 hours of video games to help raise money for sick kids. We&amp;#39;re not doing this alone! Thanks to some truly heroic redditors, we have already raised over $40,000 of our $150,000 goal!&lt;/p&gt;\n\n&lt;p&gt;However, we recognize not everyone can relinquish the majority of their weekend to play video games (we totally had other plans, we swear). We made this award to make it easier for even more people to get involved and help support one of our favorite charity events.&lt;/p&gt;\n\n&lt;p&gt;Have the opposite problem? If your wallet is feeling thin, you can also help by signing up to fundraise! Check out &lt;a href=\"https://www.reddit.com/r/blog/comments/dmj0sb/join_us_in_supporting_extra_life_a_24hour_gaming/\"&gt;our recent post&lt;/a&gt; for more details about joining Team Reddit.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Reminder: Extra Life Game Day is November 2nd!&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;On this coming Saturday a raiding party of staffers here at Reddit HQ will be streaming our fundraising efforts live on our &lt;a href=\"https://www.twitch.tv/reddit\"&gt;Twitch stream&lt;/a&gt;. Tune in and join us for 25 hours of mind-melting gaming and delirious, sleep-deprived antics. From Fortnite to Untitled Goose Game, we&amp;#39;ll be playing a variety of games, so join us and you may even get to play head-to-head against an admin in your favorite game!&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "dpqd0z", "is_robot_indexable": true, "report_reasons": null, "author": "sodypop", "discussion_type": null, "num_comments": 858, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/dpqd0z/the_extra_life_charity_award_raise_awareness_for/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/dpqd0z/the_extra_life_charity_award_raise_awareness_for/", "subreddit_subscribers": *********, "created_utc": 1572540889.0, "num_crossposts": 66, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "", "user_reports": [], "saved": false, "mod_reason_title": null, "gilded": 1, "clicked": false, "title": "Reddit Security Report -- October 30, 2019", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_dpa8rn", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.8, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 10871, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "author_fullname": "t2_1wjm", "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 10871, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "default", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "crosspost_parent_list": [{"approved_at_utc": null, "subreddit": "RedditSafety", "selftext": "Through the year, we've shared updates on[ detecting](https://www.reddit.com/r/redditsecurity/comments/b0a8he/detecting_and_mitigating_content_manipulation_on/) and[ mitigating content manipulation](https://www.reddit.com/r/redditsecurity/comments/b0a8he/detecting_and_mitigating_content_manipulation_on/) and [keeping your accounts safe](https://www.reddit.com/r/redditsecurity/comments/bletrr/how_to_keep_your_reddit_account_safe/). Today we are sharing our first Reddit Security Report, which we'll be continuing on a quarterly basis. We are committed to continuously evolving how we tackle these problems. The purpose of these reports is to keep you informed about relevant events and actions.\n\n### **By The Numbers**\n\n|**Category**|**Volume (July - Sept)**|**Volume (April - June)**| \n|---|---|---|\n|Content manipulation reports|5,461,005|5,222,058|\n|Admin content manipulation removals|19,149,133|14,375,903|\n|Admin content manipulation account sanctions|1,406,440|2,520,474|\n|3rd party breach accounts processed|4,681,297,045|1,355,654,815|\n|Protective account security actions|7,190,318|1,845,605|\n\n\nThese are the primary metrics we track internally, and we thought you’d want to see them too. If there are alternative metrics that seem worth looking at as part of this report, we’re all ears.\n\n#### **Content Manipulation**\n\nContent manipulation is a term we use to combine things like spam, community interference, vote manipulation, etc. This year we have overhauled how we handle these issues, and this quarter was no different. We focused these efforts on:\n\n1. Improving our detection models for accounts performing these actions \n2. Making it harder for them to spin up new accounts \n\nRecently, we also improved our enforcement measures against accounts taking part in [vote manipulation](https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/what-constitutes-vote-cheating-or) (i.e. when people coordinate or otherwise cheat to increase or decrease the vote scores on Reddit). Over the last 6 months (and mostly during the last couple of months), we increased our actions against accounts participating in vote manipulation by about 30x. We sanctioned or warned around 22k accounts for this in the last 3 weeks of September alone.\n\n#### **Account Security**\n\nThis quarter, we finished up a major effort to detect all accounts that had credentials matching historical 3rd party breaches. It's important to track breaches that happen on other sites or services because bad actors will use those same username/password combinations to break into your other accounts (on the basis that a percentage of people reuse passwords). You might have experienced some of our efforts if we forced you to reset your password as a precaution. We expect the number of protective account security actions to drop drastically going forward as we no longer have a large backlog of breach datasets to process. Hopefully we have reached a steady state, which should reduce some of the pain for users. We will continue to deal with new breach sets that come in, as well as accounts that are hit by bots attempting to gain access (please take a look at [this post](https://www.reddit.com/r/redditsecurity/comments/bletrr/how_to_keep_your_reddit_account_safe/) on how you can improve your account security). \n\n#### **Our Recent Investigations**  \n\nWe have a lot of investigations active at any given time (courtesy of your neighborhood t-shirt spammers and VPN peddlers), and while we can’t cover them all, we want to use this report to share the results of just some of that work.  \n\n**Ban Evasion**\n\nThis quarter, we dealt with a highly coordinated ban evasion ring from users of r/opieandanthony. This began after we banned the subreddit for targeted harassment of users, as well as repeated copyright infringement. The group would quickly pop up on both new and abandoned subreddits to continue the abuse. We also learned that they were coordinating on another platform and through dedicated websites to redirect users to the latest target of their harassment. \n\nThis situation was different from your run-of-the-mill ~~shitheadery~~ ban evasion because the group was both creating new subreddits *and* resurrecting inactive or unmoderated subreddits. We quickly adjusted our efforts to this behavior. We also reported their offending account to the other platform and they were quick to ban the account. We then contacted the hosts of the independent websites to report the abuse. This helped ensure that the sites are no longer able to redirect automatically to Reddit for abuse purposes. Ultimately, we banned 78 subreddits (5 of which existed prior to the attack), and suspended 2,382 accounts. The ban evading activity has largely ceased (you know...until they read this).\n\nThere are a few takeaways from this investigation worth pulling out: \n\n1. Ban evaders (and others up to no good) often work across platforms, and so it’s important for those of us in the industry to also share information when we spot these types of coordinated campaigns.\n2. The layered moderation on Reddit works: Moderators brought this to our attention and did some awesome initial investigating; our Community team was then able to communicate with mods and users to help surface suspicious behavior; our detection teams were able to quickly detect and stop the efforts of the ban evaders. \n3. We have also been developing and testing new tools to address ban evasion recently. This was a good opportunity to test them in the wild, and they were incredibly effective at detecting and quickly actioning many of the accounts that were responsible for the ban evasion actions. We want to roll these tools out more broadly (expect a future post around this). \n\n**Reports of Suspected Manipulation**\n\nThe protests in Hong Kong have been a growing concern worldwide, and as always, conversation on Reddit reflects this. It’s no surprise that we’ve seen Hong Kong-related communities grow immensely in recent months as a result. With this growth, we have received a number of user reports and comments asking if there is manipulation in these communities. We take the authenticity of conversation on Reddit incredibly seriously, and we want to address your concerns here. \n\nFirst, we have not detected widespread manipulation in Hong Kong related subreddits nor seen any manipulation that affected those communities or their conversations in a meaningful way.\n\nIt's worth taking a step back to talk about what we look for in these situations. While we obviously can’t share all of our tactics for investigating these threats, there are some signals that users will be familiar with. When trying to understand if a community is facing widespread manipulation, we will look at foundational signals such as the presence of vote manipulation, mod ban rates (because mods know their community better than we do), spam content removals, and other signals that allow us to detect coordinated and scaled activities (*pause for dramatic effect*). If this doesn’t sound like the stuff of spy novels, it’s because it’s not. We continually talk about foundational safety metrics like vote manipulation, and spam removals because these are the same tools that advanced adversaries use (For more thoughts on this look [here](https://www.reddit.com/r/redditsecurity/comments/d6l41l/an_update_on_content_manipulation_and_an_upcoming/)). \n\nSecond, let’s look at what other major platforms have reported on coordinated behavior targeting Hong Kong. Their investigations revealed attempts consisting primarily of very low quality propaganda. This is important when looking for similar efforts on Reddit. In healthier communities like r/hongkong, we simply don’t see a proliferation of this low-quality content (from users or adversaries). The story does change when looking at r/sino or r/Hong_Kong (note the mod overlap). In these subreddits, we see far more low quality and one-sided content. However, this is not against our rules, and indeed it is not even particularly unusual to see one-sided viewpoints in some geographically specific subreddits...What IS against the rules is coordinated action (state sponsored or otherwise). We have looked closely at these subreddits and we have found no indicators of widespread coordination. In other words, we do see this low quality content in these subreddits, but it seems to be happening in a genuine way. \n\nIf you see anything suspicious, please report it to us [here](https://www.reddit.com/report). If it’s regarding potential coordinated efforts that aren't as well-suited to our regular report system, you can also use our separate investigations report flow by [emailing us](mailto:<EMAIL>). \n\n#### **Final Thoughts**\n\nFinally, I would like to acknowledge the reports our peers have published during the past couple of months (or even today). Whenever these reports come out, we always do our own investigation. We have not found any similar attempts on our own platform this quarter. Part of this is a recognition that Reddit today is less international than these other platforms, with the majority of users being in the US, and other English speaking countries. Additionally, our layered moderation structure (user up/down-votes, community moderation, admin policy enforcement) makes Reddit a more challenging platform to manipulate in a scaled way (i.e. Reddit is hard). Finally, Reddit is simply not well suited to being an amplification platform, nor do we aim to be. This reach is ultimately what an adversary is looking for. We continue to monitor these efforts, and are committed to being transparent about anything that we do detect. \n\nAs I mentioned above, this is the first version of these reports. We would love to hear your thoughts on it, as well as any input on what type of information you would like to see in future reports. \n\nI’ll stick around, along with u/worstnerd, to answer any questions that we can.", "author_fullname": "t2_1wjm", "saved": false, "mod_reason_title": null, "gilded": 3, "clicked": false, "title": "Reddit Security Report -- October 30, 2019", "link_flair_richtext": [], "subreddit_name_prefixed": "r/RedditSafety", "hidden": false, "pwls": null, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_dp9nbg", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.86, "author_flair_background_color": null, "subreddit_type": "restricted", "ups": 3588, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 3588, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1572455819.0, "link_flair_type": "text", "wls": null, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.RedditSafety", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Through the year, we&amp;#39;ve shared updates on&lt;a href=\"https://www.reddit.com/r/redditsecurity/comments/b0a8he/detecting_and_mitigating_content_manipulation_on/\"&gt; detecting&lt;/a&gt; and&lt;a href=\"https://www.reddit.com/r/redditsecurity/comments/b0a8he/detecting_and_mitigating_content_manipulation_on/\"&gt; mitigating content manipulation&lt;/a&gt; and &lt;a href=\"https://www.reddit.com/r/redditsecurity/comments/bletrr/how_to_keep_your_reddit_account_safe/\"&gt;keeping your accounts safe&lt;/a&gt;. Today we are sharing our first Reddit Security Report, which we&amp;#39;ll be continuing on a quarterly basis. We are committed to continuously evolving how we tackle these problems. The purpose of these reports is to keep you informed about relevant events and actions.&lt;/p&gt;\n\n&lt;h3&gt;&lt;strong&gt;By The Numbers&lt;/strong&gt;&lt;/h3&gt;\n\n&lt;table&gt;&lt;thead&gt;\n&lt;tr&gt;\n&lt;th&gt;&lt;strong&gt;Category&lt;/strong&gt;&lt;/th&gt;\n&lt;th&gt;&lt;strong&gt;Volume (July - Sept)&lt;/strong&gt;&lt;/th&gt;\n&lt;th&gt;&lt;strong&gt;Volume (April - June)&lt;/strong&gt;&lt;/th&gt;\n&lt;/tr&gt;\n&lt;/thead&gt;&lt;tbody&gt;\n&lt;tr&gt;\n&lt;td&gt;Content manipulation reports&lt;/td&gt;\n&lt;td&gt;5,461,005&lt;/td&gt;\n&lt;td&gt;5,222,058&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td&gt;Admin content manipulation removals&lt;/td&gt;\n&lt;td&gt;19,149,133&lt;/td&gt;\n&lt;td&gt;14,375,903&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td&gt;Admin content manipulation account sanctions&lt;/td&gt;\n&lt;td&gt;1,406,440&lt;/td&gt;\n&lt;td&gt;2,520,474&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td&gt;3rd party breach accounts processed&lt;/td&gt;\n&lt;td&gt;4,681,297,045&lt;/td&gt;\n&lt;td&gt;1,355,654,815&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td&gt;Protective account security actions&lt;/td&gt;\n&lt;td&gt;7,190,318&lt;/td&gt;\n&lt;td&gt;1,845,605&lt;/td&gt;\n&lt;/tr&gt;\n&lt;/tbody&gt;&lt;/table&gt;\n\n&lt;p&gt;These are the primary metrics we track internally, and we thought you’d want to see them too. If there are alternative metrics that seem worth looking at as part of this report, we’re all ears.&lt;/p&gt;\n\n&lt;h4&gt;&lt;strong&gt;Content Manipulation&lt;/strong&gt;&lt;/h4&gt;\n\n&lt;p&gt;Content manipulation is a term we use to combine things like spam, community interference, vote manipulation, etc. This year we have overhauled how we handle these issues, and this quarter was no different. We focused these efforts on:&lt;/p&gt;\n\n&lt;ol&gt;\n&lt;li&gt;Improving our detection models for accounts performing these actions &lt;/li&gt;\n&lt;li&gt;Making it harder for them to spin up new accounts &lt;/li&gt;\n&lt;/ol&gt;\n\n&lt;p&gt;Recently, we also improved our enforcement measures against accounts taking part in &lt;a href=\"https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/what-constitutes-vote-cheating-or\"&gt;vote manipulation&lt;/a&gt; (i.e. when people coordinate or otherwise cheat to increase or decrease the vote scores on Reddit). Over the last 6 months (and mostly during the last couple of months), we increased our actions against accounts participating in vote manipulation by about 30x. We sanctioned or warned around 22k accounts for this in the last 3 weeks of September alone.&lt;/p&gt;\n\n&lt;h4&gt;&lt;strong&gt;Account Security&lt;/strong&gt;&lt;/h4&gt;\n\n&lt;p&gt;This quarter, we finished up a major effort to detect all accounts that had credentials matching historical 3rd party breaches. It&amp;#39;s important to track breaches that happen on other sites or services because bad actors will use those same username/password combinations to break into your other accounts (on the basis that a percentage of people reuse passwords). You might have experienced some of our efforts if we forced you to reset your password as a precaution. We expect the number of protective account security actions to drop drastically going forward as we no longer have a large backlog of breach datasets to process. Hopefully we have reached a steady state, which should reduce some of the pain for users. We will continue to deal with new breach sets that come in, as well as accounts that are hit by bots attempting to gain access (please take a look at &lt;a href=\"https://www.reddit.com/r/redditsecurity/comments/bletrr/how_to_keep_your_reddit_account_safe/\"&gt;this post&lt;/a&gt; on how you can improve your account security). &lt;/p&gt;\n\n&lt;h4&gt;&lt;strong&gt;Our Recent Investigations&lt;/strong&gt;&lt;/h4&gt;\n\n&lt;p&gt;We have a lot of investigations active at any given time (courtesy of your neighborhood t-shirt spammers and VPN peddlers), and while we can’t cover them all, we want to use this report to share the results of just some of that work.  &lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Ban Evasion&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;This quarter, we dealt with a highly coordinated ban evasion ring from users of &lt;a href=\"/r/opieandanthony\"&gt;r/opieandanthony&lt;/a&gt;. This began after we banned the subreddit for targeted harassment of users, as well as repeated copyright infringement. The group would quickly pop up on both new and abandoned subreddits to continue the abuse. We also learned that they were coordinating on another platform and through dedicated websites to redirect users to the latest target of their harassment. &lt;/p&gt;\n\n&lt;p&gt;This situation was different from your run-of-the-mill &lt;del&gt;shitheadery&lt;/del&gt; ban evasion because the group was both creating new subreddits &lt;em&gt;and&lt;/em&gt; resurrecting inactive or unmoderated subreddits. We quickly adjusted our efforts to this behavior. We also reported their offending account to the other platform and they were quick to ban the account. We then contacted the hosts of the independent websites to report the abuse. This helped ensure that the sites are no longer able to redirect automatically to Reddit for abuse purposes. Ultimately, we banned 78 subreddits (5 of which existed prior to the attack), and suspended 2,382 accounts. The ban evading activity has largely ceased (you know...until they read this).&lt;/p&gt;\n\n&lt;p&gt;There are a few takeaways from this investigation worth pulling out: &lt;/p&gt;\n\n&lt;ol&gt;\n&lt;li&gt;Ban evaders (and others up to no good) often work across platforms, and so it’s important for those of us in the industry to also share information when we spot these types of coordinated campaigns.&lt;/li&gt;\n&lt;li&gt;The layered moderation on Reddit works: Moderators brought this to our attention and did some awesome initial investigating; our Community team was then able to communicate with mods and users to help surface suspicious behavior; our detection teams were able to quickly detect and stop the efforts of the ban evaders. &lt;/li&gt;\n&lt;li&gt;We have also been developing and testing new tools to address ban evasion recently. This was a good opportunity to test them in the wild, and they were incredibly effective at detecting and quickly actioning many of the accounts that were responsible for the ban evasion actions. We want to roll these tools out more broadly (expect a future post around this). &lt;/li&gt;\n&lt;/ol&gt;\n\n&lt;p&gt;&lt;strong&gt;Reports of Suspected Manipulation&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;The protests in Hong Kong have been a growing concern worldwide, and as always, conversation on Reddit reflects this. It’s no surprise that we’ve seen Hong Kong-related communities grow immensely in recent months as a result. With this growth, we have received a number of user reports and comments asking if there is manipulation in these communities. We take the authenticity of conversation on Reddit incredibly seriously, and we want to address your concerns here. &lt;/p&gt;\n\n&lt;p&gt;First, we have not detected widespread manipulation in Hong Kong related subreddits nor seen any manipulation that affected those communities or their conversations in a meaningful way.&lt;/p&gt;\n\n&lt;p&gt;It&amp;#39;s worth taking a step back to talk about what we look for in these situations. While we obviously can’t share all of our tactics for investigating these threats, there are some signals that users will be familiar with. When trying to understand if a community is facing widespread manipulation, we will look at foundational signals such as the presence of vote manipulation, mod ban rates (because mods know their community better than we do), spam content removals, and other signals that allow us to detect coordinated and scaled activities (&lt;em&gt;pause for dramatic effect&lt;/em&gt;). If this doesn’t sound like the stuff of spy novels, it’s because it’s not. We continually talk about foundational safety metrics like vote manipulation, and spam removals because these are the same tools that advanced adversaries use (For more thoughts on this look &lt;a href=\"https://www.reddit.com/r/redditsecurity/comments/d6l41l/an_update_on_content_manipulation_and_an_upcoming/\"&gt;here&lt;/a&gt;). &lt;/p&gt;\n\n&lt;p&gt;Second, let’s look at what other major platforms have reported on coordinated behavior targeting Hong Kong. Their investigations revealed attempts consisting primarily of very low quality propaganda. This is important when looking for similar efforts on Reddit. In healthier communities like &lt;a href=\"/r/hongkong\"&gt;r/hongkong&lt;/a&gt;, we simply don’t see a proliferation of this low-quality content (from users or adversaries). The story does change when looking at &lt;a href=\"/r/sino\"&gt;r/sino&lt;/a&gt; or &lt;a href=\"/r/Hong_Kong\"&gt;r/Hong_Kong&lt;/a&gt; (note the mod overlap). In these subreddits, we see far more low quality and one-sided content. However, this is not against our rules, and indeed it is not even particularly unusual to see one-sided viewpoints in some geographically specific subreddits...What IS against the rules is coordinated action (state sponsored or otherwise). We have looked closely at these subreddits and we have found no indicators of widespread coordination. In other words, we do see this low quality content in these subreddits, but it seems to be happening in a genuine way. &lt;/p&gt;\n\n&lt;p&gt;If you see anything suspicious, please report it to us &lt;a href=\"https://www.reddit.com/report\"&gt;here&lt;/a&gt;. If it’s regarding potential coordinated efforts that aren&amp;#39;t as well-suited to our regular report system, you can also use our separate investigations report flow by [emailing us](mailto:&lt;a href=\"mailto:<EMAIL>\"&gt;<EMAIL>&lt;/a&gt;). &lt;/p&gt;\n\n&lt;h4&gt;&lt;strong&gt;Final Thoughts&lt;/strong&gt;&lt;/h4&gt;\n\n&lt;p&gt;Finally, I would like to acknowledge the reports our peers have published during the past couple of months (or even today). Whenever these reports come out, we always do our own investigation. We have not found any similar attempts on our own platform this quarter. Part of this is a recognition that Reddit today is less international than these other platforms, with the majority of users being in the US, and other English speaking countries. Additionally, our layered moderation structure (user up/down-votes, community moderation, admin policy enforcement) makes Reddit a more challenging platform to manipulate in a scaled way (i.e. Reddit is hard). Finally, Reddit is simply not well suited to being an amplification platform, nor do we aim to be. This reach is ultimately what an adversary is looking for. We continue to monitor these efforts, and are committed to being transparent about anything that we do detect. &lt;/p&gt;\n\n&lt;p&gt;As I mentioned above, this is the first version of these reports. We would love to hear your thoughts on it, as well as any input on what type of information you would like to see in future reports. &lt;/p&gt;\n\n&lt;p&gt;I’ll stick around, along with &lt;a href=\"/u/worstnerd\"&gt;u/worstnerd&lt;/a&gt;, to answer any questions that we can.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_vm1db", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "dp9nbg", "is_robot_indexable": true, "report_reasons": null, "author": "<PERSON><PERSON><PERSON><PERSON>", "discussion_type": null, "num_comments": 1272, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/RedditSafety/comments/dp9nbg/reddit_security_report_october_30_2019/", "stickied": false, "url": "https://www.reddit.com/r/RedditSafety/comments/dp9nbg/reddit_security_report_october_30_2019/", "subreddit_subscribers": 40199, "created_utc": 1572455819.0, "num_crossposts": 50, "media": null, "is_video": false}], "created": 1572458354.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.redditsecurity", "allow_live_comments": true, "selftext_html": null, "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "url_overridden_by_dest": "https://www.reddit.com/r/redditsecurity/comments/dp9nbg/reddit_security_report_october_30_2019/", "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "dpa8rn", "is_robot_indexable": true, "report_reasons": null, "author": "<PERSON><PERSON><PERSON><PERSON>", "discussion_type": null, "num_comments": 2, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "crosspost_parent": "t3_dp9nbg", "author_flair_text_color": null, "permalink": "/r/announcements/comments/dpa8rn/reddit_security_report_october_30_2019/", "stickied": false, "url": "https://www.reddit.com/r/redditsecurity/comments/dp9nbg/reddit_security_report_october_30_2019/", "subreddit_subscribers": *********, "created_utc": 1572458354.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "TL;DR is that we’re updating our harassment and bullying policy so we can be more responsive to your reports. \n\nHey everyone, \n\nWe wanted to let you know about some changes that we are making today to our [Content Policy](https://www.redditinc.com/policies/content-policy) regarding content that [threatens, harasses, or bullies](https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/do-not-threaten-harass-or-bully), which you can read in full [here](https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/do-not-threaten-harass-or-bully). \n\n**Why are we doing this?**\nThese changes, which were many months in the making, were primarily driven by feedback we received from you all, our users, indicating to us that there was a problem with the narrowness of our previous policy. Specifically, the old policy required a behavior to be “continued” and/or “systematic” for us to be able to take action against it as harassment. It also set a high bar of users fearing for their real-world safety to qualify, which we think is an incorrect calibration. Finally, it wasn’t clear that abuse toward both individuals and groups qualified under the rule. All these things meant that too often, instances of harassment and bullying, even egregious ones, were left unactioned. This was a bad user experience for you all, and frankly, it is something that made us feel not-great too. It was clearly a case of the letter of a rule not matching its spirit.\n\nThe changes we’re making today are trying to better address that, as well as to give some meta-context about the spirit of this rule: chiefly, Reddit is a place for conversation. Thus, behavior whose core effect is to shut people out of that conversation through intimidation or abuse has no place on our platform. \n\nWe also hope that this change will take some of the burden off moderators, as it will expand our ability to take action at scale against content that the vast majority of subreddits already have their own rules against-- rules that we support and encourage.\n\n**How will these changes work in practice?**\nWe all know that context is critically important here, and can be tricky, particularly when we’re talking about typed words on the internet. This is why we’re hoping today’s changes will help us better leverage human user reports. Where previously, we required the harassment victim to make the report to us directly, we’ll now be investigating reports from bystanders as well. We hope this will alleviate some of the burden on the harassee. \n\nYou should also know that we’ll also be harnessing some improved machine-learning tools to help us better sort and prioritize human user reports. But don’t worry, machines will only help us organize and prioritize user reports. They won’t be banning content or users on their own. A human user still has to report the content in order to surface it to us. Likewise, all actual decisions will still be made by a human admin. \n\nAs with any rule change, this will take some time to fully enforce. Our [response times have improved significantly since the start of the year](https://www.reddit.com/r/changelog/comments/becbnx/hey_rchangelog_were_rolling_out_some_new/), but we’re always striving to move faster. In the meantime, we encourage moderators to take this opportunity to examine their community rules and make sure that they are not creating an environment where bullying or harassment are tolerated or encouraged.\n\n**What should I do if I see content that I think breaks this rule?**\nAs always, if you see or experience behavior that you believe is in violation of this rule, please use the report button [“This is abusive or harassing &gt; “It’s targeted harassment”] to let us know. If you believe an entire user account or subreddit is dedicated to harassing or bullying behavior against an individual or group, we want to know that too; [report it to us here](https://www.reddit.com/report).\n\nThanks. As usual, we’ll hang around for a bit and answer questions.\n\nEdit: typo.\n*Edit 2: Thanks for your questions, we're signing off for now!*", "author_fullname": "t2_14bnhg", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Changes to Our Policy Against Bullying and Harassment", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_dbf9nj", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.65, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 17354, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 17354, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "self", "edited": 1569870392.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1569865578.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;TL;DR is that we’re updating our harassment and bullying policy so we can be more responsive to your reports. &lt;/p&gt;\n\n&lt;p&gt;Hey everyone, &lt;/p&gt;\n\n&lt;p&gt;We wanted to let you know about some changes that we are making today to our &lt;a href=\"https://www.redditinc.com/policies/content-policy\"&gt;Content Policy&lt;/a&gt; regarding content that &lt;a href=\"https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/do-not-threaten-harass-or-bully\"&gt;threatens, harasses, or bullies&lt;/a&gt;, which you can read in full &lt;a href=\"https://www.reddithelp.com/en/categories/rules-reporting/account-and-community-restrictions/do-not-threaten-harass-or-bully\"&gt;here&lt;/a&gt;. &lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Why are we doing this?&lt;/strong&gt;\nThese changes, which were many months in the making, were primarily driven by feedback we received from you all, our users, indicating to us that there was a problem with the narrowness of our previous policy. Specifically, the old policy required a behavior to be “continued” and/or “systematic” for us to be able to take action against it as harassment. It also set a high bar of users fearing for their real-world safety to qualify, which we think is an incorrect calibration. Finally, it wasn’t clear that abuse toward both individuals and groups qualified under the rule. All these things meant that too often, instances of harassment and bullying, even egregious ones, were left unactioned. This was a bad user experience for you all, and frankly, it is something that made us feel not-great too. It was clearly a case of the letter of a rule not matching its spirit.&lt;/p&gt;\n\n&lt;p&gt;The changes we’re making today are trying to better address that, as well as to give some meta-context about the spirit of this rule: chiefly, Reddit is a place for conversation. Thus, behavior whose core effect is to shut people out of that conversation through intimidation or abuse has no place on our platform. &lt;/p&gt;\n\n&lt;p&gt;We also hope that this change will take some of the burden off moderators, as it will expand our ability to take action at scale against content that the vast majority of subreddits already have their own rules against-- rules that we support and encourage.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;How will these changes work in practice?&lt;/strong&gt;\nWe all know that context is critically important here, and can be tricky, particularly when we’re talking about typed words on the internet. This is why we’re hoping today’s changes will help us better leverage human user reports. Where previously, we required the harassment victim to make the report to us directly, we’ll now be investigating reports from bystanders as well. We hope this will alleviate some of the burden on the harassee. &lt;/p&gt;\n\n&lt;p&gt;You should also know that we’ll also be harnessing some improved machine-learning tools to help us better sort and prioritize human user reports. But don’t worry, machines will only help us organize and prioritize user reports. They won’t be banning content or users on their own. A human user still has to report the content in order to surface it to us. Likewise, all actual decisions will still be made by a human admin. &lt;/p&gt;\n\n&lt;p&gt;As with any rule change, this will take some time to fully enforce. Our &lt;a href=\"https://www.reddit.com/r/changelog/comments/becbnx/hey_rchangelog_were_rolling_out_some_new/\"&gt;response times have improved significantly since the start of the year&lt;/a&gt;, but we’re always striving to move faster. In the meantime, we encourage moderators to take this opportunity to examine their community rules and make sure that they are not creating an environment where bullying or harassment are tolerated or encouraged.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;What should I do if I see content that I think breaks this rule?&lt;/strong&gt;\nAs always, if you see or experience behavior that you believe is in violation of this rule, please use the report button [“This is abusive or harassing &amp;gt; “It’s targeted harassment”] to let us know. If you believe an entire user account or subreddit is dedicated to harassing or bullying behavior against an individual or group, we want to know that too; &lt;a href=\"https://www.reddit.com/report\"&gt;report it to us here&lt;/a&gt;.&lt;/p&gt;\n\n&lt;p&gt;Thanks. As usual, we’ll hang around for a bit and answer questions.&lt;/p&gt;\n\n&lt;p&gt;Edit: typo.\n&lt;em&gt;Edit 2: Thanks for your questions, we&amp;#39;re signing off for now!&lt;/em&gt;&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?auto=webp&amp;s=f65608a0e50b80501b09dd4e6d4256a073bddaf7", "width": 1920, "height": 1080}, "resolutions": [{"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=f58daaa031b1949d7fba33ff04daa716f834c5fa", "width": 108, "height": 60}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=ef7c929673f1ddd3f2104d8d8eeb87c06e8c594b", "width": 216, "height": 121}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=1fb00ddb4a2d99782e7de7cde51676f0db742639", "width": 320, "height": 180}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=f7ed52b21d8431d206e4858a89b65eb9a4630955", "width": 640, "height": 360}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=b0580c69aeaa769a86051f67d7385a6c5bee2db4", "width": 960, "height": 540}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=747dd834ecf741b4de8a13d5a4f725220bd91edb", "width": 1080, "height": 607}], "variants": {}, "id": "kSMg4tuE5uFPfCQ-p6TNLozpkw7k_rxRNpv2tvuvhbY"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "dbf9nj", "is_robot_indexable": true, "report_reasons": null, "author": "landoflobsters", "discussion_type": null, "num_comments": 9946, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/dbf9nj/changes_to_our_policy_against_bullying_and/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/dbf9nj/changes_to_our_policy_against_bullying_and/", "subreddit_subscribers": *********, "created_utc": 1569865578.0, "num_crossposts": 67, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "", "user_reports": [], "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "An Update on Content Manipulation… And an Upcoming Report", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_d6li3o", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.76, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 10955, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "author_fullname": "t2_qromv", "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 10955, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "default", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "crosspost_parent_list": [{"approved_at_utc": null, "subreddit": "RedditSafety", "selftext": "**TL;DR:** Bad actors never sleep, and we are always evolving how we identify and mitigate them. But with the upcoming election, we know you want to see more. So we're committing to a quarterly report on content manipulation and account security, with the first to be shared in October. But first, we want to share context today on the history of content manipulation efforts and how we've evolved over the years to keep the site authentic.\n\n**A brief history** \n\nThe concern of content manipulation on Reddit is as old as Reddit itself. Before there were subreddits (circa 2005), everyone saw the same content and we were primarily concerned with spam and vote manipulation. As we grew in scale and introduced subreddits, we had to become more sophisticated in our detection and mitigation of these issues. The creation of subreddits also created new threats, with “brigading” becoming a more common occurrence (even if rarely defined). Today, we are not only dealing with growth hackers, bots, and your typical shitheadery, but we have to worry about more advanced threats, such as state actors interested in interfering with elections and inflaming social divisions. This represents an evolution in content manipulation, not only on Reddit, but across the internet. These advanced adversaries have resources far larger than a typical spammer. However, as with early days at Reddit, we are committed to combating this threat, while better empowering users and moderators to minimize exposure to inauthentic or manipulated content.\n\n**What we’ve done** \n\nOur strategy has been to focus on fundamentals and double down on things that have protected our platform in the past (including the [2016 election](https://www.reddit.com/r/announcements/comments/8bb85p/reddits_2017_transparency_report_and_suspect/)). Influence campaigns represent an evolution in content manipulation, not something fundamentally new. This means that these campaigns are built on top of some of the same tactics as historical manipulators (certainly with their own flavor). Namely, compromised accounts, vote manipulation, and inauthentic community engagement. This is why we have hardened our protections against these types of issues on the site. \n\n*Compromised accounts* \n\nThis year alone, we have taken preventative actions on over 10.6M accounts with compromised login credentials ([check yo’ self](https://haveibeenpwned.com/)), or accounts that have been hit by bots attempting to breach them. This is important because compromised accounts can be used to gain immediate credibility on the site, and to quickly scale up a content attack on the site (yes, even that throwaway account with password = Password! is a potential threat!).\n\n*Vote Manipulation*\n\nThe purpose of our anti-cheating rules is to make it difficult for a person to unduly impact the votes on a particular piece of content. These rules, along with user downvotes (because you know bad content when you see it), are some of the most powerful protections we have to ensure that misinformation and low quality content doesn’t get much traction on Reddit. We have strengthened these protections (in ways we can’t fully share without giving away the secret sauce). As a result, we have reduced the visibility of vote manipulated content by 20% over the last 12 months.\n\n*Content Manipulation*\n\nContent manipulation is a term we use to combine things like spam, community interference, etc. We have completely overhauled how we handle these issues, including a stronger focus on proactive detection, and machine learning to help surface clusters of bad accounts. With our newer methods, we can make improvements in detection more quickly and ensure that we are more complete in taking down all accounts that are connected to any attempt. We removed over 900% more policy violating content in the first half of 2019 than the same period in 2018, and 99% of that was before it was reported by users. \n\n*User Empowerment* \n\nOutside of admin-level detection and mitigation, we recognize that a large part of what has kept the content on Reddit authentic is the users and moderators. In our [2017 transparency report](https://www.reddit.com/r/announcements/comments/8bb85p/reddits_2017_transparency_report_and_suspect/) we highlighted the relatively small impact that Russian trolls had on the site. 71% of the trolls had 0 karma or less! This is a direct consequence of you all, and we want to continue to empower you to play a strong role in the Reddit ecosystem. We are investing in a safety product team that will build improved safety (user and content) features on the site. We are still staffing this up, but we hope to deliver new features soon (including Crowd Control, which we are in the process of refining thanks to the good feedback from our alpha testers). These features will start to provide users and moderators better information and control over the type of content that is seen. \n\n**What’s next** \n\nThe next component of this battle is the collaborative aspect. As a consequence of the large resources available to state-backed adversaries and their nefarious goals, it is important to recognize that this fight is not one that Reddit faces alone. In combating these advanced adversaries, we will collaborate with other players in this space, including law enforcement, and other platforms. By working with these groups, we can better investigate threats as they occur  on Reddit. \n\n**Our commitment** \n\nThese adversaries are more advanced than previous ones, but we are committed to ensuring that Reddit content is free from manipulation. At times, some of our efforts may seem heavy handed (forcing password resets), and other times they may be more opaque, but know that behind the scenes we are working hard on these problems. In order to provide additional transparency around our actions, we will publish a narrow scope security-report each quarter. This will focus on actions surrounding content manipulation and account security (note, it will not include any of the information on legal requests and day-to-day content policy removals, as these will continue to be released annually in our[ Transparency Report](https://www.redditinc.com/policies/transparency-report-2018)). We will get our first one out in October. If there is specific information you’d like or questions you have, let us know in the comments below.\n\n[EDIT: Im signing off, thank you all for the great questions and feedback. I'll check back in on this occasionally and try to reply as much as feasible.]", "author_fullname": "t2_qromv", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "An Update on Content Manipulation… And an Upcoming Report", "link_flair_richtext": [], "subreddit_name_prefixed": "r/RedditSafety", "hidden": false, "pwls": null, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_d6l41l", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.81, "author_flair_background_color": null, "subreddit_type": "restricted", "ups": 5122, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 5122, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "self", "edited": 1568937842.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1568930673.0, "link_flair_type": "text", "wls": null, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.RedditSafety", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;&lt;strong&gt;TL;DR:&lt;/strong&gt; Bad actors never sleep, and we are always evolving how we identify and mitigate them. But with the upcoming election, we know you want to see more. So we&amp;#39;re committing to a quarterly report on content manipulation and account security, with the first to be shared in October. But first, we want to share context today on the history of content manipulation efforts and how we&amp;#39;ve evolved over the years to keep the site authentic.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;A brief history&lt;/strong&gt; &lt;/p&gt;\n\n&lt;p&gt;The concern of content manipulation on Reddit is as old as Reddit itself. Before there were subreddits (circa 2005), everyone saw the same content and we were primarily concerned with spam and vote manipulation. As we grew in scale and introduced subreddits, we had to become more sophisticated in our detection and mitigation of these issues. The creation of subreddits also created new threats, with “brigading” becoming a more common occurrence (even if rarely defined). Today, we are not only dealing with growth hackers, bots, and your typical shitheadery, but we have to worry about more advanced threats, such as state actors interested in interfering with elections and inflaming social divisions. This represents an evolution in content manipulation, not only on Reddit, but across the internet. These advanced adversaries have resources far larger than a typical spammer. However, as with early days at Reddit, we are committed to combating this threat, while better empowering users and moderators to minimize exposure to inauthentic or manipulated content.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;What we’ve done&lt;/strong&gt; &lt;/p&gt;\n\n&lt;p&gt;Our strategy has been to focus on fundamentals and double down on things that have protected our platform in the past (including the &lt;a href=\"https://www.reddit.com/r/announcements/comments/8bb85p/reddits_2017_transparency_report_and_suspect/\"&gt;2016 election&lt;/a&gt;). Influence campaigns represent an evolution in content manipulation, not something fundamentally new. This means that these campaigns are built on top of some of the same tactics as historical manipulators (certainly with their own flavor). Namely, compromised accounts, vote manipulation, and inauthentic community engagement. This is why we have hardened our protections against these types of issues on the site. &lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Compromised accounts&lt;/em&gt; &lt;/p&gt;\n\n&lt;p&gt;This year alone, we have taken preventative actions on over 10.6M accounts with compromised login credentials (&lt;a href=\"https://haveibeenpwned.com/\"&gt;check yo’ self&lt;/a&gt;), or accounts that have been hit by bots attempting to breach them. This is important because compromised accounts can be used to gain immediate credibility on the site, and to quickly scale up a content attack on the site (yes, even that throwaway account with password = Password! is a potential threat!).&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Vote Manipulation&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;The purpose of our anti-cheating rules is to make it difficult for a person to unduly impact the votes on a particular piece of content. These rules, along with user downvotes (because you know bad content when you see it), are some of the most powerful protections we have to ensure that misinformation and low quality content doesn’t get much traction on Reddit. We have strengthened these protections (in ways we can’t fully share without giving away the secret sauce). As a result, we have reduced the visibility of vote manipulated content by 20% over the last 12 months.&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Content Manipulation&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;Content manipulation is a term we use to combine things like spam, community interference, etc. We have completely overhauled how we handle these issues, including a stronger focus on proactive detection, and machine learning to help surface clusters of bad accounts. With our newer methods, we can make improvements in detection more quickly and ensure that we are more complete in taking down all accounts that are connected to any attempt. We removed over 900% more policy violating content in the first half of 2019 than the same period in 2018, and 99% of that was before it was reported by users. &lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;User Empowerment&lt;/em&gt; &lt;/p&gt;\n\n&lt;p&gt;Outside of admin-level detection and mitigation, we recognize that a large part of what has kept the content on Reddit authentic is the users and moderators. In our &lt;a href=\"https://www.reddit.com/r/announcements/comments/8bb85p/reddits_2017_transparency_report_and_suspect/\"&gt;2017 transparency report&lt;/a&gt; we highlighted the relatively small impact that Russian trolls had on the site. 71% of the trolls had 0 karma or less! This is a direct consequence of you all, and we want to continue to empower you to play a strong role in the Reddit ecosystem. We are investing in a safety product team that will build improved safety (user and content) features on the site. We are still staffing this up, but we hope to deliver new features soon (including Crowd Control, which we are in the process of refining thanks to the good feedback from our alpha testers). These features will start to provide users and moderators better information and control over the type of content that is seen. &lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;What’s next&lt;/strong&gt; &lt;/p&gt;\n\n&lt;p&gt;The next component of this battle is the collaborative aspect. As a consequence of the large resources available to state-backed adversaries and their nefarious goals, it is important to recognize that this fight is not one that Reddit faces alone. In combating these advanced adversaries, we will collaborate with other players in this space, including law enforcement, and other platforms. By working with these groups, we can better investigate threats as they occur  on Reddit. &lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Our commitment&lt;/strong&gt; &lt;/p&gt;\n\n&lt;p&gt;These adversaries are more advanced than previous ones, but we are committed to ensuring that Reddit content is free from manipulation. At times, some of our efforts may seem heavy handed (forcing password resets), and other times they may be more opaque, but know that behind the scenes we are working hard on these problems. In order to provide additional transparency around our actions, we will publish a narrow scope security-report each quarter. This will focus on actions surrounding content manipulation and account security (note, it will not include any of the information on legal requests and day-to-day content policy removals, as these will continue to be released annually in our&lt;a href=\"https://www.redditinc.com/policies/transparency-report-2018\"&gt; Transparency Report&lt;/a&gt;). We will get our first one out in October. If there is specific information you’d like or questions you have, let us know in the comments below.&lt;/p&gt;\n\n&lt;p&gt;[EDIT: Im signing off, thank you all for the great questions and feedback. I&amp;#39;ll check back in on this occasionally and try to reply as much as feasible.]&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_vm1db", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "d6l41l", "is_robot_indexable": true, "report_reasons": null, "author": "worstnerd", "discussion_type": null, "num_comments": 2713, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/RedditSafety/comments/d6l41l/an_update_on_content_manipulation_and_an_upcoming/", "stickied": false, "url": "https://www.reddit.com/r/RedditSafety/comments/d6l41l/an_update_on_content_manipulation_and_an_upcoming/", "subreddit_subscribers": 40199, "created_utc": 1568930673.0, "num_crossposts": 24, "media": null, "is_video": false}], "created": 1568932423.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.redditsecurity", "allow_live_comments": true, "selftext_html": null, "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "url_overridden_by_dest": "https://old.reddit.com/r/redditsecurity/comments/d6l41l/an_update_on_content_manipulation_and_an_upcoming/", "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "d6li3o", "is_robot_indexable": true, "report_reasons": null, "author": "worstnerd", "discussion_type": null, "num_comments": 1, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "crosspost_parent": "t3_d6l41l", "author_flair_text_color": null, "permalink": "/r/announcements/comments/d6li3o/an_update_on_content_manipulation_and_an_upcoming/", "stickied": false, "url": "https://old.reddit.com/r/redditsecurity/comments/d6l41l/an_update_on_content_manipulation_and_an_upcoming/", "subreddit_subscribers": *********, "created_utc": 1568932423.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "", "user_reports": [], "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Announcing RPAN, a limited-time live broadcasting experience", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_ct2gcb", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.64, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 7838, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "author_fullname": "t2_16q6epyk", "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 7838, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "default", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "link", "content_categories": null, "is_self": false, "mod_note": null, "crosspost_parent_list": [{"approved_at_utc": null, "subreddit": "pan", "selftext": "Hi Reddit! We’re back with a new experience for the community, the Reddit Public Access Network ([RPAN](http://www.reddit.com/rpan)). Starting August 19 until 5PM PT, and from 9AM-5PM PT through Friday, August 23, redditors around the world will be able to create live broadcasts. In true Reddit fashion, voting will determine the top broadcast, and you can explore different broadcasts by swiping or clicking right or left. As you move further from the top broadcast, the broadcasts you see will be increasingly more random, so we encourage you to explore and vote!\n\nFirst and foremost, this is about having fun as a Reddit community, and if you all enjoy it, we’ll continue to explore how it might work as an actual feature. So if you have thoughts, suggestions, or other feedback, please share that in the comments of this post. We genuinely want to hear what you all think, and we look through all of the comments we can, including those without many upvotes.\n\nWe’re rolling out the RPAN experience progressively across Reddit starting August 19, so it’s possible that some people may see RPAN earlier than others.\n\n**Some general rules for broadcasting with RPAN:**\n\n* RPAN is a Safe for Work experience—Nudity, sexually suggestive content, graphic violence, illegal/dangerous behavior, hoax promotion, or content that would be seen as highly offensive/upsetting to the average redditor will result in a banned account\n* All redditors may see your stream, so don’t show yourself if you want to stay anonymous\n* Be like the Lambeosaurus—feed on pine needles and have a good time\n\nRead the full rules[ here](https://www.redditinc.com/policies/broadcasting-content-policy).", "author_fullname": "t2_16q6epyk", "saved": false, "mod_reason_title": null, "gilded": 2, "clicked": false, "title": "Announcing RPAN, a limited-time live broadcasting experience", "link_flair_richtext": [{"e": "text", "t": "Admin Posts"}], "subreddit_name_prefixed": "r/pan", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_csjqqy", "quarantine": false, "link_flair_text_color": "light", "upvote_ratio": 0.93, "author_flair_background_color": "#ea0027", "subreddit_type": "restricted", "ups": 6683, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": "fd7c271c-1c71-11ea-b286-0e53ee8710f3", "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Admin Posts", "can_mod_post": false, "score": 6683, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "self", "edited": 1566316957.0, "author_flair_css_class": null, "author_flair_richtext": [{"e": "text", "t": "Reddit Admin"}], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1566233355.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "richtext", "domain": "self.pan", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Hi Reddit! We’re back with a new experience for the community, the Reddit Public Access Network (&lt;a href=\"http://www.reddit.com/rpan\"&gt;RPAN&lt;/a&gt;). Starting August 19 until 5PM PT, and from 9AM-5PM PT through Friday, August 23, redditors around the world will be able to create live broadcasts. In true Reddit fashion, voting will determine the top broadcast, and you can explore different broadcasts by swiping or clicking right or left. As you move further from the top broadcast, the broadcasts you see will be increasingly more random, so we encourage you to explore and vote!&lt;/p&gt;\n\n&lt;p&gt;First and foremost, this is about having fun as a Reddit community, and if you all enjoy it, we’ll continue to explore how it might work as an actual feature. So if you have thoughts, suggestions, or other feedback, please share that in the comments of this post. We genuinely want to hear what you all think, and we look through all of the comments we can, including those without many upvotes.&lt;/p&gt;\n\n&lt;p&gt;We’re rolling out the RPAN experience progressively across Reddit starting August 19, so it’s possible that some people may see RPAN earlier than others.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;Some general rules for broadcasting with RPAN:&lt;/strong&gt;&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;RPAN is a Safe for Work experience—Nudity, sexually suggestive content, graphic violence, illegal/dangerous behavior, hoax promotion, or content that would be seen as highly offensive/upsetting to the average redditor will result in a banned account&lt;/li&gt;\n&lt;li&gt;All redditors may see your stream, so don’t show yourself if you want to stay anonymous&lt;/li&gt;\n&lt;li&gt;Be like the Lambeosaurus—feed on pine needles and have a good time&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;Read the full rules&lt;a href=\"https://www.redditinc.com/policies/broadcasting-content-policy\"&gt; here&lt;/a&gt;.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?auto=webp&amp;s=f65608a0e50b80501b09dd4e6d4256a073bddaf7", "width": 1920, "height": 1080}, "resolutions": [{"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=f58daaa031b1949d7fba33ff04daa716f834c5fa", "width": 108, "height": 60}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=ef7c929673f1ddd3f2104d8d8eeb87c06e8c594b", "width": 216, "height": 121}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=1fb00ddb4a2d99782e7de7cde51676f0db742639", "width": 320, "height": 180}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=f7ed52b21d8431d206e4858a89b65eb9a4630955", "width": 640, "height": 360}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=b0580c69aeaa769a86051f67d7385a6c5bee2db4", "width": 960, "height": 540}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=747dd834ecf741b4de8a13d5a4f725220bd91edb", "width": 1080, "height": 607}], "variants": {}, "id": "kSMg4tuE5uFPfCQ-p6TNLozpkw7k_rxRNpv2tvuvhbY"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "0422f52a-dfe9-11e9-96a4-0e7faf2cba02", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": "Reddit Admin", "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2set6", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#005ba1", "id": "csjqqy", "is_robot_indexable": true, "report_reasons": null, "author": "Sn00byD00", "discussion_type": null, "num_comments": 967, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": "light", "permalink": "/r/pan/comments/csjqqy/announcing_rpan_a_limitedtime_live_broadcasting/", "stickied": false, "url": "https://www.reddit.com/r/pan/comments/csjqqy/announcing_rpan_a_limitedtime_live_broadcasting/", "subreddit_subscribers": 183622, "created_utc": 1566233355.0, "num_crossposts": 12, "media": null, "is_video": false}], "created": 1566321359.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.pan", "allow_live_comments": true, "selftext_html": null, "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "url_overridden_by_dest": "https://www.reddit.com/r/pan/comments/csjqqy/announcing_rpan_a_limitedtime_live_broadcasting/", "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?auto=webp&amp;s=f65608a0e50b80501b09dd4e6d4256a073bddaf7", "width": 1920, "height": 1080}, "resolutions": [{"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=f58daaa031b1949d7fba33ff04daa716f834c5fa", "width": 108, "height": 60}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=ef7c929673f1ddd3f2104d8d8eeb87c06e8c594b", "width": 216, "height": 121}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=1fb00ddb4a2d99782e7de7cde51676f0db742639", "width": 320, "height": 180}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=f7ed52b21d8431d206e4858a89b65eb9a4630955", "width": 640, "height": 360}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=b0580c69aeaa769a86051f67d7385a6c5bee2db4", "width": 960, "height": 540}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=747dd834ecf741b4de8a13d5a4f725220bd91edb", "width": 1080, "height": 607}], "variants": {}, "id": "kSMg4tuE5uFPfCQ-p6TNLozpkw7k_rxRNpv2tvuvhbY"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "ct2gcb", "is_robot_indexable": true, "report_reasons": null, "author": "Sn00byD00", "discussion_type": null, "num_comments": 1093, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "crosspost_parent": "t3_csjqqy", "author_flair_text_color": null, "permalink": "/r/announcements/comments/ct2gcb/announcing_rpan_a_limitedtime_live_broadcasting/", "stickied": false, "url": "https://www.reddit.com/r/pan/comments/csjqqy/announcing_rpan_a_limitedtime_live_broadcasting/", "subreddit_subscribers": *********, "created_utc": 1566321359.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "**UPDATE (9/4):** Winners of the Coins Giveaway have been [announced below in the stickied comment](https://www.reddit.com/r/announcements/comments/chdx1h/introducing_community_awards/eus5nc4?utm_source=share&amp;utm_medium=web2x)! Thanks to all who participated!\n\nHi all,\n\nYou may have noticed some new icons popping up alongside Silver, Gold, and Platinum Awards on your front page recently—these are **Community Awards**! We started testing these in a [small alpha group back in April](https://www.reddit.com/r/changelog/comments/bf2jep/hi_rchangelog_were_back_for_another_exciting/) and expanded the group to include more volunteer communities over the past couple of weeks.\n\nAs of today, Community Awards are now widely available for mods to create in their communities.\n\n# What Are Community Awards?\n\nCommunity Awards give mods the ability to create custom Awards for redditors to use in their own communities. Mods can select the images, names, and Coin price of Awards to reflect their own communities. Awards can be priced between 500 Coins and 40,000 Coins.\n\nCommunity Awards will be available to give in the communities that created them, in addition to Silver, Gold, and Platinum Awards (which are available site-wide).\n\n[A highly decorated post on r\\/DunderMifflin, featuring Silver, <PERSON>, and <PERSON>, as well as the new Community Awards!](https://preview.redd.it/fsqt84ep6bc31.png?width=1928&amp;format=png&amp;auto=webp&amp;s=735a9a37bb33c8d2139d9982cd68908c920ed19f)\n\nIn the above screenshot from [r/DunderMifflin](https://www.reddit.com/r/DunderMifflin/), you can see a few new icons in between Gold and Silver. These are **Community Awards**.\n\n# What Are the Benefits of Community Awards?\n\nCommunity Awards are a new way of showing appreciation to posters and commenters. But unlike Silver, Gold and Platinum, when Community Awards are used, they give Coins back to that community through the **Community Bank**.\n\nWith this new update, 20% of Coins spent on Community Awards will go into a bank of Community Coins. For example, in the [r/IAmA](https://www.reddit.com/r/IAmA/) community if you give the “Star of Excellence” Award (2,000 Coins) to another user, [r/IAmA](https://www.reddit.com/r/IAmA/) automatically gets 400 Coins in its Community Bank.\n\nMods can access the Community Bank to give…\n\n# Mod-Exclusive Awards\n\nModerators will now have the ability to give Mod-Exclusive Awards, to recognize users for high-quality content that is representative of their community.\n\nMod-Exclusive Awards will draw from the bank of Community Coins, so Moderators don’t need to spend money to reward users (e.g., for community contests). Mod-Exclusive Awards also have the additional benefit of 1 or more months of Reddit Premium, depending on the Award price.\n\n* Mod-Award costing **1,800 Coins** = **1 month** of Reddit Premium\n* Mod-Award costing **5,400 Coins** = **3 months** of Reddit Premium\n* … and so on!\n\nHere’s what Mod-Exclusive Awards look like on posts / comments:\n\n&amp;#x200B;\n\n[This example shows the coveted Golden Toaster Award, which you can view in a larger size by hovering over the icon.](https://preview.redd.it/rek8d44u6bc31.png?width=300&amp;format=png&amp;auto=webp&amp;s=56520aaebbfdee38fd9cb0f80bc6339924222e40)\n\n# Which Communities Are Eligible for Community Awards?\n\nCommunity Awards are available to public, SFW, non-banned, non-quarantined communities.\n\n# Great! How Do I Go and Create Awards Now?\n\nCheck out [our companion post on r/modnews](https://www.reddit.com/r/modnews/comments/chdxkh/community_awards_creating_new_awards_for_users/) for all the details on how mods can create Awards!\n\nWe are looking forward to seeing all your creativity with these new Awards, but **please do note these important considerations** when creating Awards:\n\n* They must comply with [Reddit’s Content Policy](https://www.redditinc.com/policies/content-policy);\n* They must not violate intellectual property rights of others; and\n* They must be SFW.\n\n# A Coin Giveaway: Mods, Create Some New Awards!\n\nWe've seen some pretty great Awards pop up in a few subs already, but now that they're available to more mod teams, we’re seeing which community can create the best collection of six Community Awards!\n\nParticipating is pretty simple: **If you are a mod, create an amazing set of six Community Awards that exemplifies the culture of your community, and reply to the stickied comment below with the name of your community.** For 20 random entries, we will put 40,000 Coins into to each community's Community Bank, to give back to users in your communities!", "author_fullname": "t2_vlvx0rz", "saved": false, "mod_reason_title": null, "gilded": 2, "clicked": false, "title": "Introducing Community Awards!", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": 78, "top_awarded_type": null, "hide_score": false, "media_metadata": {"rek8d44u6bc31": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 111, "x": 108, "u": "https://preview.redd.it/rek8d44u6bc31.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=ffd68530dd271a0aac37852ee9386bc2b480974e"}, {"y": 222, "x": 216, "u": "https://preview.redd.it/rek8d44u6bc31.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=dcd2ec80e7f6620878287d8cf1b17a3afb0bfb78"}], "s": {"y": 309, "x": 300, "u": "https://preview.redd.it/rek8d44u6bc31.png?width=300&amp;format=png&amp;auto=webp&amp;s=56520aaebbfdee38fd9cb0f80bc6339924222e40"}, "id": "rek8d44u6bc31"}, "fsqt84ep6bc31": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 64, "x": 108, "u": "https://preview.redd.it/fsqt84ep6bc31.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=b4964a937e2f8fbc292f48ff7f8e7c08db540883"}, {"y": 128, "x": 216, "u": "https://preview.redd.it/fsqt84ep6bc31.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=c0f16de401a1be1327148ee959e6a558e99f322e"}, {"y": 190, "x": 320, "u": "https://preview.redd.it/fsqt84ep6bc31.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=12ce6f923e9dea9046521c7e2a80045b1c30deb0"}, {"y": 381, "x": 640, "u": "https://preview.redd.it/fsqt84ep6bc31.png?width=640&amp;crop=smart&amp;auto=webp&amp;s=20041342786f1f5f98187832bb2d2c4465aede33"}, {"y": 572, "x": 960, "u": "https://preview.redd.it/fsqt84ep6bc31.png?width=960&amp;crop=smart&amp;auto=webp&amp;s=f261d868ede280a2e1f2bc7295461c84420069ef"}, {"y": 644, "x": 1080, "u": "https://preview.redd.it/fsqt84ep6bc31.png?width=1080&amp;crop=smart&amp;auto=webp&amp;s=65c4744762c7ab57257927ced26f56b8bed567db"}], "s": {"y": 1150, "x": 1928, "u": "https://preview.redd.it/fsqt84ep6bc31.png?width=1928&amp;format=png&amp;auto=webp&amp;s=735a9a37bb33c8d2139d9982cd68908c920ed19f"}, "id": "fsqt84ep6bc31"}}, "name": "t3_chdx1h", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.71, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 13876, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": 140, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 13876, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "https://a.thumbs.redditmedia.com/-D0hxchgus9EyzvGqvnabK_3Obl93tMfG9G58V1QwW8.jpg", "edited": 1567640499.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": 1564001958.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;&lt;strong&gt;UPDATE (9/4):&lt;/strong&gt; Winners of the Coins Giveaway have been &lt;a href=\"https://www.reddit.com/r/announcements/comments/chdx1h/introducing_community_awards/eus5nc4?utm_source=share&amp;amp;utm_medium=web2x\"&gt;announced below in the stickied comment&lt;/a&gt;! Thanks to all who participated!&lt;/p&gt;\n\n&lt;p&gt;Hi all,&lt;/p&gt;\n\n&lt;p&gt;You may have noticed some new icons popping up alongside Silver, Gold, and Platinum Awards on your front page recently—these are &lt;strong&gt;Community Awards&lt;/strong&gt;! We started testing these in a &lt;a href=\"https://www.reddit.com/r/changelog/comments/bf2jep/hi_rchangelog_were_back_for_another_exciting/\"&gt;small alpha group back in April&lt;/a&gt; and expanded the group to include more volunteer communities over the past couple of weeks.&lt;/p&gt;\n\n&lt;p&gt;As of today, Community Awards are now widely available for mods to create in their communities.&lt;/p&gt;\n\n&lt;h1&gt;What Are Community Awards?&lt;/h1&gt;\n\n&lt;p&gt;Community Awards give mods the ability to create custom Awards for redditors to use in their own communities. Mods can select the images, names, and Coin price of Awards to reflect their own communities. Awards can be priced between 500 Coins and 40,000 Coins.&lt;/p&gt;\n\n&lt;p&gt;Community Awards will be available to give in the communities that created them, in addition to Silver, Gold, and Platinum Awards (which are available site-wide).&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/fsqt84ep6bc31.png?width=1928&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=735a9a37bb33c8d2139d9982cd68908c920ed19f\"&gt;A highly decorated post on r/DunderMifflin, featuring Silver, Gold, and Platinum, as well as the new Community Awards!&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;In the above screenshot from &lt;a href=\"https://www.reddit.com/r/DunderMifflin/\"&gt;r/DunderMifflin&lt;/a&gt;, you can see a few new icons in between Gold and Silver. These are &lt;strong&gt;Community Awards&lt;/strong&gt;.&lt;/p&gt;\n\n&lt;h1&gt;What Are the Benefits of Community Awards?&lt;/h1&gt;\n\n&lt;p&gt;Community Awards are a new way of showing appreciation to posters and commenters. But unlike Silver, Gold and Platinum, when Community Awards are used, they give Coins back to that community through the &lt;strong&gt;Community Bank&lt;/strong&gt;.&lt;/p&gt;\n\n&lt;p&gt;With this new update, 20% of Coins spent on Community Awards will go into a bank of Community Coins. For example, in the &lt;a href=\"https://www.reddit.com/r/IAmA/\"&gt;r/IAmA&lt;/a&gt; community if you give the “Star of Excellence” Award (2,000 Coins) to another user, &lt;a href=\"https://www.reddit.com/r/IAmA/\"&gt;r/IAmA&lt;/a&gt; automatically gets 400 Coins in its Community Bank.&lt;/p&gt;\n\n&lt;p&gt;Mods can access the Community Bank to give…&lt;/p&gt;\n\n&lt;h1&gt;Mod-Exclusive Awards&lt;/h1&gt;\n\n&lt;p&gt;Moderators will now have the ability to give Mod-Exclusive Awards, to recognize users for high-quality content that is representative of their community.&lt;/p&gt;\n\n&lt;p&gt;Mod-Exclusive Awards will draw from the bank of Community Coins, so Moderators don’t need to spend money to reward users (e.g., for community contests). Mod-Exclusive Awards also have the additional benefit of 1 or more months of Reddit Premium, depending on the Award price.&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Mod-Award costing &lt;strong&gt;1,800 Coins&lt;/strong&gt; = &lt;strong&gt;1 month&lt;/strong&gt; of Reddit Premium&lt;/li&gt;\n&lt;li&gt;Mod-Award costing &lt;strong&gt;5,400 Coins&lt;/strong&gt; = &lt;strong&gt;3 months&lt;/strong&gt; of Reddit Premium&lt;/li&gt;\n&lt;li&gt;… and so on!&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;Here’s what Mod-Exclusive Awards look like on posts / comments:&lt;/p&gt;\n\n&lt;p&gt;&amp;#x200B;&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/rek8d44u6bc31.png?width=300&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=56520aaebbfdee38fd9cb0f80bc6339924222e40\"&gt;This example shows the coveted Golden Toaster Award, which you can view in a larger size by hovering over the icon.&lt;/a&gt;&lt;/p&gt;\n\n&lt;h1&gt;Which Communities Are Eligible for Community Awards?&lt;/h1&gt;\n\n&lt;p&gt;Community Awards are available to public, SFW, non-banned, non-quarantined communities.&lt;/p&gt;\n\n&lt;h1&gt;Great! How Do I Go and Create Awards Now?&lt;/h1&gt;\n\n&lt;p&gt;Check out &lt;a href=\"https://www.reddit.com/r/modnews/comments/chdxkh/community_awards_creating_new_awards_for_users/\"&gt;our companion post on r/modnews&lt;/a&gt; for all the details on how mods can create Awards!&lt;/p&gt;\n\n&lt;p&gt;We are looking forward to seeing all your creativity with these new Awards, but &lt;strong&gt;please do note these important considerations&lt;/strong&gt; when creating Awards:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;They must comply with &lt;a href=\"https://www.redditinc.com/policies/content-policy\"&gt;Reddit’s Content Policy&lt;/a&gt;;&lt;/li&gt;\n&lt;li&gt;They must not violate intellectual property rights of others; and&lt;/li&gt;\n&lt;li&gt;They must be SFW.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;h1&gt;A Coin Giveaway: Mods, Create Some New Awards!&lt;/h1&gt;\n\n&lt;p&gt;We&amp;#39;ve seen some pretty great Awards pop up in a few subs already, but now that they&amp;#39;re available to more mod teams, we’re seeing which community can create the best collection of six Community Awards!&lt;/p&gt;\n\n&lt;p&gt;Participating is pretty simple: &lt;strong&gt;If you are a mod, create an amazing set of six Community Awards that exemplifies the culture of your community, and reply to the stickied comment below with the name of your community.&lt;/strong&gt; For 20 random entries, we will put 40,000 Coins into to each community&amp;#39;s Community Bank, to give back to users in your communities!&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?auto=webp&amp;s=f65608a0e50b80501b09dd4e6d4256a073bddaf7", "width": 1920, "height": 1080}, "resolutions": [{"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=108&amp;crop=smart&amp;auto=webp&amp;s=f58daaa031b1949d7fba33ff04daa716f834c5fa", "width": 108, "height": 60}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=216&amp;crop=smart&amp;auto=webp&amp;s=ef7c929673f1ddd3f2104d8d8eeb87c06e8c594b", "width": 216, "height": 121}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=320&amp;crop=smart&amp;auto=webp&amp;s=1fb00ddb4a2d99782e7de7cde51676f0db742639", "width": 320, "height": 180}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=640&amp;crop=smart&amp;auto=webp&amp;s=f7ed52b21d8431d206e4858a89b65eb9a4630955", "width": 640, "height": 360}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=960&amp;crop=smart&amp;auto=webp&amp;s=b0580c69aeaa769a86051f67d7385a6c5bee2db4", "width": 960, "height": 540}, {"url": "https://external-preview.redd.it/mxetgECWBzOXDlVg2wtnEssyBczIJQFZlum_1-Os9Zg.jpg?width=1080&amp;crop=smart&amp;auto=webp&amp;s=747dd834ecf741b4de8a13d5a4f725220bd91edb", "width": 1080, "height": 607}], "variants": {}, "id": "kSMg4tuE5uFPfCQ-p6TNLozpkw7k_rxRNpv2tvuvhbY"}], "enabled": false}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "chdx1h", "is_robot_indexable": true, "report_reasons": null, "author": "venkman01", "discussion_type": null, "num_comments": 1696, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/chdx1h/introducing_community_awards/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/chdx1h/introducing_community_awards/", "subreddit_subscribers": *********, "created_utc": 1564001958.0, "num_crossposts": 30, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "Edit (2019/11/26): This feature has been delayed until 2020\n\nEdit (2020/03/30): We released a feature where you will get a push notification when you get a new follower. If you have your push notifications enabled on our mobile apps, or desktop notifications enabled, you should receive one. We are working on expanding this feature to all users, even without push notifications. The follower list is still delayed until later this year.\n\nHi everyone,\n\nWe collect a lot of feedback from you all, and one theme we’ve heard consistently from users is that many of you want more visibility when users follow you. As we move the new profiles out of beta, we wanted to share a transparency change we are making. In the coming months, we will allow people to see which users follow them.\n\nWe know that this may be a change from existing expectations, so we want to give you time to update your settings before moving forward with this. In the immediate future (starting Aug 19th, 2019), this will only affect new follows made. In about 3 months, we will make it possible to see your full list of followers. This would include follows made while profiles were in beta.\n\nWe plan to send a PM to all affected users, but wanted to make this public post as well so that you aren’t surprised when you receive it. To be clear, the usernames will only be visible to the user who was followed. No one will be able to look up your full list of subscriptions/follows and no one else will be able to see a list of followers of a profile.\n\nIf you are someone who follows other users, please take a second to examine your subscription/follow list and make sure you are comfortable with those users being aware that you follow them. If you are someone who has followers, we will make another post when the ability to view your followers has been released. We’ll stick around in the comments for a bit if you have questions. If there are other features you’d like to see for profiles, please let us know!\n\nThanks!\n\nEdit: updated 8/29 to Aug 29th, 2019 as it's a more clear date format\n\nEdit: updated Aug 29th to Aug 19th to match release date of the start of the feature rollout", "author_fullname": "t2_5rbio", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Update regarding user profile transparency", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_cevm31", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.77, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 16900, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 16900, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "self", "edited": 1585613052.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1563471455.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Edit (2019/11/26): This feature has been delayed until 2020&lt;/p&gt;\n\n&lt;p&gt;Edit (2020/03/30): We released a feature where you will get a push notification when you get a new follower. If you have your push notifications enabled on our mobile apps, or desktop notifications enabled, you should receive one. We are working on expanding this feature to all users, even without push notifications. The follower list is still delayed until later this year.&lt;/p&gt;\n\n&lt;p&gt;Hi everyone,&lt;/p&gt;\n\n&lt;p&gt;We collect a lot of feedback from you all, and one theme we’ve heard consistently from users is that many of you want more visibility when users follow you. As we move the new profiles out of beta, we wanted to share a transparency change we are making. In the coming months, we will allow people to see which users follow them.&lt;/p&gt;\n\n&lt;p&gt;We know that this may be a change from existing expectations, so we want to give you time to update your settings before moving forward with this. In the immediate future (starting Aug 19th, 2019), this will only affect new follows made. In about 3 months, we will make it possible to see your full list of followers. This would include follows made while profiles were in beta.&lt;/p&gt;\n\n&lt;p&gt;We plan to send a PM to all affected users, but wanted to make this public post as well so that you aren’t surprised when you receive it. To be clear, the usernames will only be visible to the user who was followed. No one will be able to look up your full list of subscriptions/follows and no one else will be able to see a list of followers of a profile.&lt;/p&gt;\n\n&lt;p&gt;If you are someone who follows other users, please take a second to examine your subscription/follow list and make sure you are comfortable with those users being aware that you follow them. If you are someone who has followers, we will make another post when the ability to view your followers has been released. We’ll stick around in the comments for a bit if you have questions. If there are other features you’d like to see for profiles, please let us know!&lt;/p&gt;\n\n&lt;p&gt;Thanks!&lt;/p&gt;\n\n&lt;p&gt;Edit: updated 8/29 to Aug 29th, 2019 as it&amp;#39;s a more clear date format&lt;/p&gt;\n\n&lt;p&gt;Edit: updated Aug 29th to Aug 19th to match release date of the start of the feature rollout&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "cevm31", "is_robot_indexable": true, "report_reasons": null, "author": "mj<PERSON><PERSON><PERSON>", "discussion_type": null, "num_comments": 2636, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/cevm31/update_regarding_user_profile_transparency/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/cevm31/update_regarding_user_profile_transparency/", "subreddit_subscribers": *********, "created_utc": 1563471455.0, "num_crossposts": 20, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "Hello hello,\n\nWe’ve made some changes to Multireddits that we’d like to share with you. Also, a fun contest! Let’s get to it...\n\n## What’s New\n\n*New Name: Multireddits → Custom Feeds*\n\nMultireddits have been around a [long time](https://www.reddit.com/r/blog/comments/1iwd5i/new_create_and_share_your_own_collections_of/). They are a way for redditors to curate communities into shareable feeds and can help newer redditors discover more communities. However, they haven’t been widely adopted. In order to prevent confusion, we will be changing the name from Multireddits to “Custom Feeds.” Sometimes simple is better.\n\n*More Support*\n\nWe’ve added more support to new Reddit and our iOS app for Custom Feeds. Now, redditors can create a new Custom Feed, add or remove communities from them, duplicate other redditors' feeds, and change the privacy settings. Previously, this was something you could only do from old Reddit. We’ll be adding support for Custom Feeds on Android in the near future.\n\n[iOS Screenshot](https://preview.redd.it/jox6inka4my21.png?width=3360&amp;format=png&amp;auto=webp&amp;s=c92d7268dbcd46b35d6d2380b8316d4471dc1aac)\n\n*New Follow Functionality*\n\nYou can now follow another redditor’s Custom Feed (as long as it’s set to Public). This means that when you follow a feed it will appear on your list of Custom Feed subscriptions and when that redditor adds another community to the feed, you’ll see that update the next time you open the feed. This will be super useful for communities that want to keep a running feed of related communities, or for folks that have a specific ever-evolving interest that they want to share with others.\n\n[Follow on new Reddit](https://preview.redd.it/a6ztwd2b4my21.png?width=3360&amp;format=png&amp;auto=webp&amp;s=4576c32bf987c0dadf6a41024134387f96b45fb4)\n\n*Improved Sharing*\n\nWe’ve made some tweaks to the URL structure of your Custom Feed so that it’s easier to share with others. No more accidentally sharing a URL with /me/ in it that won’t work for anyone else.  We’ve also created a new privacy category for public custom feeds, Hidden, that can be accessed by anyone with the link but will not show up on your profile.\n\n*Spaces*\n\nYou can now have spaces in the name of your Custom Feed. [Enough said.](https://media.giphy.com/media/Wyt6sLEjKjaFjzybth/giphy.gif)\n\n*Mix and Match*\n\nFollow a mix of communities and profiles.\n\n*Coming Soon*\n\nIn the near future communities will be able to create Custom Feeds that are owned by the community, rather than an individual. This will also support turning the Related Communities sidebar widget into a feed. We are also going to be building ways for you to see popular and trending Custom Feeds.\n\n## The Contest\n\nWe are investing in this feature because we believe redditors are great at finding niche communities and we want more people to discover all of the unique communities that we have. Now for the [gold](https://media.giphy.com/media/30LbRh69K3fO/giphy.gif) part! We are holding a one-week contest for the best Custom Feeds created by redditors. The winners will receive Coins and bragging rights.\n\nTo submit your Custom Feed, reply to the top-level sticky comment with a link to your Custom Feed and the category it best fits under. **It must be public.** Please only submit one feed per category.\n\nHere are the categories that we will award winners from:\n\n* Aww\n* Artist Resources\n* Beauty\n* Books &amp; Writing\n* Cool Pictures (images only)\n* Discussion (text only)\n* Fashion\n* Food &amp; Cooking\n* Health &amp; Fitness\n* Music\n* Parenting\n* Quirky\n* Sports\n* Travel\n* Wholesome\n\nWe’ll be picking winners based on a combination of the number of followers the feed has, how many upvotes their comment entry has, and our internal voting. Winners will be announced in a follow-up post next week.\n\nHere are some custom feeds to get you started (many of which are mine):\n\n* [Some of my favorite communities](https://www.reddit.com/user/singshredcode/m/singshredcodesubbies/)\n* Some of my [voice and singing communities](https://www.reddit.com/user/singshredcode/m/voices/)\n* [Animal Subbies](https://www.reddit.com/user/singshredcode/m/animal_subbies/) (If you know of any cute-animal subs I missed, please comment below)\n* [Asks](https://www.reddit.com/user/mujhair/m/asks/)\n* Some [Admins’ favorites](https://www.reddit.com/user/reddit/m/admin_faves/) that were mentioned in this [r/askreddit thread](https://www.reddit.com/r/AskReddit/comments/afzfa7/admins_of_reddit_whats_your_favorite_subreddit/) a few months ago\n\nSee the sticky comment below to enter your Custom Feed.\n\nEdit: Added a quirky category", "author_fullname": "t2_11ilczno", "saved": false, "mod_reason_title": null, "gilded": 1, "clicked": false, "title": "Introducing Custom Feeds (plus: a Community Contest with modest prizes!)", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": 140, "top_awarded_type": null, "hide_score": false, "media_metadata": {"a6ztwd2b4my21": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 60, "x": 108, "u": "https://preview.redd.it/a6ztwd2b4my21.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=696aa19dd990c581862a7d75387d8dd3c85ebb8f"}, {"y": 121, "x": 216, "u": "https://preview.redd.it/a6ztwd2b4my21.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=ec9d16908244bd34bf11359507cfc6025a5f4973"}, {"y": 180, "x": 320, "u": "https://preview.redd.it/a6ztwd2b4my21.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=0f2f357d1c6195d0d1604f3e75e752c1df0d4f3e"}, {"y": 360, "x": 640, "u": "https://preview.redd.it/a6ztwd2b4my21.png?width=640&amp;crop=smart&amp;auto=webp&amp;s=1b4f869c2a783e35b149dffb71d6be73de8f8c7b"}, {"y": 541, "x": 960, "u": "https://preview.redd.it/a6ztwd2b4my21.png?width=960&amp;crop=smart&amp;auto=webp&amp;s=248529b23ac72f6c73cf8b33de2cdadbb21e2487"}, {"y": 608, "x": 1080, "u": "https://preview.redd.it/a6ztwd2b4my21.png?width=1080&amp;crop=smart&amp;auto=webp&amp;s=9979a632b18eb96dc3cd814e6a23be175e0c9501"}], "s": {"y": 1894, "x": 3360, "u": "https://preview.redd.it/a6ztwd2b4my21.png?width=3360&amp;format=png&amp;auto=webp&amp;s=4576c32bf987c0dadf6a41024134387f96b45fb4"}, "id": "a6ztwd2b4my21"}, "jox6inka4my21": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 60, "x": 108, "u": "https://preview.redd.it/jox6inka4my21.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=0a8dfc09803dc8690ca4a06d49338b1b61344489"}, {"y": 121, "x": 216, "u": "https://preview.redd.it/jox6inka4my21.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=27e4beeb7a0f381c55ef0facf176d54ba2d79ee1"}, {"y": 180, "x": 320, "u": "https://preview.redd.it/jox6inka4my21.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=92691ded57d4872a0b362a84ccb03d98a08a550e"}, {"y": 360, "x": 640, "u": "https://preview.redd.it/jox6inka4my21.png?width=640&amp;crop=smart&amp;auto=webp&amp;s=513ded3c4a44269f985f52def81e9090e7ac8209"}, {"y": 540, "x": 960, "u": "https://preview.redd.it/jox6inka4my21.png?width=960&amp;crop=smart&amp;auto=webp&amp;s=3a31d27d89985c2d90d2c764eb97e530da5e8abe"}, {"y": 608, "x": 1080, "u": "https://preview.redd.it/jox6inka4my21.png?width=1080&amp;crop=smart&amp;auto=webp&amp;s=4bdf32f74a375036864f2aecec2df0f737926f9e"}], "s": {"y": 1892, "x": 3360, "u": "https://preview.redd.it/jox6inka4my21.png?width=3360&amp;format=png&amp;auto=webp&amp;s=c92d7268dbcd46b35d6d2380b8316d4471dc1aac"}, "id": "jox6inka4my21"}}, "name": "t3_bpfyx1", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.77, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 8803, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": 140, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 8803, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": true, "thumbnail": "https://b.thumbs.redditmedia.com/zCaV9kuSMZxmMvdmErGyUsYNdPrmX8LP9_Ee4QTT16g.jpg", "edited": 1558033312.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "post_hint": "self", "content_categories": null, "is_self": true, "mod_note": null, "created": **********.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.announcements", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Hello hello,&lt;/p&gt;\n\n&lt;p&gt;We’ve made some changes to Multireddits that we’d like to share with you. Also, a fun contest! Let’s get to it...&lt;/p&gt;\n\n&lt;h2&gt;What’s New&lt;/h2&gt;\n\n&lt;p&gt;&lt;em&gt;New Name: Multireddits → Custom Feeds&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;Multireddits have been around a &lt;a href=\"https://www.reddit.com/r/blog/comments/1iwd5i/new_create_and_share_your_own_collections_of/\"&gt;long time&lt;/a&gt;. They are a way for redditors to curate communities into shareable feeds and can help newer redditors discover more communities. However, they haven’t been widely adopted. In order to prevent confusion, we will be changing the name from Multireddits to “Custom Feeds.” Sometimes simple is better.&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;More Support&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;We’ve added more support to new Reddit and our iOS app for Custom Feeds. Now, redditors can create a new Custom Feed, add or remove communities from them, duplicate other redditors&amp;#39; feeds, and change the privacy settings. Previously, this was something you could only do from old Reddit. We’ll be adding support for Custom Feeds on Android in the near future.&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/jox6inka4my21.png?width=3360&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=c92d7268dbcd46b35d6d2380b8316d4471dc1aac\"&gt;iOS Screenshot&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;New Follow Functionality&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;You can now follow another redditor’s Custom Feed (as long as it’s set to Public). This means that when you follow a feed it will appear on your list of Custom Feed subscriptions and when that redditor adds another community to the feed, you’ll see that update the next time you open the feed. This will be super useful for communities that want to keep a running feed of related communities, or for folks that have a specific ever-evolving interest that they want to share with others.&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/a6ztwd2b4my21.png?width=3360&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=4576c32bf987c0dadf6a41024134387f96b45fb4\"&gt;Follow on new Reddit&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Improved Sharing&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;We’ve made some tweaks to the URL structure of your Custom Feed so that it’s easier to share with others. No more accidentally sharing a URL with /me/ in it that won’t work for anyone else.  We’ve also created a new privacy category for public custom feeds, Hidden, that can be accessed by anyone with the link but will not show up on your profile.&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Spaces&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;You can now have spaces in the name of your Custom Feed. &lt;a href=\"https://media.giphy.com/media/Wyt6sLEjKjaFjzybth/giphy.gif\"&gt;Enough said.&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Mix and Match&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;Follow a mix of communities and profiles.&lt;/p&gt;\n\n&lt;p&gt;&lt;em&gt;Coming Soon&lt;/em&gt;&lt;/p&gt;\n\n&lt;p&gt;In the near future communities will be able to create Custom Feeds that are owned by the community, rather than an individual. This will also support turning the Related Communities sidebar widget into a feed. We are also going to be building ways for you to see popular and trending Custom Feeds.&lt;/p&gt;\n\n&lt;h2&gt;The Contest&lt;/h2&gt;\n\n&lt;p&gt;We are investing in this feature because we believe redditors are great at finding niche communities and we want more people to discover all of the unique communities that we have. Now for the &lt;a href=\"https://media.giphy.com/media/30LbRh69K3fO/giphy.gif\"&gt;gold&lt;/a&gt; part! We are holding a one-week contest for the best Custom Feeds created by redditors. The winners will receive Coins and bragging rights.&lt;/p&gt;\n\n&lt;p&gt;To submit your Custom Feed, reply to the top-level sticky comment with a link to your Custom Feed and the category it best fits under. &lt;strong&gt;It must be public.&lt;/strong&gt; Please only submit one feed per category.&lt;/p&gt;\n\n&lt;p&gt;Here are the categories that we will award winners from:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Aww&lt;/li&gt;\n&lt;li&gt;Artist Resources&lt;/li&gt;\n&lt;li&gt;Beauty&lt;/li&gt;\n&lt;li&gt;Books &amp;amp; Writing&lt;/li&gt;\n&lt;li&gt;Cool Pictures (images only)&lt;/li&gt;\n&lt;li&gt;Discussion (text only)&lt;/li&gt;\n&lt;li&gt;Fashion&lt;/li&gt;\n&lt;li&gt;Food &amp;amp; Cooking&lt;/li&gt;\n&lt;li&gt;Health &amp;amp; Fitness&lt;/li&gt;\n&lt;li&gt;Music&lt;/li&gt;\n&lt;li&gt;Parenting&lt;/li&gt;\n&lt;li&gt;Quirky&lt;/li&gt;\n&lt;li&gt;Sports&lt;/li&gt;\n&lt;li&gt;Travel&lt;/li&gt;\n&lt;li&gt;Wholesome&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;We’ll be picking winners based on a combination of the number of followers the feed has, how many upvotes their comment entry has, and our internal voting. Winners will be announced in a follow-up post next week.&lt;/p&gt;\n\n&lt;p&gt;Here are some custom feeds to get you started (many of which are mine):&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;&lt;a href=\"https://www.reddit.com/user/singshredcode/m/singshredcodesubbies/\"&gt;Some of my favorite communities&lt;/a&gt;&lt;/li&gt;\n&lt;li&gt;Some of my &lt;a href=\"https://www.reddit.com/user/singshredcode/m/voices/\"&gt;voice and singing communities&lt;/a&gt;&lt;/li&gt;\n&lt;li&gt;&lt;a href=\"https://www.reddit.com/user/singshredcode/m/animal_subbies/\"&gt;Animal Subbies&lt;/a&gt; (If you know of any cute-animal subs I missed, please comment below)&lt;/li&gt;\n&lt;li&gt;&lt;a href=\"https://www.reddit.com/user/mujhair/m/asks/\"&gt;Asks&lt;/a&gt;&lt;/li&gt;\n&lt;li&gt;Some &lt;a href=\"https://www.reddit.com/user/reddit/m/admin_faves/\"&gt;Admins’ favorites&lt;/a&gt; that were mentioned in this &lt;a href=\"https://www.reddit.com/r/AskReddit/comments/afzfa7/admins_of_reddit_whats_your_favorite_subreddit/\"&gt;r/askreddit thread&lt;/a&gt; a few months ago&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;See the sticky comment below to enter your Custom Feed.&lt;/p&gt;\n\n&lt;p&gt;Edit: Added a quirky category&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "preview": {"images": [{"source": {"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?format=png8&amp;s=f4e29fd8376946da83c767696f9668d16cb361bc", "width": 480, "height": 480}, "resolutions": [{"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?width=108&amp;crop=smart&amp;format=png8&amp;s=d2f171a836f7de5d97caedc96f8961c1460b5e81", "width": 108, "height": 108}, {"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?width=216&amp;crop=smart&amp;format=png8&amp;s=a73492b44bdd8ccd7c1fb4aa517af7f000d31362", "width": 216, "height": 216}, {"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?width=320&amp;crop=smart&amp;format=png8&amp;s=ab555deb76f9c9a5cf8c644280a169e414090958", "width": 320, "height": 320}], "variants": {"gif": {"source": {"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?s=40b663332694be2f29dc8378db328d089f57fe84", "width": 480, "height": 480}, "resolutions": [{"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?width=108&amp;crop=smart&amp;s=689a41bbe6acdfccc9a46539672d91dae8da0cc7", "width": 108, "height": 108}, {"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?width=216&amp;crop=smart&amp;s=13ecf3934d90f6d580364842639c000a7eb2caab", "width": 216, "height": 216}, {"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?width=320&amp;crop=smart&amp;s=320578b2da00c55d1e5c5343bdb20e073b5d8976", "width": 320, "height": 320}]}, "mp4": {"source": {"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?format=mp4&amp;s=5194c33f59febb7524241e522dd368633178e4f2", "width": 480, "height": 480}, "resolutions": [{"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?width=108&amp;format=mp4&amp;s=c2a0daa2bdf6badcdc24df84152d6b9282a4efc9", "width": 108, "height": 108}, {"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?width=216&amp;format=mp4&amp;s=0b4aa9884e1b35f497d1dd4f00168f49b1bc0458", "width": 216, "height": 216}, {"url": "https://external-preview.redd.it/c8Tl2CgY9V2I1iaLKEN5Knz1wtIvYAEbdANJID_Szyo.gif?width=320&amp;format=mp4&amp;s=18339489f0e4f53f3f1c2b1027fd53968568442d", "width": 320, "height": 320}]}}, "id": "RTbWM4Otg_xZkCHSyTI51qs2ct-Fz1dHj0-6jKh0vAE"}], "enabled": true}, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "bpfyx1", "is_robot_indexable": true, "report_reasons": null, "author": "SingShredCode", "discussion_type": null, "num_comments": 1002, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/announcements/comments/bpfyx1/introducing_custom_feeds_plus_a_community_contest/", "stickied": false, "url": "https://www.reddit.com/r/announcements/comments/bpfyx1/introducing_custom_feeds_plus_a_community_contest/", "subreddit_subscribers": *********, "created_utc": **********.0, "num_crossposts": 20, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "announcements", "selftext": "", "user_reports": [], "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "How to keep your Reddit account safe", "link_flair_richtext": [], "subreddit_name_prefixed": "r/announcements", "hidden": false, "pwls": 6, "link_flair_css_class": null, "downs": 0, "thumbnail_height": 140, "top_awarded_type": null, "hide_score": false, "name": "t3_blev4z", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.75, "author_flair_background_color": null, "subreddit_type": "archived", "ups": 10234, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": 140, "author_flair_template_id": null, "is_original_content": false, "author_fullname": "t2_qromv", "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 10234, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "https://b.thumbs.redditmedia.com/4EGkxZNzrzt-RCtd035cWyO0BipdbDVuPwi4W2v4TyM.jpg", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "crosspost_parent_list": [{"approved_at_utc": null, "subreddit": "RedditSafety", "selftext": "Your account expresses your voice and your personality here on Reddit. To protect that voice, you need to protect your access to it and maintain its security. Not only do compromised accounts deprive you of your online identity, but they are often used for malicious behavior like vote manipulation, spam, fraud, or even just posting content to misrepresent the true owner. While we’re always developing ways to take faster action against compromised accounts, there are things you can do to be proactive about your account’s security.\n\n**What we do** to keep your account secure:\n\n* **Actively look for suspicious signals** \\- We use tools that help us detect unusual behavior in accounts. We monitor trends and compare against known threats. \n* **Check passwords against 3rd party breach datasets** \\- We check for username / password combinations in 3rd party breach sets. \n* **Display your recent IP sessions for you to access** \\- You can check your [account activity](https://www.reddit.com/account-activity) at any time to see your recent login IPs. Keep in mind that the geolocation of each login may not be exact and will only include events within the last 100 days. If you see something you don’t recognize, you should change your password immediately and ensure your email address is correct.\n\nIf we determine that your account is vulnerable to compromise (or has actually been compromised), we lock the account and force a password reset. If we can’t establish account ownership or the account has been used in a malicious manner that prevents it being returned to the original owner, the account may be permanently suspended and closed.\n\n**What you can do** to prevent this situation:\n\n* **Use permanent emails** \\- We highly encourage users to link their accounts to accessible email addresses that you regularly check (you can add and update email addresses in your [user settings page](https://new.reddit.com/settings) if you are using new reddit, otherwise you can do that from the [preferences page](https://old.reddit.com/prefs/update) in old reddit). This is also how you will receive any activities alerting you of suspicious activity on your account if you’re signed out. As a general rule of thumb, avoid using email addresses you don't have permanent ownership over like school or work addresses. Temporary email addresses that expire are a bad idea.\n* **Verify your emails** \\- Verifying your email helps us confirm that there is a real person creating the account and that you have access to the email address given. If we determine that your account has been compromised, this is the only way we have to validate account ownership. Without this our only option will be to permanently close the account to prevent further misuse and access to the original owner’s data. There will be no appeals possible!\n* **Check your profile occasionally to make sure your email address is current.** You can do this via the [preferences page on old reddit](https://www.reddit.com/prefs/update/) or the [settings page in new reddit](https://new.reddit.com/settings). It’s easy to forget to update it when you change schools, service providers, or set up new accounts. \n* **Use strong/unique passwords** \\- Use passwords that are complex and not used on any other site. We recommend using a password manager to help you generate and securely store passwords. \n* [**Add two factor authentication**](https://www.reddithelp.com/en/categories/using-reddit/your-reddit-account/how-set-two-factor-authentication) \\- For an extra layer of security. If someone gets ahold of your username/password combo, they will not be able to log into your account without entering the verification code.\n\nWe know users want to protect their privacy and don’t always want to provide an email address to companies, so we don’t require it. However, there are certain account protections that require users establish ownership, which is why an email address is required for password reset requests. Forcing password resets on vulnerable accounts is one of many ways we try to secure potentially compromised accounts and prevent manipulation of our platform. Accounts flagged as compromised with a verified email receive a forced password reset notice, but accounts without one will be permanently closed. In the past, manual attempts to establish ownership on accounts with lost access rarely resulted in an account recovery. Because manual attempts are ineffective and time consuming for our operations teams and you, we won’t be doing them moving forward. You're welcome to use Reddit without an email address associated with your account, but do so with the understanding of the account protection limitation. You can visit your user settings page at anytime to add or verify an email address.", "author_fullname": "t2_qromv", "saved": false, "mod_reason_title": null, "gilded": 1, "clicked": false, "title": "How to keep your Reddit account safe", "link_flair_richtext": [], "subreddit_name_prefixed": "r/RedditSafety", "hidden": false, "pwls": null, "link_flair_css_class": null, "downs": 0, "thumbnail_height": null, "top_awarded_type": null, "hide_score": false, "name": "t3_bletrr", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.85, "author_flair_background_color": null, "subreddit_type": "restricted", "ups": 2933, "total_awards_received": 0, "media_embed": {}, "thumbnail_width": null, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": null, "can_mod_post": false, "score": 2933, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "self", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": **********.0, "link_flair_type": "text", "wls": null, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.RedditSafety", "allow_live_comments": true, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Your account expresses your voice and your personality here on Reddit. To protect that voice, you need to protect your access to it and maintain its security. Not only do compromised accounts deprive you of your online identity, but they are often used for malicious behavior like vote manipulation, spam, fraud, or even just posting content to misrepresent the true owner. While we’re always developing ways to take faster action against compromised accounts, there are things you can do to be proactive about your account’s security.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;What we do&lt;/strong&gt; to keep your account secure:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;&lt;strong&gt;Actively look for suspicious signals&lt;/strong&gt; - We use tools that help us detect unusual behavior in accounts. We monitor trends and compare against known threats. &lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Check passwords against 3rd party breach datasets&lt;/strong&gt; - We check for username / password combinations in 3rd party breach sets. &lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Display your recent IP sessions for you to access&lt;/strong&gt; - You can check your &lt;a href=\"https://www.reddit.com/account-activity\"&gt;account activity&lt;/a&gt; at any time to see your recent login IPs. Keep in mind that the geolocation of each login may not be exact and will only include events within the last 100 days. If you see something you don’t recognize, you should change your password immediately and ensure your email address is correct.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;If we determine that your account is vulnerable to compromise (or has actually been compromised), we lock the account and force a password reset. If we can’t establish account ownership or the account has been used in a malicious manner that prevents it being returned to the original owner, the account may be permanently suspended and closed.&lt;/p&gt;\n\n&lt;p&gt;&lt;strong&gt;What you can do&lt;/strong&gt; to prevent this situation:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;&lt;strong&gt;Use permanent emails&lt;/strong&gt; - We highly encourage users to link their accounts to accessible email addresses that you regularly check (you can add and update email addresses in your &lt;a href=\"https://new.reddit.com/settings\"&gt;user settings page&lt;/a&gt; if you are using new reddit, otherwise you can do that from the &lt;a href=\"https://old.reddit.com/prefs/update\"&gt;preferences page&lt;/a&gt; in old reddit). This is also how you will receive any activities alerting you of suspicious activity on your account if you’re signed out. As a general rule of thumb, avoid using email addresses you don&amp;#39;t have permanent ownership over like school or work addresses. Temporary email addresses that expire are a bad idea.&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Verify your emails&lt;/strong&gt; - Verifying your email helps us confirm that there is a real person creating the account and that you have access to the email address given. If we determine that your account has been compromised, this is the only way we have to validate account ownership. Without this our only option will be to permanently close the account to prevent further misuse and access to the original owner’s data. There will be no appeals possible!&lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Check your profile occasionally to make sure your email address is current.&lt;/strong&gt; You can do this via the &lt;a href=\"https://www.reddit.com/prefs/update/\"&gt;preferences page on old reddit&lt;/a&gt; or the &lt;a href=\"https://new.reddit.com/settings\"&gt;settings page in new reddit&lt;/a&gt;. It’s easy to forget to update it when you change schools, service providers, or set up new accounts. &lt;/li&gt;\n&lt;li&gt;&lt;strong&gt;Use strong/unique passwords&lt;/strong&gt; - Use passwords that are complex and not used on any other site. We recommend using a password manager to help you generate and securely store passwords. &lt;/li&gt;\n&lt;li&gt;&lt;a href=\"https://www.reddithelp.com/en/categories/using-reddit/your-reddit-account/how-set-two-factor-authentication\"&gt;&lt;strong&gt;Add two factor authentication&lt;/strong&gt;&lt;/a&gt; - For an extra layer of security. If someone gets ahold of your username/password combo, they will not be able to log into your account without entering the verification code.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;We know users want to protect their privacy and don’t always want to provide an email address to companies, so we don’t require it. However, there are certain account protections that require users establish ownership, which is why an email address is required for password reset requests. Forcing password resets on vulnerable accounts is one of many ways we try to secure potentially compromised accounts and prevent manipulation of our platform. Accounts flagged as compromised with a verified email receive a forced password reset notice, but accounts without one will be permanently closed. In the past, manual attempts to establish ownership on accounts with lost access rarely resulted in an account recovery. Because manual attempts are ineffective and time consuming for our operations teams and you, we won’t be doing them moving forward. You&amp;#39;re welcome to use Reddit without an email address associated with your account, but do so with the understanding of the account protection limitation. You can visit your user settings page at anytime to add or verify an email address.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_vm1db", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "bletrr", "is_robot_indexable": true, "report_reasons": null, "author": "worstnerd", "discussion_type": null, "num_comments": 909, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/RedditSafety/comments/bletrr/how_to_keep_your_reddit_account_safe/", "stickied": false, "url": "https://www.reddit.com/r/RedditSafety/comments/bletrr/how_to_keep_your_reddit_account_safe/", "subreddit_subscribers": 40199, "created_utc": **********.0, "num_crossposts": 17, "media": null, "is_video": false}], "created": **********.0, "link_flair_type": "text", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.redditsecurity", "allow_live_comments": true, "selftext_html": null, "likes": null, "suggested_sort": "qa", "banned_at_utc": null, "url_overridden_by_dest": "https://www.reddit.com/r/redditsecurity/comments/bletrr/how_to_keep_your_reddit_account_safe/", "view_count": null, "archived": true, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "can_gild": false, "spoiler": false, "locked": true, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": "admin", "subreddit_id": "t5_2r0ij", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "blev4z", "is_robot_indexable": true, "report_reasons": null, "author": "worstnerd", "discussion_type": null, "num_comments": 1, "send_replies": false, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "crosspost_parent": "t3_bletrr", "author_flair_text_color": null, "permalink": "/r/announcements/comments/blev4z/how_to_keep_your_reddit_account_safe/", "stickied": false, "url": "https://www.reddit.com/r/redditsecurity/comments/bletrr/how_to_keep_your_reddit_account_safe/", "subreddit_subscribers": *********, "created_utc": **********.0, "num_crossposts": 0, "media": null, "is_video": false}}], "before": null}}