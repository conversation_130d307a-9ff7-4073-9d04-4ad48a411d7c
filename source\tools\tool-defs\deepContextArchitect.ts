import { t } from "structural";
import { ToolDef } from "../common.ts";
import { DeepContextArchitect } from "../../deepContextArchitect/index.ts";

const ArgumentsSchema = t.subtype({
  query: t.str.comment("The user's query or request for code analysis/modification"),
  workingDirectory: t.optional(t.str.comment("The working directory to analyze (defaults to current directory)")),
  targetFiles: t.optional(t.array(t.str).comment("Optional specific files to focus on")),
  analysisDepth: t.optional(t.str.comment("Depth of analysis to perform (default: deep)")),
});

const Schema = t.subtype({
  name: t.value("deepContextArchitect"),
  arguments: ArgumentsSchema,
}).comment("Analyzes code context deeply like a senior developer before making changes");

export default {
  Schema,
  ArgumentsSchema,
  validate: async (toolCall: t.GetType<typeof Schema>) => {
    // Basic validation - ensure working directory exists if provided
    if (toolCall.arguments['workingDirectory']) {
      const fs = await import("fs/promises");
      try {
        await fs.access(toolCall.arguments['workingDirectory']);
      } catch {
        throw new Error(`Working directory does not exist: ${toolCall.arguments['workingDirectory']}`);
      }
    }
    return null;
  },
  async run(abortSignal, call, config, modelOverride) {
    const { query, workingDirectory, targetFiles, analysisDepth } = call.tool.arguments;
    
    // Use current working directory if not specified
    const workDir = workingDirectory || process.cwd();
    
    const architect = new DeepContextArchitect();
    const result = await architect.analyze(
      query,
      workDir,
      targetFiles,
      (analysisDepth as 'shallow' | 'deep' | 'comprehensive') || 'deep'
    );

    // Format the result as a comprehensive report
    return formatAnalysisReport(result);
  },
} satisfies ToolDef<t.GetType<typeof Schema>>;

function formatAnalysisReport(result: any): string {
  return `# Deep Context Analysis Report

## Query Analysis
**Intent:** ${result.analysis.queryIntent}
**Target Components:** ${result.analysis.targetComponents.join(', ') || 'None identified'}
**Risk Level:** ${result.analysis.riskAssessment.level.toUpperCase()}

## Affected Files (${result.analysis.affectedFiles.length})
${result.analysis.affectedFiles.length > 0 
  ? result.analysis.affectedFiles.map((file: string) => `- ${file}`).join('\n')
  : '- No files directly affected'
}

## Dependencies (${result.analysis.dependencies.length})
${result.analysis.dependencies.length > 0
  ? result.analysis.dependencies.map((dep: any) => 
      `- ${dep.file}: ${dep.type} of ${dep.component}`
    ).join('\n')
  : '- No dependencies identified'
}

## Risk Factors
${result.analysis.riskAssessment.factors.map((factor: string) => `- ${factor}`).join('\n')}

## Relevant Code Context (${result.context.relevantCode.length} files)
${result.context.relevantCode.map((code: any) => 
  `### ${code.file} (Relevance: ${(code.relevanceScore * 100).toFixed(1)}%)
${code.summary || 'No summary available'}

\`\`\`
${code.content.length > 1000 ? code.content.substring(0, 1000) + '\n// ... (truncated)' : code.content}
\`\`\``
).join('\n\n')}

## Related Tests (${result.context.relatedTests.length})
${result.context.relatedTests.length > 0
  ? result.context.relatedTests.map((test: string) => `- ${test}`).join('\n')
  : '- No related tests found'
}

## Documentation (${result.context.documentation.length})
${result.context.documentation.length > 0
  ? result.context.documentation.map((doc: string) => `- ${doc}`).join('\n')
  : '- No related documentation found'
}

---

# Implementation Recommendations

## Suggested Approach
${result.recommendations.suggestedApproach}

---

## Implementation Plan
${result.recommendations.implementationPlan}

---

## Alternative Approaches
${result.recommendations.alternativeApproaches.map((alt: string, index: number) => 
  `### Option ${index + 1}\n${alt}`
).join('\n\n')}

---

## Testing Strategy
${result.recommendations.testingStrategy}

---

## Rollback Plan
${result.recommendations.rollbackPlan}

---

## Recent Changes (${result.context.recentChanges.length})
${result.context.recentChanges.length > 0
  ? result.context.recentChanges.map((change: any) => 
      `- ${change.file} (${change.timestamp}): ${change.summary}`
    ).join('\n')
  : '- No recent changes detected'
}

---

*Analysis completed at ${new Date().toISOString()}*
`;
}