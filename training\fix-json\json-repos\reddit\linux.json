{"kind": "Listing", "data": {"after": "t3_1m9v27o", "dist": 27, "modhash": "qajh7zhvwyb89280a4a4ebc671a52dd446431fbe207026535d", "geo_filter": null, "children": [{"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_65v7y5x01", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "The EU is trying to implement a plan to use AI to scan and report all private encrypted communication. This is insane and breaks the fundamental concepts of privacy and end to end encryption. Don’t sleep on this Europeans. Call and harass your reps in Brussels.", "link_flair_richtext": [{"e": "text", "t": "Privacy"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1djfqo7", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.98, "author_flair_background_color": null, "subreddit_type": "public", "ups": 3862, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Privacy", "can_mod_post": false, "score": 3862, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1718792418.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "signal.org", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://signal.org/blog/pdfs/upload-moderation.pdf", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "016e0864-77bb-11ea-8afc-0e0d4f3ef543", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#ddbd37", "id": "1djfqo7", "is_robot_indexable": true, "report_reasons": null, "author": "B3_Kind_R3wind_", "discussion_type": null, "num_comments": 528, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1djfqo7/the_eu_is_trying_to_implement_a_plan_to_use_ai_to/", "stickied": true, "url": "https://signal.org/blog/pdfs/upload-moderation.pdf", "subreddit_subscribers": 1732389, "created_utc": 1718792418.0, "num_crossposts": 9, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_12rv1wib0q", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "EU is proposing a new mass surveillance law and they are asking the public for feedback", "link_flair_richtext": [{"e": "text", "t": "Privacy"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1kvf7vr", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.98, "author_flair_background_color": null, "subreddit_type": "public", "ups": 2057, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Privacy", "can_mod_post": false, "score": 2057, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": **********.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "ec.europa.eu", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://ec.europa.eu/info/law/better-regulation/have-your-say/initiatives/14680-Impact-assessment-on-retention-of-data-by-service-providers-for-criminal-proceedings-_en", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "016e0864-77bb-11ea-8afc-0e0d4f3ef543", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#ddbd37", "id": "1kvf7vr", "is_robot_indexable": true, "report_reasons": null, "author": "Dry_Row_7050", "discussion_type": null, "num_comments": 234, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1kvf7vr/eu_is_proposing_a_new_mass_surveillance_law_and/", "stickied": true, "url": "https://ec.europa.eu/info/law/better-regulation/have-your-say/initiatives/14680-Impact-assessment-on-retention-of-data-by-service-providers-for-criminal-proceedings-_en", "subreddit_subscribers": 1732389, "created_utc": **********.0, "num_crossposts": 3, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "Gotta say Linux is cool af. I had a 64 GB flash drive collecting dust (who uses flash drives these days anyways) and I set up MX Linux Fluxbox on it with automatic startup _persist_all_ boot option. \n\nNow I have a portable and lightweight workstation (kinda) which I can just pretty much plug and play on any hardware, even the potato ones. This thing consumes only 634 mb on idle! \n\n", "author_fullname": "t2_1tdu31wvzp", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "MX Linux Fluxbox with Persistence is amazing", "link_flair_richtext": [{"e": "text", "t": "Discussion"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1mbn1z3", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.79, "author_flair_background_color": null, "subreddit_type": "public", "ups": 33, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": true, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Discussion", "can_mod_post": false, "score": 33, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753724830.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "i.redd.it", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Gotta say Linux is cool af. I had a 64 GB flash drive collecting dust (who uses flash drives these days anyways) and I set up MX Linux Fluxbox on it with automatic startup &lt;em&gt;persist_all&lt;/em&gt; boot option. &lt;/p&gt;\n\n&lt;p&gt;Now I have a portable and lightweight workstation (kinda) which I can just pretty much plug and play on any hardware, even the potato ones. This thing consumes only 634 mb on idle! &lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://i.redd.it/0lrgpypaknff1.png", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "b7bc6ebe-a68e-11eb-9aa7-0e5d2b55839b", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "1mbn1z3", "is_robot_indexable": true, "report_reasons": null, "author": "<PERSON><PERSON>", "discussion_type": null, "num_comments": 21, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1mbn1z3/mx_linux_fluxbox_with_persistence_is_amazing/", "stickied": false, "url": "https://i.redd.it/0lrgpypaknff1.png", "subreddit_subscribers": 1732389, "created_utc": 1753724830.0, "num_crossposts": 1, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "I am a software engineering student currently in uni. Up until pretty recently, I would've never thought to switch to Linux. The reasons were:\n\n\\- Security just isn't a big deal for the average person\n\n\\- Can't play games (or as good as windows)\n\n\\- It seemed pretty nerdy (i know, shouldn't be a negative reason lol)\n\n\\- It looked like id have to learn a new programming language to open the settings app on linux. I also saw a post about a guy who accidently wiped his drive and his home server while trying to get steam to work once, soo that was pretty scary.\n\n\\- Windows better! (?)\n\nBut since then, both the world and I've changed. Both pretty significantly, in my opinion.\n\nOver the last year or so I've begun pursuing AI Engineering as a field in software engineering. However, this also made me realize that AI is the harbringer of the ultimate privacy nightmare. While the average person should have had little concern about getting tracked by agencies (because it was costly for those agencies to track people, thus they didn't pursue average people as heavily), AI automations are now beginning to make it a reality. Those of you familiar with defense or cybersecurity news must already be aware that people may begin (or may already have begun) getting profiled en masse by certain companies utilizing AI. We are yet to see the effects of this, but as someone who somewhat understands the field I believe that the threats are very real. I've thus begun to seek ways to make my data harder to access, shifting many of my utilities to proton, switching to linux and considering a home server system etc. for this reason\n\nI also stopped playing games, and as a software engineering student I no longer get as scared by the terminal, though I am still pretty cautious and have begun learning the basics.\n\nWindows also stopped being \"better\" in my experience. Win 11 more OneDrive enforcement, more weird features that they force you to use and most importantly more lag. My pc with 8gb of ram and a ryzen 5500u should not lag while using a browser, its not acceptable.\n\nSo the privacy concerns, windows itself and my curiosity towards coding pushed me into Linux, though I could have sworn 9 months ago that I would never use it.\n\nWhat do you guys think? Im curious to know your perspective on the privacy argument i have, aswell as curious to hear what was your reason for switching\n\nOh, and linux is pretty nerdy lol", "author_fullname": "t2_1tp0coaxmj", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Why I switched to Linux as someone who once never would have", "link_flair_richtext": [{"e": "text", "t": "Discussion"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": true, "name": "t3_1mbq3m9", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.73, "author_flair_background_color": "transparent", "subreddit_type": "public", "ups": 23, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": "e2a38f7c-cdf6-11e8-baca-0e7c33189530", "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Discussion", "can_mod_post": false, "score": 23, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [{"a": ":linuxmint:", "e": "emoji", "u": "https://emoji.redditmedia.com/iriyn29pqpr11_t5_2qh1a/linuxmint"}], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753731557.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "richtext", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;I am a software engineering student currently in uni. Up until pretty recently, I would&amp;#39;ve never thought to switch to Linux. The reasons were:&lt;/p&gt;\n\n&lt;p&gt;- Security just isn&amp;#39;t a big deal for the average person&lt;/p&gt;\n\n&lt;p&gt;- Can&amp;#39;t play games (or as good as windows)&lt;/p&gt;\n\n&lt;p&gt;- It seemed pretty nerdy (i know, shouldn&amp;#39;t be a negative reason lol)&lt;/p&gt;\n\n&lt;p&gt;- It looked like id have to learn a new programming language to open the settings app on linux. I also saw a post about a guy who accidently wiped his drive and his home server while trying to get steam to work once, soo that was pretty scary.&lt;/p&gt;\n\n&lt;p&gt;- Windows better! (?)&lt;/p&gt;\n\n&lt;p&gt;But since then, both the world and I&amp;#39;ve changed. Both pretty significantly, in my opinion.&lt;/p&gt;\n\n&lt;p&gt;Over the last year or so I&amp;#39;ve begun pursuing AI Engineering as a field in software engineering. However, this also made me realize that AI is the harbringer of the ultimate privacy nightmare. While the average person should have had little concern about getting tracked by agencies (because it was costly for those agencies to track people, thus they didn&amp;#39;t pursue average people as heavily), AI automations are now beginning to make it a reality. Those of you familiar with defense or cybersecurity news must already be aware that people may begin (or may already have begun) getting profiled en masse by certain companies utilizing AI. We are yet to see the effects of this, but as someone who somewhat understands the field I believe that the threats are very real. I&amp;#39;ve thus begun to seek ways to make my data harder to access, shifting many of my utilities to proton, switching to linux and considering a home server system etc. for this reason&lt;/p&gt;\n\n&lt;p&gt;I also stopped playing games, and as a software engineering student I no longer get as scared by the terminal, though I am still pretty cautious and have begun learning the basics.&lt;/p&gt;\n\n&lt;p&gt;Windows also stopped being &amp;quot;better&amp;quot; in my experience. Win 11 more OneDrive enforcement, more weird features that they force you to use and most importantly more lag. My pc with 8gb of ram and a ryzen 5500u should not lag while using a browser, its not acceptable.&lt;/p&gt;\n\n&lt;p&gt;So the privacy concerns, windows itself and my curiosity towards coding pushed me into Linux, though I could have sworn 9 months ago that I would never use it.&lt;/p&gt;\n\n&lt;p&gt;What do you guys think? Im curious to know your perspective on the privacy argument i have, aswell as curious to hear what was your reason for switching&lt;/p&gt;\n\n&lt;p&gt;Oh, and linux is pretty nerdy lol&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "b7bc6ebe-a68e-11eb-9aa7-0e5d2b55839b", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": ":linuxmint:", "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "1mbq3m9", "is_robot_indexable": true, "report_reasons": null, "author": "<PERSON><PERSON><PERSON><PERSON>", "discussion_type": null, "num_comments": 19, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": "dark", "permalink": "/r/linux/comments/1mbq3m9/why_i_switched_to_linux_as_someone_who_once_never/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1mbq3m9/why_i_switched_to_linux_as_someone_who_once_never/", "subreddit_subscribers": 1732389, "created_utc": 1753731557.0, "num_crossposts": 1, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "**From the blog post:**\n\nHello everyone!\n\nAs it’s been not much of a secret anymore, Hyprperks has been a few months in the making, and now it’s all come together.\n\nHyprperks is now open to purchase to everyone!\n\nIf you want to support the development for 5€ + tax a month, and also get a few goodies from us, please check out [the pricing page](https://account.hypr.land/pricing)\n\nYou will get:\n\n* Member-only forum access, with dev Q&amp;A, support from me, and more\n* Premium desktop experience, which is a set of preconfigured dotfiles with a one-click install and update\n* And of course, support the continued development.\n\nIf you don’t have a Hyprland Account yet, consider making one! It’s free, and gives you access to our public forums, where you can find answers, ask questions, and interact with the community.\n\nAlso, thank you for all the support, guys. You are awesome!\n\nCheers, vax.", "author_fullname": "t2_23kkyv3d", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Hyprperks: a new official subscription to support Hyprland development.", "link_flair_richtext": [{"e": "text", "t": "Popular Application"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": true, "name": "t3_1mbqb5d", "quarantine": false, "link_flair_text_color": "light", "upvote_ratio": 0.62, "author_flair_background_color": "transparent", "subreddit_type": "public", "ups": 15, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": "2194c338-ce1d-11e8-8ed7-0e20bb1bbc52", "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Popular Application", "can_mod_post": false, "score": 15, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [{"a": ":nix:", "e": "emoji", "u": "https://emoji.redditmedia.com/ww1ubcjpqpr11_t5_2qh1a/nix"}], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753732043.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "richtext", "domain": "hypr.land", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;&lt;strong&gt;From the blog post:&lt;/strong&gt;&lt;/p&gt;\n\n&lt;p&gt;Hello everyone!&lt;/p&gt;\n\n&lt;p&gt;As it’s been not much of a secret anymore, Hyprperks has been a few months in the making, and now it’s all come together.&lt;/p&gt;\n\n&lt;p&gt;Hyprperks is now open to purchase to everyone!&lt;/p&gt;\n\n&lt;p&gt;If you want to support the development for 5€ + tax a month, and also get a few goodies from us, please check out &lt;a href=\"https://account.hypr.land/pricing\"&gt;the pricing page&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;You will get:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Member-only forum access, with dev Q&amp;amp;A, support from me, and more&lt;/li&gt;\n&lt;li&gt;Premium desktop experience, which is a set of preconfigured dotfiles with a one-click install and update&lt;/li&gt;\n&lt;li&gt;And of course, support the continued development.&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;If you don’t have a Hyprland Account yet, consider making one! It’s free, and gives you access to our public forums, where you can find answers, ask questions, and interact with the community.&lt;/p&gt;\n\n&lt;p&gt;Also, thank you for all the support, guys. You are awesome!&lt;/p&gt;\n\n&lt;p&gt;Cheers, vax.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://hypr.land/news/hyprperks/", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "7127ec98-5859-11e8-9488-0e8717893ec8", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": ":nix:", "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#0aa18f", "id": "1mbqb5d", "is_robot_indexable": true, "report_reasons": null, "author": "TheTwelveYearOld", "discussion_type": null, "num_comments": 15, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": "dark", "permalink": "/r/linux/comments/1mbqb5d/hyprperks_a_new_official_subscription_to_support/", "stickied": false, "url": "https://hypr.land/news/hyprperks/", "subreddit_subscribers": 1732389, "created_utc": 1753732043.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_ujg2t", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Linux 6.16 Released", "link_flair_richtext": [{"e": "text", "t": "<PERSON><PERSON>"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1mayynm", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.99, "author_flair_background_color": null, "subreddit_type": "public", "ups": 642, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "<PERSON><PERSON>", "can_mod_post": false, "score": 642, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753653862.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "lore.kernel.org", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://lore.kernel.org/lkml/CAHk-=<EMAIL>/T/#u", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "5a277eb4-5859-11e8-b1d4-0eb3c1459ab0", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#ffd133", "id": "1mayynm", "is_robot_indexable": true, "report_reasons": null, "author": "t<PERSON><PERSON>", "discussion_type": null, "num_comments": 55, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1mayynm/linux_616_released/", "stickied": false, "url": "https://lore.kernel.org/lkml/CAHk-=<EMAIL>/T/#u", "subreddit_subscribers": 1732389, "created_utc": 1753653862.0, "num_crossposts": 1, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_3kanv", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Well, <PERSON><PERSON> released Linux Kernel 6.16 ...get it and have fun!", "link_flair_richtext": [{"e": "text", "t": "<PERSON><PERSON>"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1mb0a7j", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.93, "author_flair_background_color": null, "subreddit_type": "public", "ups": 313, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "<PERSON><PERSON>", "can_mod_post": false, "score": 313, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753657371.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "git.kernel.org", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://git.kernel.org/pub/scm/linux/kernel/git/torvalds/linux.git/", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "5a277eb4-5859-11e8-b1d4-0eb3c1459ab0", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#ffd133", "id": "1mb0a7j", "is_robot_indexable": true, "report_reasons": null, "author": "unixbhaskar", "discussion_type": null, "num_comments": 33, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1mb0a7j/well_linus_released_linux_kernel_616_get_it_and/", "stickied": false, "url": "https://git.kernel.org/pub/scm/linux/kernel/git/torvalds/linux.git/", "subreddit_subscribers": 1732389, "created_utc": 1753657371.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "HeliumOS 10 has been released as stable! Learn what's new and how HeliumOS 10 may improve in the future!", "author_fullname": "t2_mtqya0pw", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Announcing the release of HeliumOS 10", "link_flair_richtext": [{"e": "text", "t": "Distro News"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": true, "name": "t3_1mbq3fc", "quarantine": false, "link_flair_text_color": "light", "upvote_ratio": 0.81, "author_flair_background_color": "transparent", "subreddit_type": "public", "ups": 6, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": "f90cf328-ac58-11eb-951b-0e5adf64dd6d", "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Distro News", "can_mod_post": false, "score": 6, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [{"a": ":almalinux:", "e": "emoji", "u": "https://emoji.redditmedia.com/qcnbklfaxyw61_t5_2qh1a/almalinux"}], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753731546.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "richtext", "domain": "heliumos.org", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;HeliumOS 10 has been released as stable! Learn what&amp;#39;s new and how HeliumOS 10 may improve in the future!&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://www.heliumos.org/blog/post/heliumos-10-released/", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "6888e772-5859-11e8-82ff-0e816ab71260", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": ":almalinux:", "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#0dd3bb", "id": "1mbq3fc", "is_robot_indexable": true, "report_reasons": null, "author": "imbev", "discussion_type": null, "num_comments": 1, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": "dark", "permalink": "/r/linux/comments/1mbq3fc/announcing_the_release_of_heliumos_10/", "stickied": false, "url": "https://www.heliumos.org/blog/post/heliumos-10-released/", "subreddit_subscribers": 1732389, "created_utc": 1753731546.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "Hey folks,\n\nI just released a small tool called venv-stack that helps manage Python virtual environments in a more modular and disk-efficient way (without duplicating libraries), especially in the context of PEP 668 on Linux, where messing with system or user-wide packages is discouraged.\n\n[https://github.com/ignis-sec/venv-stack](https://github.com/ignis-sec/venv-stack)\n\n[https://pypi.org/project/venv-stack/](https://pypi.org/project/venv-stack/)\n\n# Problem\n\n* PEP 668 makes it hard to install packages globally or system-wide-- you’re encouraged to use virtualenvs for everything.\n* But heavy packages (like torch, opencv, etc.) get installed into every single project, wasting time and tons of disk space. I realize that pip caches the downloaded wheels which helps a little, but it is still annoying to have gb's of virtual environments for every project that uses these large dependencies.\n* So, your options often boil down to:\n   * Ignoring PEP 668 all-together and using --break-system-packages for everything\n   * Have a node\\_modules-esque problem with python.\n\nHere is how layered virtual environments work instead:\n\n1. You create a set of base virtual environments which get placed in \\~/.venv-stack/\n2. For example, you can have a virtual environment with your ML dependencies (torch, opencv, etc) and a virtual environment with all the rest of your non-system packages. You can create these base layers like this: `venv-stack base ml`, or `venv-stack base some-other-environment`\n3. You can activate your base virtual environments with a name: `venv-stack activate base` and install the required dependencies. To deactivate, `exit` does the trick.\n4. When creating a virtual-environment for a project, you can provide a list of these base environments to be linked to the project environment. Such as `venv-stack project . ml,some-other-environment`\n5. You can activate it old-school like `source ./bin/scripts/activate` or just use `venv-stack activate`. If no project name is given for the activate command, it activates the project in the current directory instead.\n\nThe idea behind it is that we can create project level virtual environments with symlinks enabled: `venv.create(venv_path, with_pip=True, symlinks=True)` And we can monkey-patch the pth files on the project virtual environments to list site-packages from all the base environments we are initiating from.\n\nThis helps you stay PEP 668-compliant **without duplicating large libraries**, and gives you a clean way to manage stackable dependency layers.\n\nCurrently it only works on Linux. The activate command is a bit wonky and depends on the shell you are using. I only implemented and tested it with bash and zsh. If you are using a differnt terminal, it is fairly easy add the definitions and contributions are welcome!\n\n# Target Audience\n\n`venv-stack` is aimed at:\n\n* Python developers who work on multiple projects that share large dependencies (e.g., PyTorch, OpenCV, Selenium, etc.)\n* Users on Debian-based distros where PEP 668 makes it painful to install packages outside of a virtual environment\n* Developers who want a modular and space-efficient way to manage environments\n* Anyone tired of re-installing the same 1GB of packages across multiple .venv/ folders\n\nIt’s production-usable, but it’s still a small tool. It’s great for:\n\n* Individual developers\n* Researchers and ML practitioners\n* Power users maintaining many scripts and CLI tools\n\n# Comparison\n\n|Tool|Focus|How `venv-stack` is different|\n|:-|:-|:-|\n|`virtualenv`|Create isolated environments|`venv-stack` creates layered environments by **linking multiple base envs** into a project venv|\n|`venv` (stdlib)|Default for environment creation|`venv-stack` builds on top of `venv`, adding **composition**, reuse, and convenience|\n|`pyenv`|Manage Python versions|`venv-stack` doesn’t manage versions, it builds **modular dependencies** on top of your chosen Python install|\n|`conda`|Full package/environment manager|`venv-stack` is lighter, uses native tools, and focuses on **Python-only dependency layering**|\n|`tox`, `poetry`|Project-based workflows, packaging|`venv-stack` is **agnostic** to your workflow, it focuses only on the environment reuse problem|", "author_fullname": "t2_d6pundw", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "I've created a lightweight tool called \"venv-stack\" to make it easier to deal with PEP 668 on Linux", "link_flair_richtext": [{"e": "text", "t": "Software Release"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1mbj7r1", "quarantine": false, "link_flair_text_color": "light", "upvote_ratio": 0.73, "author_flair_background_color": null, "subreddit_type": "public", "ups": 12, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Software Release", "can_mod_post": false, "score": 12, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753716382.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Hey folks,&lt;/p&gt;\n\n&lt;p&gt;I just released a small tool called venv-stack that helps manage Python virtual environments in a more modular and disk-efficient way (without duplicating libraries), especially in the context of PEP 668 on Linux, where messing with system or user-wide packages is discouraged.&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://github.com/ignis-sec/venv-stack\"&gt;https://github.com/ignis-sec/venv-stack&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://pypi.org/project/venv-stack/\"&gt;https://pypi.org/project/venv-stack/&lt;/a&gt;&lt;/p&gt;\n\n&lt;h1&gt;Problem&lt;/h1&gt;\n\n&lt;ul&gt;\n&lt;li&gt;PEP 668 makes it hard to install packages globally or system-wide-- you’re encouraged to use virtualenvs for everything.&lt;/li&gt;\n&lt;li&gt;But heavy packages (like torch, opencv, etc.) get installed into every single project, wasting time and tons of disk space. I realize that pip caches the downloaded wheels which helps a little, but it is still annoying to have gb&amp;#39;s of virtual environments for every project that uses these large dependencies.&lt;/li&gt;\n&lt;li&gt;So, your options often boil down to:\n\n&lt;ul&gt;\n&lt;li&gt;Ignoring PEP 668 all-together and using --break-system-packages for everything&lt;/li&gt;\n&lt;li&gt;Have a node_modules-esque problem with python.&lt;/li&gt;\n&lt;/ul&gt;&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;Here is how layered virtual environments work instead:&lt;/p&gt;\n\n&lt;ol&gt;\n&lt;li&gt;You create a set of base virtual environments which get placed in ~/.venv-stack/&lt;/li&gt;\n&lt;li&gt;For example, you can have a virtual environment with your ML dependencies (torch, opencv, etc) and a virtual environment with all the rest of your non-system packages. You can create these base layers like this: &lt;code&gt;venv-stack base ml&lt;/code&gt;, or &lt;code&gt;venv-stack base some-other-environment&lt;/code&gt;&lt;/li&gt;\n&lt;li&gt;You can activate your base virtual environments with a name: &lt;code&gt;venv-stack activate base&lt;/code&gt; and install the required dependencies. To deactivate, &lt;code&gt;exit&lt;/code&gt; does the trick.&lt;/li&gt;\n&lt;li&gt;When creating a virtual-environment for a project, you can provide a list of these base environments to be linked to the project environment. Such as &lt;code&gt;venv-stack project . ml,some-other-environment&lt;/code&gt;&lt;/li&gt;\n&lt;li&gt;You can activate it old-school like &lt;code&gt;source ./bin/scripts/activate&lt;/code&gt; or just use &lt;code&gt;venv-stack activate&lt;/code&gt;. If no project name is given for the activate command, it activates the project in the current directory instead.&lt;/li&gt;\n&lt;/ol&gt;\n\n&lt;p&gt;The idea behind it is that we can create project level virtual environments with symlinks enabled: &lt;code&gt;venv.create(venv_path, with_pip=True, symlinks=True)&lt;/code&gt; And we can monkey-patch the pth files on the project virtual environments to list site-packages from all the base environments we are initiating from.&lt;/p&gt;\n\n&lt;p&gt;This helps you stay PEP 668-compliant &lt;strong&gt;without duplicating large libraries&lt;/strong&gt;, and gives you a clean way to manage stackable dependency layers.&lt;/p&gt;\n\n&lt;p&gt;Currently it only works on Linux. The activate command is a bit wonky and depends on the shell you are using. I only implemented and tested it with bash and zsh. If you are using a differnt terminal, it is fairly easy add the definitions and contributions are welcome!&lt;/p&gt;\n\n&lt;h1&gt;Target Audience&lt;/h1&gt;\n\n&lt;p&gt;&lt;code&gt;venv-stack&lt;/code&gt; is aimed at:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Python developers who work on multiple projects that share large dependencies (e.g., PyTorch, OpenCV, Selenium, etc.)&lt;/li&gt;\n&lt;li&gt;Users on Debian-based distros where PEP 668 makes it painful to install packages outside of a virtual environment&lt;/li&gt;\n&lt;li&gt;Developers who want a modular and space-efficient way to manage environments&lt;/li&gt;\n&lt;li&gt;Anyone tired of re-installing the same 1GB of packages across multiple .venv/ folders&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;p&gt;It’s production-usable, but it’s still a small tool. It’s great for:&lt;/p&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Individual developers&lt;/li&gt;\n&lt;li&gt;Researchers and ML practitioners&lt;/li&gt;\n&lt;li&gt;Power users maintaining many scripts and CLI tools&lt;/li&gt;\n&lt;/ul&gt;\n\n&lt;h1&gt;Comparison&lt;/h1&gt;\n\n&lt;table&gt;&lt;thead&gt;\n&lt;tr&gt;\n&lt;th align=\"left\"&gt;Tool&lt;/th&gt;\n&lt;th align=\"left\"&gt;Focus&lt;/th&gt;\n&lt;th align=\"left\"&gt;How &lt;code&gt;venv-stack&lt;/code&gt; is different&lt;/th&gt;\n&lt;/tr&gt;\n&lt;/thead&gt;&lt;tbody&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;virtualenv&lt;/code&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;Create isolated environments&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;venv-stack&lt;/code&gt; creates layered environments by &lt;strong&gt;linking multiple base envs&lt;/strong&gt; into a project venv&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;venv&lt;/code&gt; (stdlib)&lt;/td&gt;\n&lt;td align=\"left\"&gt;Default for environment creation&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;venv-stack&lt;/code&gt; builds on top of &lt;code&gt;venv&lt;/code&gt;, adding &lt;strong&gt;composition&lt;/strong&gt;, reuse, and convenience&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;pyenv&lt;/code&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;Manage Python versions&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;venv-stack&lt;/code&gt; doesn’t manage versions, it builds &lt;strong&gt;modular dependencies&lt;/strong&gt; on top of your chosen Python install&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;conda&lt;/code&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;Full package/environment manager&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;venv-stack&lt;/code&gt; is lighter, uses native tools, and focuses on &lt;strong&gt;Python-only dependency layering&lt;/strong&gt;&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;tox&lt;/code&gt;, &lt;code&gt;poetry&lt;/code&gt;&lt;/td&gt;\n&lt;td align=\"left\"&gt;Project-based workflows, packaging&lt;/td&gt;\n&lt;td align=\"left\"&gt;&lt;code&gt;venv-stack&lt;/code&gt; is &lt;strong&gt;agnostic&lt;/strong&gt; to your workflow, it focuses only on the environment reuse problem&lt;/td&gt;\n&lt;/tr&gt;\n&lt;/tbody&gt;&lt;/table&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "904ea3e4-6748-11e7-b925-0ef3dfbb807a", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#349e48", "id": "1mbj7r1", "is_robot_indexable": true, "report_reasons": null, "author": "FlameOfIgnis", "discussion_type": null, "num_comments": 20, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1mbj7r1/ive_created_a_lightweight_tool_called_venvstack/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1mbj7r1/ive_created_a_lightweight_tool_called_venvstack/", "subreddit_subscribers": 1732389, "created_utc": 1753716382.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_mtqya0pw", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Linux 6.16 is available today in Fedora Rawhide", "link_flair_richtext": [{"e": "text", "t": "<PERSON><PERSON>"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1mb0rve", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.81, "author_flair_background_color": "transparent", "subreddit_type": "public", "ups": 36, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": "f90cf328-ac58-11eb-951b-0e5adf64dd6d", "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "<PERSON><PERSON>", "can_mod_post": false, "score": 36, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [{"a": ":almalinux:", "e": "emoji", "u": "https://emoji.redditmedia.com/qcnbklfaxyw61_t5_2qh1a/almalinux"}], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753658702.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "richtext", "domain": "dl.fedoraproject.org", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://dl.fedoraproject.org/pub/fedora/linux/development/rawhide/Everything/x86_64/os/Packages/k/", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "5a277eb4-5859-11e8-b1d4-0eb3c1459ab0", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": ":almalinux:", "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#ffd133", "id": "1mb0rve", "is_robot_indexable": true, "report_reasons": null, "author": "imbev", "discussion_type": null, "num_comments": 4, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": "dark", "permalink": "/r/linux/comments/1mb0rve/linux_616_is_available_today_in_fedora_rawhide/", "stickied": false, "url": "https://dl.fedoraproject.org/pub/fedora/linux/development/rawhide/Everything/x86_64/os/Packages/k/", "subreddit_subscribers": 1732389, "created_utc": 1753658702.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "All in all, it is a linux cli tool using C to low level program with DBus MPRIS to repeat/loop over media/songs (full/random parts of it) (on any sort of player),to your hearts desired number. One can find the detailed description of the project in GitHub readme.\n\nWould love to hear suggestions for betterment. Right now it is as per my requirements only :)\n\nYou can find it here: https://github.com/<PERSON><PERSON><PERSON>-<PERSON>/loopctl\n\nP.S. please star the repo, if you find it useful/to your taste :)\n\n\n\n", "author_fullname": "t2_1gmt63jkne", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "loopctl - Linux CLI tool to repeat audio/video (full/custom segments) user defined \"N\" times", "link_flair_richtext": [{"e": "text", "t": "Discussion"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1mbeps3", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.67, "author_flair_background_color": null, "subreddit_type": "public", "ups": 2, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Discussion", "can_mod_post": false, "score": 2, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753705369.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "github.com", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;All in all, it is a linux cli tool using C to low level program with DBus MPRIS to repeat/loop over media/songs (full/random parts of it) (on any sort of player),to your hearts desired number. One can find the detailed description of the project in GitHub readme.&lt;/p&gt;\n\n&lt;p&gt;Would love to hear suggestions for betterment. Right now it is as per my requirements only :)&lt;/p&gt;\n\n&lt;p&gt;You can find it here: &lt;a href=\"https://github.com/<PERSON>r<PERSON>-<PERSON>/loopctl\"&gt;https://github.com/Kar<PERSON>-<PERSON>/loopctl&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;P.S. please star the repo, if you find it useful/to your taste :)&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://github.com/<PERSON><PERSON><PERSON>-<PERSON>/loopctl", "view_count": null, "archived": false, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "b7bc6ebe-a68e-11eb-9aa7-0e5d2b55839b", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "1mbeps3", "is_robot_indexable": true, "report_reasons": null, "author": "BeyondMoney3072", "discussion_type": null, "num_comments": 1, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1mbeps3/loopctl_linux_cli_tool_to_repeat_audiovideo/", "stickied": false, "url": "https://github.com/<PERSON><PERSON><PERSON>-<PERSON>/loopctl", "subreddit_subscribers": 1732389, "created_utc": 1753705369.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_iji1c", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Open-Source AI in New US Policy: What This Means for Linux", "link_flair_richtext": [{"e": "text", "t": "Open Source Organization"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1mal6bx", "quarantine": false, "link_flair_text_color": "light", "upvote_ratio": 0.74, "author_flair_background_color": null, "subreddit_type": "public", "ups": 91, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Open Source Organization", "can_mod_post": false, "score": 91, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753619687.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "linuxblog.io", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://linuxblog.io/open-source-ai-linux/", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "8a1dd4b0-5859-11e8-a2c7-0e5ebdbe24d6", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#800000", "id": "1mal6bx", "is_robot_indexable": true, "report_reasons": null, "author": "modelop", "discussion_type": null, "num_comments": 59, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1mal6bx/opensource_ai_in_new_us_policy_what_this_means/", "stickied": false, "url": "https://linuxblog.io/open-source-ai-linux/", "subreddit_subscribers": 1732389, "created_utc": 1753619687.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "Hi everyone, I've been using linux for several years in different ways and instances. Everything I learned was on the go or on the job but I'm wondering what would be a good book to use as a formal learning resource. Which one would you recommend?\n\nEDIT: recommended books in the comments\n\n\\- Linux From Scratch  \n\\- The Unix and Internet Fundamentals Howto  \n\\- The Linux Programming Interface + The Kernel Org Docs  \n\\- Unix and Linux System Administration Handbook  \n\\- Linux Pocket Guide - O’Reilly  \n\\- How Linux works - No Starch Press  \n\\- How Linux Works by <PERSON>", "author_fullname": "t2_e0oa1", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Which book to use to learn linux formally?", "link_flair_richtext": [{"e": "text", "t": "Tips and Tricks"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1maw506", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.7, "author_flair_background_color": null, "subreddit_type": "public", "ups": 22, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Tips and Tricks", "can_mod_post": false, "score": 22, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": 1753733686.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753646820.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Hi everyone, I&amp;#39;ve been using linux for several years in different ways and instances. Everything I learned was on the go or on the job but I&amp;#39;m wondering what would be a good book to use as a formal learning resource. Which one would you recommend?&lt;/p&gt;\n\n&lt;p&gt;EDIT: recommended books in the comments&lt;/p&gt;\n\n&lt;p&gt;- Linux From Scratch&lt;br/&gt;\n- The Unix and Internet Fundamentals Howto&lt;br/&gt;\n- The Linux Programming Interface + The Kernel Org Docs&lt;br/&gt;\n- Unix and Linux System Administration Handbook&lt;br/&gt;\n- Linux Pocket Guide - O’Reilly&lt;br/&gt;\n- How Linux works - No Starch Press&lt;br/&gt;\n- How Linux Works by <PERSON>&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "de62f716-76df-11ea-802c-0e7469f68f6b", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#00a6a5", "id": "1maw506", "is_robot_indexable": true, "report_reasons": null, "author": "synapse88", "discussion_type": null, "num_comments": 49, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1maw506/which_book_to_use_to_learn_linux_formally/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1maw506/which_book_to_use_to_learn_linux_formally/", "subreddit_subscribers": 1732389, "created_utc": 1753646820.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "Created this by reverse engineering the GDM Settings programs method of swapping the greeter background. No real reason for this program to exist, just liked having something new to see every time I boot up. It does work with multiple monitors, just make sure the images you use stretch the length of all of your monitors or it will look weird.\n\nOnly tested thoroughly on Fedora 42 Workstation. I did try briefly with an Ubuntu VM, and I do believe it is possible with minor modifications, I'm not actively working toward getting it to work, so use at your own risk.\n\n[https://github.com/CyberSurge-Dev/fedora\\_greeter\\_wallpaper.git](https://github.com/CyberSurge-Dev/fedora_greeter_wallpaper.git)\n\n", "author_fullname": "t2_4xwrlgxo", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "[Fedora 42 GNOME] Created a simple program/service that automatically swaps the GDM greeter (the login screen) background on each boot up.", "link_flair_richtext": [{"e": "text", "t": "Software Release"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1maubgu", "quarantine": false, "link_flair_text_color": "light", "upvote_ratio": 0.89, "author_flair_background_color": null, "subreddit_type": "public", "ups": 14, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Software Release", "can_mod_post": false, "score": 14, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753642398.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Created this by reverse engineering the GDM Settings programs method of swapping the greeter background. No real reason for this program to exist, just liked having something new to see every time I boot up. It does work with multiple monitors, just make sure the images you use stretch the length of all of your monitors or it will look weird.&lt;/p&gt;\n\n&lt;p&gt;Only tested thoroughly on Fedora 42 Workstation. I did try briefly with an Ubuntu VM, and I do believe it is possible with minor modifications, I&amp;#39;m not actively working toward getting it to work, so use at your own risk.&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://github.com/CyberSurge-Dev/fedora_greeter_wallpaper.git\"&gt;https://github.com/CyberSurge-Dev/fedora_greeter_wallpaper.git&lt;/a&gt;&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "904ea3e4-6748-11e7-b925-0ef3dfbb807a", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#349e48", "id": "1maubgu", "is_robot_indexable": true, "report_reasons": null, "author": "__creativeusername", "discussion_type": null, "num_comments": 2, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1maubgu/fedora_42_gnome_created_a_simple_programservice/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1maubgu/fedora_42_gnome_created_a_simple_programservice/", "subreddit_subscribers": 1732389, "created_utc": 1753642398.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "The Linux kernel provides the ability for cryptographically signing kernel modules during their installation. Thus, when they are being loaded the signature is validated. By doing so we increase the kernel security due to the fact that unsigned kernel modules\\\\signed modules with an invalid key(s) are blocked from loading. We can leverage different hashing algorithms as part of the signing process like: SHA-1,SH-224, SHA-256, SHA-384 and SHA-512. Also, the public key for singing is handled using X.509 ITU-T standard certificates ([https://www.kernel.org/doc/html/v4.19/admin-guide/module-signing.html](https://www.kernel.org/doc/html/v4.19/admin-guide/module-signing.html)). Based on the kernel configuration modules can be signed using a RSA key which is controlled by “CONFIG\\_MODULE\\_SIG\\_KEY\\_TYPE\\_RSA” ([https://elixir.bootlin.com/linux/v6.15.6/source/certs/Kconfig#L25](https://elixir.bootlin.com/linux/v6.15.6/source/certs/Kconfig#L25)) or using an elliptic curve key controlled by “CONFIG\\_MODULE\\_SIG\\_KEY\\_TYPE\\_ECDSA” ([https://elixir.bootlin.com/linux/v6.15.6/source/certs/Kconfig#L30](https://elixir.bootlin.com/linux/v6.15.6/source/certs/Kconfig#L30)). By the way, in case a kernel module is signed we can check out different attributes such as: the signature, hashing algorithm used, the signing key, the name of the signer and more using the “modinfo” ([https://linux.die.net/man/8/modinfo](https://linux.die.net/man/8/modinfo)) utility — as shown in the screenshot below.\n\nOverall, probably the main structure related to module singing is “struct module\\_signature” ([https://elixir.bootlin.com/linux/v6.15.6/source/include/linux/module\\_signature.h#L33](https://elixir.bootlin.com/linux/v6.15.6/source/include/linux/module_signature.h#L33)). It is also known as the “module signature information block” that contains: signer’s name, key identifier, signature data and information block ([https://elixir.bootlin.com/linux/v6.15.6/source/include/linux/module\\_signature.h#L24](https://elixir.bootlin.com/linux/v6.15.6/source/include/linux/module_signature.h#L24)). It is leveraged in the kernel in different places such as (but not limited to): a code for signing a module file using a given key ([https://elixir.bootlin.com/linux/v6.15.6/source/scripts/sign-file.c#L222](https://elixir.bootlin.com/linux/v6.15.6/source/scripts/sign-file.c#L222)), as part of IMA ([https://elixir.bootlin.com/linux/v6.15.6/source/security/integrity/ima/ima\\_modsig.c#L44](https://elixir.bootlin.com/linux/v6.15.6/source/security/integrity/ima/ima_modsig.c#L44)), verifying the kernel signature during “kexec\\_file\\_load” ([https://elixir.bootlin.com/linux/v6.15.6/source/arch/s390/kernel/machine\\_kexec\\_file.c#L28](https://elixir.bootlin.com/linux/v6.15.6/source/arch/s390/kernel/machine_kexec_file.c#L28)) and as part of “mod\\_verify\\_sig” ([https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L45](https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L45)) which is used for verifying the signature of a module.\n\nLastly, the general flow is that the “init\\_module\\_from\\_file” function calls “load\\_module” ([https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/main.c#L3601](https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/main.c#L3601)). Than the “load\\_module” (used for allocating and loading the module) function calls the “module\\_sig\\_check” which does the signature check ([https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/main.c#L3275](https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/main.c#L3275)). “module\\_sig\\_check” calls “mod\\_verify\\_sig” ([https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L87](https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L87)). Based on the return value from “mod\\_verify\\_sig” the “module\\_sig\\_check” function created the appropriate error message ([https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L99](https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L99)) and emits the appropriate log entry ([https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L120](https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L120)).\n\nhttps://preview.redd.it/6b1s7ulxonff1.png?width=829&amp;format=png&amp;auto=webp&amp;s=5d510f9e8c94dbfc7fcc2ad7f6a56b7ba84a177d\n\n", "author_fullname": "t2_sx5cie38", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Kernel Module Signing", "link_flair_richtext": [{"e": "text", "t": "Security"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "media_metadata": {"6b1s7ulxonff1": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 59, "x": 108, "u": "https://preview.redd.it/6b1s7ulxonff1.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=491373f83675b89274a3c07d46fc85daed123727"}, {"y": 119, "x": 216, "u": "https://preview.redd.it/6b1s7ulxonff1.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=6690d13021180081bd12cd7157880fb3c55d2569"}, {"y": 177, "x": 320, "u": "https://preview.redd.it/6b1s7ulxonff1.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=b4e587a3f2d4ed5beb9c373b2ec4624984d2bb73"}, {"y": 355, "x": 640, "u": "https://preview.redd.it/6b1s7ulxonff1.png?width=640&amp;crop=smart&amp;auto=webp&amp;s=a31e44962dcd8a979ea302bc9d7d1f34a4d80bd6"}], "s": {"y": 460, "x": 829, "u": "https://preview.redd.it/6b1s7ulxonff1.png?width=829&amp;format=png&amp;auto=webp&amp;s=5d510f9e8c94dbfc7fcc2ad7f6a56b7ba84a177d"}, "id": "6b1s7ulxonff1"}}, "name": "t3_1mbns9y", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.25, "author_flair_background_color": null, "subreddit_type": "public", "ups": 0, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Security", "can_mod_post": false, "score": 0, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753726413.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;The Linux kernel provides the ability for cryptographically signing kernel modules during their installation. Thus, when they are being loaded the signature is validated. By doing so we increase the kernel security due to the fact that unsigned kernel modules\\signed modules with an invalid key(s) are blocked from loading. We can leverage different hashing algorithms as part of the signing process like: SHA-1,SH-224, SHA-256, SHA-384 and SHA-512. Also, the public key for singing is handled using X.509 ITU-T standard certificates (&lt;a href=\"https://www.kernel.org/doc/html/v4.19/admin-guide/module-signing.html\"&gt;https://www.kernel.org/doc/html/v4.19/admin-guide/module-signing.html&lt;/a&gt;). Based on the kernel configuration modules can be signed using a RSA key which is controlled by “CONFIG_MODULE_SIG_KEY_TYPE_RSA” (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/certs/Kconfig#L25\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/certs/Kconfig#L25&lt;/a&gt;) or using an elliptic curve key controlled by “CONFIG_MODULE_SIG_KEY_TYPE_ECDSA” (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/certs/Kconfig#L30\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/certs/Kconfig#L30&lt;/a&gt;). By the way, in case a kernel module is signed we can check out different attributes such as: the signature, hashing algorithm used, the signing key, the name of the signer and more using the “modinfo” (&lt;a href=\"https://linux.die.net/man/8/modinfo\"&gt;https://linux.die.net/man/8/modinfo&lt;/a&gt;) utility — as shown in the screenshot below.&lt;/p&gt;\n\n&lt;p&gt;Overall, probably the main structure related to module singing is “struct module_signature” (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/include/linux/module_signature.h#L33\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/include/linux/module_signature.h#L33&lt;/a&gt;). It is also known as the “module signature information block” that contains: signer’s name, key identifier, signature data and information block (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/include/linux/module_signature.h#L24\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/include/linux/module_signature.h#L24&lt;/a&gt;). It is leveraged in the kernel in different places such as (but not limited to): a code for signing a module file using a given key (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/scripts/sign-file.c#L222\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/scripts/sign-file.c#L222&lt;/a&gt;), as part of IMA (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/security/integrity/ima/ima_modsig.c#L44\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/security/integrity/ima/ima_modsig.c#L44&lt;/a&gt;), verifying the kernel signature during “kexec_file_load” (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/arch/s390/kernel/machine_kexec_file.c#L28\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/arch/s390/kernel/machine_kexec_file.c#L28&lt;/a&gt;) and as part of “mod_verify_sig” (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L45\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L45&lt;/a&gt;) which is used for verifying the signature of a module.&lt;/p&gt;\n\n&lt;p&gt;Lastly, the general flow is that the “init_module_from_file” function calls “load_module” (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/main.c#L3601\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/main.c#L3601&lt;/a&gt;). Than the “load_module” (used for allocating and loading the module) function calls the “module_sig_check” which does the signature check (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/main.c#L3275\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/main.c#L3275&lt;/a&gt;). “module_sig_check” calls “mod_verify_sig” (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L87\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L87&lt;/a&gt;). Based on the return value from “mod_verify_sig” the “module_sig_check” function created the appropriate error message (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L99\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L99&lt;/a&gt;) and emits the appropriate log entry (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L120\"&gt;https://elixir.bootlin.com/linux/v6.15.6/source/kernel/module/signing.c#L120&lt;/a&gt;).&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/6b1s7ulxonff1.png?width=829&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=5d510f9e8c94dbfc7fcc2ad7f6a56b7ba84a177d\"&gt;https://preview.redd.it/6b1s7ulxonff1.png?width=829&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=5d510f9e8c94dbfc7fcc2ad7f6a56b7ba84a177d&lt;/a&gt;&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "f5ca482e-5da5-11ec-8c3d-42d730f72f5c", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "1mbns9y", "is_robot_indexable": true, "report_reasons": null, "author": "<PERSON><PERSON><PERSON>", "discussion_type": null, "num_comments": 3, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1mbns9y/kernel_module_signing/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1mbns9y/kernel_module_signing/", "subreddit_subscribers": 1732389, "created_utc": 1753726413.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_7jex7", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Fast and cheap bulk storage: using LVM to cache HDDs on SSDs", "link_flair_richtext": [{"e": "text", "t": "Tips and Tricks"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1malkn2", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.81, "author_flair_background_color": "transparent", "subreddit_type": "public", "ups": 19, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": "fa602e36-cdf6-11e8-93c9-0e41ac35f4cc", "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Tips and Tricks", "can_mod_post": false, "score": 19, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [{"a": ":ubuntu:", "e": "emoji", "u": "https://emoji.redditmedia.com/uwmddx7qqpr11_t5_2qh1a/ubuntu"}, {"e": "text", "t": " <PERSON><PERSON>"}], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753620877.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "richtext", "domain": "quantum5.ca", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://quantum5.ca/2025/05/11/fast-cheap-bulk-storage-using-lvm-to-cache-hdds-on-ssds/", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "de62f716-76df-11ea-802c-0e7469f68f6b", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": ":ubuntu: <PERSON><PERSON>", "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#00a6a5", "id": "1malkn2", "is_robot_indexable": true, "report_reasons": null, "author": "ou<PERSON><PERSON>", "discussion_type": null, "num_comments": 7, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": "dark", "permalink": "/r/linux/comments/1malkn2/fast_and_cheap_bulk_storage_using_lvm_to_cache/", "stickied": false, "url": "https://quantum5.ca/2025/05/11/fast-cheap-bulk-storage-using-lvm-to-cache-hdds-on-ssds/", "subreddit_subscribers": 1732389, "created_utc": 1753620877.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "# Fix for [UPSilon 2000 v5.5 software installer](https://www.megatec.com.tw/software-download/) for Ubuntu 24.04 / Debian 12 (for UPSilon 2000 UPS)\n\nGitHub repo to download patched install.linux: [https://github.com/MarsTheProtogen/upsilon-linux-fix](https://github.com/MarsTheProtogen/upsilon-linux-fix)\n\n# NOTES:\n\nnot sure if this is the right place to put this post, feel free to suggest a relocation\n\nThe patched script simply skips upsilon.eml and upsilon.pgr if they’re missing.\n\n&gt;If you need {\n\n&gt;Email updates;\n\n&gt;SMS updates;\n\n&gt;}\n\n&gt;then {\n\n&gt;put the needed helper scripts into /etc/upsilon/\n\n&gt;}\n\n# 1. Install the missing library (libtinfo5)\n\n    # grab the last maintained build\n    wget http://security.ubuntu.com/ubuntu/pool/universe/n/ncurses/libtinfo5_6.3-2ubuntu0.1_amd64.deb\n    \n    # install \n    sudo apt install ./libtinfo5_6.3-2ubuntu0.1_amd64.deb\n\n\\*32‑bit uses the i386 deb instead.)\n\n# 2 Replace the install.linux with this patched one\n\nPatched script:\n\n    #!/bin/sh\n    # Patched UPSilon 2000 installer – July 2025 by MarsTheProtogen on github\n    # - Quotes variables (supports paths with spaces)\n    # - Skips optional helper files (upsilon.eml / upsilon.pgr) if absent\n    # - Auto‑symlinks libncurses.so.5 &amp; libtinfo.so.5 → *.so.6 when packages are missing\n    #   (so the program starts even if only the -6 libs are present)\n    \n    PROG=rupsd\n    INSTALL_DIR=\"$(pwd)\"\n    PROGRAM_DIR=/etc/upsilon\n    \n    echo \"Linux 2.x INSTALL FOR UPSilon 2000 (patched)\"\n    \n    [ \"$(id -u)\" -eq 0 ] || { echo \"Run as root.\"; exit 1; }\n    \n    echo \"UPSilon 2000 will be installed to $PROGRAM_DIR.\"\n    \n    # stop any running daemon\n    [ -x \"$PROGRAM_DIR/upsilon\" ] &amp;&amp; \"$PROGRAM_DIR/upsilon\" stop 2&gt;/dev/null\n    \n    # backup previous install\n    [ -d \"$PROGRAM_DIR\" ] &amp;&amp; { rm -rf \"$PROGRAM_DIR.old\"; mv \"$PROGRAM_DIR\" \"$PROGRAM_DIR.old\"; }\n    \n    mkdir -p \"$PROGRAM_DIR\"\n    \n    echo -n \"Copying files \"\n    for f in rupsd upsilon email pager shutdown.ini rups.ini preshut.bat upsilon.eml upsilon.pgr; do\n      if [ -s \"$INSTALL_DIR/$f\" ]; then\n         cp \"$INSTALL_DIR/$f\" \"$PROGRAM_DIR\" &amp;&amp; echo -n \".\"\n      fi\n    done\n    echo \" OK\"\n    \n    chmod 544 \"$PROGRAM_DIR/rupsd\"\n    chmod 555 \"$PROGRAM_DIR/upsilon\"\n    \n    # add legacy lib symlinks if packages not installed\n    for lib in ncurses tinfo; do\n      ldconfig -p | grep -q \"lib${lib}.so.5\" || {\n        [ -e /lib/x86_64-linux-gnu/lib${lib}.so.6 ] &amp;&amp; \\\n        ln -sf /lib/x86_64-linux-gnu/lib${lib}.so.6 /lib/x86_64-linux-gnu/lib${lib}.so.5\n      }\n    done\n    ldconfig\n    \n    \"$PROGRAM_DIR/upsilon\" reginit\n    \"$PROGRAM_DIR/upsilon\" start &amp;&amp; echo \"Installation completed!\"\n\nSave it over the existing install.linux and:\n\n    # make sure the file is exicuteable \n    chmod +x install.linux\n\n# 2.5 make sure that there are no actively running upsilon processes\n\n&gt;the installer may say \\*\\*Please stop the UPSilon 2000 background process\\*\\* you will need to list the current upsilon processes twice in case the first one you see isn't \"actually\" doing stuff\n\n    ps aux | grep -i upsilon\n    \n    # you should see something like:\n    $ ps aux | grep -i upsilon\n        user      2573  0.0  0.1  15480  5556 ?  Ssl  14:02   0:00 /etc/upsilon/rupsd\n        user      2589  0.0  0.0   9212  2168 ?  Ss   14:02   0:00 /etc/upsilon/upsilon \n    \n    $ ps aux | grep -i upsilon\n        user      2573  0.0  0.1  15480  5556 ?  Ssl  14:02   0:00 /etc/upsilon/rupsd\n        user      3690  0.0  0.0   9212  2168 ?  Ss   14:02   0:00 /etc/upsilon/upsilon \n\nyou want to sudo kill 2573 as it's an process that's doing something\n\n# 3 Run the installer\n\n    sudo ./install.linux\n\n&gt;you may need to try 2.5 again and/ or sudo /etc/upsilon/upsilon stop\n\n# 4 Register &amp; configure without the CLI\n\nthe CLI doesn't work for me, so I manually changed the .ini file\n\nTHIS MAY NOT WORK\n\n&gt;there is a warning saying protection will be disabled after 30 days is not registered properly, and as of this post's creation, not tested by time\n\n    # stop daemon \n    sudo /etc/upsilon/upsilon stop\n    \n    # edit registration info\n    sudo nano /etc/upsilon/rups.ini\n    # [REGISTRATION]         \n    # CDKEY=AAAAAAA-BBBBBBB  \n    # EMAIL=<EMAIL>  \n    # PASSWORD= ****\n    \n    \n    # flush cache &amp; restart\n    sudo /etc/upsilon/upsilon reginit\n    sudo /etc/upsilon/upsilon start\n    sudo /etc/upsilon/upsilon status   # shows voltage, battery, etc.\n\n# extra upsilon commands\n\n|Path (as root)|Purpose / Action|Typical use‑case or note|\n|:-|:-|:-|\n|/etc/upsilon/upsilon start|Start the background daemon (rupsd).|Run at boot via rc.local; use manually for testing.|\n|/etc/upsilon/upsilon stop|Gracefully stop the daemon.|Always try this before any pkill brute‑force.|\n|/etc/upsilon/upsilon restart|Convenience wrapper: stop → 1 s wait → start.|Useful after editing rups.ini.|\n|/etc/upsilon/upsilon status|One‑shot status dump (line‑voltage, battery %).|Quick health check from the shell.|\n|/etc/upsilon/upsilon config|Launch the text‑mode parameter editor.|Change serial port, shutdown timer, etc.|\n|/etc/upsilon/upsilon reginit|Flush license cache &amp; reread rups.ini.|Run after you edit CD‑Key or e‑mail by hand.|\n|/etc/upsilon/upsilon issuer|Send direct commands to the UPS (on/off, test).|Advanced / diagnostic only.|\n|/etc/upsilon/upsilon help|Bare‑bones help screen (same text as README).|Shows key bindings.|\n|/etc/upsilon/upsilon.eml|Helper script for e‑mail alerts (shell script).|Called automatically when you enable e‑mail events.|\n|/etc/upsilon/upsilon.pgr|Helper script for pager/SMS alerts.|Legacy dial‑out; safe to leave empty if unused.|\n|/etc/upsilon/rupsd|The actual daemon binary UPSilon controls.|Started by upsilon start; seldom called directly.|\n|/etc/upsilon/rups.ini|Main INI file: CD‑Key, serial port, timers, etc.|Edit in a text editor, then run reginit.|\n|/etc/upsilon/rupslog|Rolling event log (plain text).|View with tail -f or any log watcher.|\n\n# ", "author_fullname": "t2_gdvcsxuz", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "[Fix] UPSilon 2000 installer on Ubuntu 24.04 – missing libncurses5/libtinfo5 &amp; copy abort", "link_flair_richtext": [{"e": "text", "t": "Tips and Tricks"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1mb74s5", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.46, "author_flair_background_color": null, "subreddit_type": "public", "ups": 0, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Tips and Tricks", "can_mod_post": false, "score": 0, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753677921.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;h1&gt;Fix for &lt;a href=\"https://www.megatec.com.tw/software-download/\"&gt;UPSilon 2000 v5.5 software installer&lt;/a&gt; for Ubuntu 24.04 / Debian 12 (for UPSilon 2000 UPS)&lt;/h1&gt;\n\n&lt;p&gt;GitHub repo to download patched install.linux: &lt;a href=\"https://github.com/MarsTheProtogen/upsilon-linux-fix\"&gt;https://github.com/MarsTheProtogen/upsilon-linux-fix&lt;/a&gt;&lt;/p&gt;\n\n&lt;h1&gt;NOTES:&lt;/h1&gt;\n\n&lt;p&gt;not sure if this is the right place to put this post, feel free to suggest a relocation&lt;/p&gt;\n\n&lt;p&gt;The patched script simply skips upsilon.eml and upsilon.pgr if they’re missing.&lt;/p&gt;\n\n&lt;blockquote&gt;\n&lt;p&gt;If you need {&lt;/p&gt;\n\n&lt;p&gt;Email updates;&lt;/p&gt;\n\n&lt;p&gt;SMS updates;&lt;/p&gt;\n\n&lt;p&gt;}&lt;/p&gt;\n\n&lt;p&gt;then {&lt;/p&gt;\n\n&lt;p&gt;put the needed helper scripts into /etc/upsilon/&lt;/p&gt;\n\n&lt;p&gt;}&lt;/p&gt;\n&lt;/blockquote&gt;\n\n&lt;h1&gt;1. Install the missing library (libtinfo5)&lt;/h1&gt;\n\n&lt;pre&gt;&lt;code&gt;# grab the last maintained build\nwget http://security.ubuntu.com/ubuntu/pool/universe/n/ncurses/libtinfo5_6.3-2ubuntu0.1_amd64.deb\n\n# install \nsudo apt install ./libtinfo5_6.3-2ubuntu0.1_amd64.deb\n&lt;/code&gt;&lt;/pre&gt;\n\n&lt;p&gt;*32‑bit uses the i386 deb instead.)&lt;/p&gt;\n\n&lt;h1&gt;2 Replace the install.linux with this patched one&lt;/h1&gt;\n\n&lt;p&gt;Patched script:&lt;/p&gt;\n\n&lt;pre&gt;&lt;code&gt;#!/bin/sh\n# Patched UPSilon 2000 installer – July 2025 by MarsTheProtogen on github\n# - Quotes variables (supports paths with spaces)\n# - Skips optional helper files (upsilon.eml / upsilon.pgr) if absent\n# - Auto‑symlinks libncurses.so.5 &amp;amp; libtinfo.so.5 → *.so.6 when packages are missing\n#   (so the program starts even if only the -6 libs are present)\n\nPROG=rupsd\nINSTALL_DIR=&amp;quot;$(pwd)&amp;quot;\nPROGRAM_DIR=/etc/upsilon\n\necho &amp;quot;Linux 2.x INSTALL FOR UPSilon 2000 (patched)&amp;quot;\n\n[ &amp;quot;$(id -u)&amp;quot; -eq 0 ] || { echo &amp;quot;Run as root.&amp;quot;; exit 1; }\n\necho &amp;quot;UPSilon 2000 will be installed to $PROGRAM_DIR.&amp;quot;\n\n# stop any running daemon\n[ -x &amp;quot;$PROGRAM_DIR/upsilon&amp;quot; ] &amp;amp;&amp;amp; &amp;quot;$PROGRAM_DIR/upsilon&amp;quot; stop 2&amp;gt;/dev/null\n\n# backup previous install\n[ -d &amp;quot;$PROGRAM_DIR&amp;quot; ] &amp;amp;&amp;amp; { rm -rf &amp;quot;$PROGRAM_DIR.old&amp;quot;; mv &amp;quot;$PROGRAM_DIR&amp;quot; &amp;quot;$PROGRAM_DIR.old&amp;quot;; }\n\nmkdir -p &amp;quot;$PROGRAM_DIR&amp;quot;\n\necho -n &amp;quot;Copying files &amp;quot;\nfor f in rupsd upsilon email pager shutdown.ini rups.ini preshut.bat upsilon.eml upsilon.pgr; do\n  if [ -s &amp;quot;$INSTALL_DIR/$f&amp;quot; ]; then\n     cp &amp;quot;$INSTALL_DIR/$f&amp;quot; &amp;quot;$PROGRAM_DIR&amp;quot; &amp;amp;&amp;amp; echo -n &amp;quot;.&amp;quot;\n  fi\ndone\necho &amp;quot; OK&amp;quot;\n\nchmod 544 &amp;quot;$PROGRAM_DIR/rupsd&amp;quot;\nchmod 555 &amp;quot;$PROGRAM_DIR/upsilon&amp;quot;\n\n# add legacy lib symlinks if packages not installed\nfor lib in ncurses tinfo; do\n  ldconfig -p | grep -q &amp;quot;lib${lib}.so.5&amp;quot; || {\n    [ -e /lib/x86_64-linux-gnu/lib${lib}.so.6 ] &amp;amp;&amp;amp; \\\n    ln -sf /lib/x86_64-linux-gnu/lib${lib}.so.6 /lib/x86_64-linux-gnu/lib${lib}.so.5\n  }\ndone\nldconfig\n\n&amp;quot;$PROGRAM_DIR/upsilon&amp;quot; reginit\n&amp;quot;$PROGRAM_DIR/upsilon&amp;quot; start &amp;amp;&amp;amp; echo &amp;quot;Installation completed!&amp;quot;\n&lt;/code&gt;&lt;/pre&gt;\n\n&lt;p&gt;Save it over the existing install.linux and:&lt;/p&gt;\n\n&lt;pre&gt;&lt;code&gt;# make sure the file is exicuteable \nchmod +x install.linux\n&lt;/code&gt;&lt;/pre&gt;\n\n&lt;h1&gt;2.5 make sure that there are no actively running upsilon processes&lt;/h1&gt;\n\n&lt;blockquote&gt;\n&lt;p&gt;the installer may say **Please stop the UPSilon 2000 background process** you will need to list the current upsilon processes twice in case the first one you see isn&amp;#39;t &amp;quot;actually&amp;quot; doing stuff&lt;/p&gt;\n&lt;/blockquote&gt;\n\n&lt;pre&gt;&lt;code&gt;ps aux | grep -i upsilon\n\n# you should see something like:\n$ ps aux | grep -i upsilon\n    user      2573  0.0  0.1  15480  5556 ?  Ssl  14:02   0:00 /etc/upsilon/rupsd\n    user      2589  0.0  0.0   9212  2168 ?  Ss   14:02   0:00 /etc/upsilon/upsilon \n\n$ ps aux | grep -i upsilon\n    user      2573  0.0  0.1  15480  5556 ?  Ssl  14:02   0:00 /etc/upsilon/rupsd\n    user      3690  0.0  0.0   9212  2168 ?  Ss   14:02   0:00 /etc/upsilon/upsilon \n&lt;/code&gt;&lt;/pre&gt;\n\n&lt;p&gt;you want to sudo kill 2573 as it&amp;#39;s an process that&amp;#39;s doing something&lt;/p&gt;\n\n&lt;h1&gt;3 Run the installer&lt;/h1&gt;\n\n&lt;pre&gt;&lt;code&gt;sudo ./install.linux\n&lt;/code&gt;&lt;/pre&gt;\n\n&lt;blockquote&gt;\n&lt;p&gt;you may need to try 2.5 again and/ or sudo /etc/upsilon/upsilon stop&lt;/p&gt;\n&lt;/blockquote&gt;\n\n&lt;h1&gt;4 Register &amp;amp; configure without the CLI&lt;/h1&gt;\n\n&lt;p&gt;the CLI doesn&amp;#39;t work for me, so I manually changed the .ini file&lt;/p&gt;\n\n&lt;p&gt;THIS MAY NOT WORK&lt;/p&gt;\n\n&lt;blockquote&gt;\n&lt;p&gt;there is a warning saying protection will be disabled after 30 days is not registered properly, and as of this post&amp;#39;s creation, not tested by time&lt;/p&gt;\n&lt;/blockquote&gt;\n\n&lt;pre&gt;&lt;code&gt;# stop daemon \nsudo /etc/upsilon/upsilon stop\n\n# edit registration info\nsudo nano /etc/upsilon/rups.ini\n# [REGISTRATION]         \n# CDKEY=AAAAAAA-BBBBBBB  \n# EMAIL=<EMAIL>  \n# PASSWORD= ****\n\n\n# flush cache &amp;amp; restart\nsudo /etc/upsilon/upsilon reginit\nsudo /etc/upsilon/upsilon start\nsudo /etc/upsilon/upsilon status   # shows voltage, battery, etc.\n&lt;/code&gt;&lt;/pre&gt;\n\n&lt;h1&gt;extra upsilon commands&lt;/h1&gt;\n\n&lt;table&gt;&lt;thead&gt;\n&lt;tr&gt;\n&lt;th align=\"left\"&gt;Path (as root)&lt;/th&gt;\n&lt;th align=\"left\"&gt;Purpose / Action&lt;/th&gt;\n&lt;th align=\"left\"&gt;Typical use‑case or note&lt;/th&gt;\n&lt;/tr&gt;\n&lt;/thead&gt;&lt;tbody&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon start&lt;/td&gt;\n&lt;td align=\"left\"&gt;Start the background daemon (rupsd).&lt;/td&gt;\n&lt;td align=\"left\"&gt;Run at boot via rc.local; use manually for testing.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon stop&lt;/td&gt;\n&lt;td align=\"left\"&gt;Gracefully stop the daemon.&lt;/td&gt;\n&lt;td align=\"left\"&gt;Always try this before any pkill brute‑force.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon restart&lt;/td&gt;\n&lt;td align=\"left\"&gt;Convenience wrapper: stop → 1 s wait → start.&lt;/td&gt;\n&lt;td align=\"left\"&gt;Useful after editing rups.ini.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon status&lt;/td&gt;\n&lt;td align=\"left\"&gt;One‑shot status dump (line‑voltage, battery %).&lt;/td&gt;\n&lt;td align=\"left\"&gt;Quick health check from the shell.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon config&lt;/td&gt;\n&lt;td align=\"left\"&gt;Launch the text‑mode parameter editor.&lt;/td&gt;\n&lt;td align=\"left\"&gt;Change serial port, shutdown timer, etc.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon reginit&lt;/td&gt;\n&lt;td align=\"left\"&gt;Flush license cache &amp;amp; reread rups.ini.&lt;/td&gt;\n&lt;td align=\"left\"&gt;Run after you edit CD‑Key or e‑mail by hand.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon issuer&lt;/td&gt;\n&lt;td align=\"left\"&gt;Send direct commands to the UPS (on/off, test).&lt;/td&gt;\n&lt;td align=\"left\"&gt;Advanced / diagnostic only.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon help&lt;/td&gt;\n&lt;td align=\"left\"&gt;Bare‑bones help screen (same text as README).&lt;/td&gt;\n&lt;td align=\"left\"&gt;Shows key bindings.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon.eml&lt;/td&gt;\n&lt;td align=\"left\"&gt;Helper script for e‑mail alerts (shell script).&lt;/td&gt;\n&lt;td align=\"left\"&gt;Called automatically when you enable e‑mail events.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/upsilon.pgr&lt;/td&gt;\n&lt;td align=\"left\"&gt;Helper script for pager/SMS alerts.&lt;/td&gt;\n&lt;td align=\"left\"&gt;Legacy dial‑out; safe to leave empty if unused.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/rupsd&lt;/td&gt;\n&lt;td align=\"left\"&gt;The actual daemon binary UPSilon controls.&lt;/td&gt;\n&lt;td align=\"left\"&gt;Started by upsilon start; seldom called directly.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/rups.ini&lt;/td&gt;\n&lt;td align=\"left\"&gt;Main INI file: CD‑Key, serial port, timers, etc.&lt;/td&gt;\n&lt;td align=\"left\"&gt;Edit in a text editor, then run reginit.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;tr&gt;\n&lt;td align=\"left\"&gt;/etc/upsilon/rupslog&lt;/td&gt;\n&lt;td align=\"left\"&gt;Rolling event log (plain text).&lt;/td&gt;\n&lt;td align=\"left\"&gt;View with tail -f or any log watcher.&lt;/td&gt;\n&lt;/tr&gt;\n&lt;/tbody&gt;&lt;/table&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "de62f716-76df-11ea-802c-0e7469f68f6b", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#00a6a5", "id": "1mb74s5", "is_robot_indexable": true, "report_reasons": null, "author": "Alert-Bet-5672", "discussion_type": null, "num_comments": 0, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1mb74s5/fix_upsilon_2000_installer_on_ubuntu_2404_missing/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1mb74s5/fix_upsilon_2000_installer_on_ubuntu_2404_missing/", "subreddit_subscribers": 1732389, "created_utc": 1753677921.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_kgcds", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "How we Rooted Copilot (cause it's running from a customized Ubuntu container)", "link_flair_richtext": [{"e": "text", "t": "Security"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1ma12we", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.92, "author_flair_background_color": null, "subreddit_type": "public", "ups": 110, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Security", "can_mod_post": false, "score": 110, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753555745.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "research.eye.security", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://research.eye.security/how-we-rooted-copilot/", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "f5ca482e-5da5-11ec-8c3d-42d730f72f5c", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "1ma12we", "is_robot_indexable": true, "report_reasons": null, "author": "<PERSON><PERSON><PERSON>", "discussion_type": null, "num_comments": 5, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1ma12we/how_we_rooted_copilot_cause_its_running_from_a/", "stickied": false, "url": "https://research.eye.security/how-we-rooted-copilot/", "subreddit_subscribers": 1732389, "created_utc": 1753555745.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "Hello everyone, my dad who got me into computers back in 2006 or so has gotten out of them and just stuck to windows, but today after he's gotten a new desktop a while back he's down with trying to daily drive linux mint!\n\nHe uses SDRs and other radios and softwares but they're usually old so I feel we should be able to use them on wine, if not that's the reason for the dual boot, he doesn't use it daily or even weekly.\n\nHe spends a lot of time trying to make windows faster, more secure, etc. but he really can't so I think he'll love playing with this, not having to play with it, or maybe even learning about real security (I'm in Purple Team security so I can help guide him and teach him) like firewalls and static code scanners and stuff instead of Geek Squad and random youtube tutorials lol\n\n<PERSON> was excited and wanted to share!\n\nCheers!", "author_fullname": "t2_sna1xju4g", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Oh blessed day, my dad was down with a dual boot to try and daily drive Linux Mint! His first Linux distro!", "link_flair_richtext": [{"e": "text", "t": "<PERSON><PERSON><PERSON>"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1ma7ogk", "quarantine": false, "link_flair_text_color": "light", "upvote_ratio": 0.85, "author_flair_background_color": null, "subreddit_type": "public", "ups": 27, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "<PERSON><PERSON><PERSON>", "can_mod_post": false, "score": 27, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753573047.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Hello everyone, my dad who got me into computers back in 2006 or so has gotten out of them and just stuck to windows, but today after he&amp;#39;s gotten a new desktop a while back he&amp;#39;s down with trying to daily drive linux mint!&lt;/p&gt;\n\n&lt;p&gt;He uses SDRs and other radios and softwares but they&amp;#39;re usually old so I feel we should be able to use them on wine, if not that&amp;#39;s the reason for the dual boot, he doesn&amp;#39;t use it daily or even weekly.&lt;/p&gt;\n\n&lt;p&gt;He spends a lot of time trying to make windows faster, more secure, etc. but he really can&amp;#39;t so I think he&amp;#39;ll love playing with this, not having to play with it, or maybe even learning about real security (I&amp;#39;m in Purple Team security so I can help guide him and teach him) like firewalls and static code scanners and stuff instead of Geek Squad and random youtube tutorials lol&lt;/p&gt;\n\n&lt;p&gt;Just was excited and wanted to share!&lt;/p&gt;\n\n&lt;p&gt;Cheers!&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "af8918be-6777-11e7-8273-0e925d908786", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#9a2bff", "id": "1ma7ogk", "is_robot_indexable": true, "report_reasons": null, "author": "NuggetNasty", "discussion_type": null, "num_comments": 9, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1ma7ogk/oh_blessed_day_my_dad_was_down_with_a_dual_boot/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1ma7ogk/oh_blessed_day_my_dad_was_down_with_a_dual_boot/", "subreddit_subscribers": 1732389, "created_utc": 1753573047.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_ngxf7", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Linux Kernel Proposal Documents Rules For Using AI Coding Assistants", "link_flair_richtext": [{"e": "text", "t": "<PERSON><PERSON>"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1m9uub4", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.94, "author_flair_background_color": null, "subreddit_type": "public", "ups": 132, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "<PERSON><PERSON>", "can_mod_post": false, "score": 132, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753540475.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "phoronix.com", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://www.phoronix.com/news/Linux-Kernel-AI-Docs-Rules", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "5a277eb4-5859-11e8-b1d4-0eb3c1459ab0", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#ffd133", "id": "1m9uub4", "is_robot_indexable": true, "report_reasons": null, "author": "mrlin<PERSON><PERSON><PERSON>", "discussion_type": null, "num_comments": 67, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1m9uub4/linux_kernel_proposal_documents_rules_for_using/", "stickied": false, "url": "https://www.phoronix.com/news/Linux-Kernel-AI-Docs-Rules", "subreddit_subscribers": 1732389, "created_utc": 1753540475.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_3kanv", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Linux Will Finally Be Able To Reboot Apple M1/M2 Macs With The v6.17 Kernel", "link_flair_richtext": [{"e": "text", "t": "<PERSON><PERSON>"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1m9m0dk", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.98, "author_flair_background_color": null, "subreddit_type": "public", "ups": 541, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "<PERSON><PERSON>", "can_mod_post": false, "score": 541, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753510151.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "phoronix.com", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://www.phoronix.com/news/Linux-6.17-Apple-SMC", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "5a277eb4-5859-11e8-b1d4-0eb3c1459ab0", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#ffd133", "id": "1m9m0dk", "is_robot_indexable": true, "report_reasons": null, "author": "unixbhaskar", "discussion_type": null, "num_comments": 37, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1m9m0dk/linux_will_finally_be_able_to_reboot_apple_m1m2/", "stickied": false, "url": "https://www.phoronix.com/news/Linux-6.17-Apple-SMC", "subreddit_subscribers": 1732389, "created_utc": 1753510151.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "Has anybody worked on opens source projects with many developers? How does the project gets started? How does it work? How do people join the project? Please share your experiences with both small, large and individual projects.\nI am asking about both Linux distros and smaller applications that run on Linux.", "author_fullname": "t2_adnzl8f8x", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "How do open source Linux projects work?", "link_flair_richtext": [{"e": "text", "t": "Development"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1macbka", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.61, "author_flair_background_color": null, "subreddit_type": "public", "ups": 8, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Development", "can_mod_post": false, "score": 8, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": 1753587957.0, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753587301.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Has anybody worked on opens source projects with many developers? How does the project gets started? How does it work? How do people join the project? Please share your experiences with both small, large and individual projects.\nI am asking about both Linux distros and smaller applications that run on Linux.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "3cb511e2-7914-11ea-bb33-0ee30ee9d22b", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#f0db8a", "id": "1macbka", "is_robot_indexable": true, "report_reasons": null, "author": "Maleficent_Mess6445", "discussion_type": null, "num_comments": 17, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1macbka/how_do_open_source_linux_projects_work/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1macbka/how_do_open_source_linux_projects_work/", "subreddit_subscribers": 1732389, "created_utc": 1753587301.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_2op3d34c", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "Linux 6.17 Will Be Exciting With Intel \"Project Battlematrix\" GPU Driver Changes &amp; More", "link_flair_richtext": [{"e": "text", "t": "Hardware"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1m9tmnh", "quarantine": false, "link_flair_text_color": "light", "upvote_ratio": 0.97, "author_flair_background_color": null, "subreddit_type": "public", "ups": 66, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Hardware", "can_mod_post": false, "score": 66, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753537239.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "phoronix.com", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://www.phoronix.com/news/Linux-6.17-Early-Features", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "3d48793a-c823-11e8-9a58-0ee3c97eb952", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#cc5289", "id": "1m9tmnh", "is_robot_indexable": true, "report_reasons": null, "author": "reps_up", "discussion_type": null, "num_comments": 2, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1m9tmnh/linux_617_will_be_exciting_with_intel_project/", "stickied": false, "url": "https://www.phoronix.com/news/Linux-6.17-Early-Features", "subreddit_subscribers": 1732389, "created_utc": 1753537239.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "Like many in the last year, I have been looking to jump ship from Windows. Started with <PERSON><PERSON><PERSON> on a separate rig which I tinkered with JUST enough to make sure it would run, and have largely left it alone because the games are playable and already beat the performance of my currently-Windows primary desktop. Of course, <PERSON><PERSON>te isn't really meant for a desktop environment, so I decided to mess around on my laptop.\n\nCachyOS (obligatory \"I use Arch, btw,\" even if it does do some handholding compared to other Arch distros). Honestly, learning this has been one of the most fun things I have done on a computer in a long while. Learning the jargon, getting lost in the terminal, tweaking with settings, messing with drivers. I knew that there had been some points along the way that I probably installed way too many bunk or deprecated files by accident, and I have been wanting to give it another go from a fresh build and apply what I've learned. Well, now I have no choice.\n\nLast night, I decided to do what I thought was a harmless act since it had been a couple weeks since I turned it on: sudo pacman -Syu\n\n3.5 minutes later I get the notification that my system may need to be restarted. Now it crashes into a black terminal box because it seems to be missing some hook.\n\nThis post is not a cry for help. I will learn and keep moving forward. This is more just to say for all the other Linux noobs out there, you WILL break things, even if by accident, and that is okay. Just gotta pick yourself up and move on. Also a friendly reminder to make sure you're backing things up regularly. I definitely need to make sure I know how to do that. ", "author_fullname": "t2_86rzth8", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "My First Linux Distro Kill! (I think)", "link_flair_richtext": [{"e": "text", "t": "Discussion"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1maz0es", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.33, "author_flair_background_color": null, "subreddit_type": "public", "ups": 0, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Discussion", "can_mod_post": false, "score": 0, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753653991.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;Like many in the last year, I have been looking to jump ship from Windows. Started with <PERSON><PERSON><PERSON> on a separate rig which I tinkered with JUST enough to make sure it would run, and have largely left it alone because the games are playable and already beat the performance of my currently-Windows primary desktop. Of course, <PERSON>zzite isn&amp;#39;t really meant for a desktop environment, so I decided to mess around on my laptop.&lt;/p&gt;\n\n&lt;p&gt;CachyOS (obligatory &amp;quot;I use Arch, btw,&amp;quot; even if it does do some handholding compared to other Arch distros). Honestly, learning this has been one of the most fun things I have done on a computer in a long while. Learning the jargon, getting lost in the terminal, tweaking with settings, messing with drivers. I knew that there had been some points along the way that I probably installed way too many bunk or deprecated files by accident, and I have been wanting to give it another go from a fresh build and apply what I&amp;#39;ve learned. Well, now I have no choice.&lt;/p&gt;\n\n&lt;p&gt;Last night, I decided to do what I thought was a harmless act since it had been a couple weeks since I turned it on: sudo pacman -Syu&lt;/p&gt;\n\n&lt;p&gt;3.5 minutes later I get the notification that my system may need to be restarted. Now it crashes into a black terminal box because it seems to be missing some hook.&lt;/p&gt;\n\n&lt;p&gt;This post is not a cry for help. I will learn and keep moving forward. This is more just to say for all the other Linux noobs out there, you WILL break things, even if by accident, and that is okay. Just gotta pick yourself up and move on. Also a friendly reminder to make sure you&amp;#39;re backing things up regularly. I definitely need to make sure I know how to do that. &lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "b7bc6ebe-a68e-11eb-9aa7-0e5d2b55839b", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "1maz0es", "is_robot_indexable": true, "report_reasons": null, "author": "Pugh95Bear", "discussion_type": null, "num_comments": 18, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1maz0es/my_first_linux_distro_kill_i_think/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1maz0es/my_first_linux_distro_kill_i_think/", "subreddit_subscribers": 1732389, "created_utc": 1753653991.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "", "author_fullname": "t2_voe0b", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "DHH Switches from Mac to Linux After 20 Years", "link_flair_richtext": [{"e": "text", "t": "Discussion"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1maza0b", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.4, "author_flair_background_color": "transparent", "subreddit_type": "public", "ups": 0, "total_awards_received": 0, "media_embed": {"content": "&lt;iframe width=\"356\" height=\"200\" src=\"https://www.youtube.com/embed/vagyIcmIGOQ?start=18340&amp;feature=oembed&amp;enablejsapi=1\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" referrerpolicy=\"strict-origin-when-cross-origin\" allowfullscreen title=\"DHH: Future of Programming, AI, Ruby on Rails, Productivity &amp;amp; Parenting | <PERSON>idman Podcast #474\"&gt;&lt;/iframe&gt;", "width": 356, "scrolling": false, "height": 200}, "author_flair_template_id": "ff002768-ce08-11e8-9f20-0e7c68d0424a", "is_original_content": false, "user_reports": [], "secure_media": {"oembed": {"provider_url": "https://www.youtube.com/", "title": "DHH: Future of Programming, AI, Ruby on Rails, Productivity &amp; Parenting | <PERSON> Podcast #474", "html": "&lt;iframe width=\"356\" height=\"200\" src=\"https://www.youtube.com/embed/vagyIcmIGOQ?start=18340&amp;feature=oembed&amp;enablejsapi=1\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" referrerpolicy=\"strict-origin-when-cross-origin\" allowfullscreen title=\"DHH: Future of Programming, AI, Ruby on Rails, Productivity &amp;amp; Parenting | <PERSON>idman Podcast #474\"&gt;&lt;/iframe&gt;", "thumbnail_width": 480, "height": 200, "width": 356, "version": "1.0", "author_name": "<PERSON>", "provider_name": "YouTube", "thumbnail_url": "https://i.ytimg.com/vi/vagyIcmIGOQ/hqdefault.jpg", "type": "video", "thumbnail_height": 360, "author_url": "https://www.youtube.com/@lexfridman"}, "type": "youtube.com"}, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {"content": "&lt;iframe width=\"356\" height=\"200\" src=\"https://www.youtube.com/embed/vagyIcmIGOQ?start=18340&amp;feature=oembed&amp;enablejsapi=1\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" referrerpolicy=\"strict-origin-when-cross-origin\" allowfullscreen title=\"DHH: Future of Programming, AI, Ruby on Rails, Productivity &amp;amp; Parenting | <PERSON>idman Podcast #474\"&gt;&lt;/iframe&gt;", "width": 356, "scrolling": false, "media_domain_url": "https://www.redditmedia.com/mediaembed/1maza0b", "height": 200}, "link_flair_text": "Discussion", "can_mod_post": false, "score": 0, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [{"a": ":void:", "e": "emoji", "u": "https://emoji.redditmedia.com/lt1tos9qqpr11_t5_2qh1a/void"}], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": **********.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "richtext", "domain": "youtu.be", "allow_live_comments": false, "selftext_html": null, "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://youtu.be/vagyIcmIGOQ?t=18340", "view_count": null, "archived": false, "no_follow": true, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "b7bc6ebe-a68e-11eb-9aa7-0e5d2b55839b", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": ":void:", "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "1maza0b", "is_robot_indexable": true, "report_reasons": null, "author": "BinkReddit", "discussion_type": null, "num_comments": 14, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": "dark", "permalink": "/r/linux/comments/1maza0b/dhh_switches_from_mac_to_linux_after_20_years/", "stickied": false, "url": "https://youtu.be/vagyIcmIGOQ?t=18340", "subreddit_subscribers": 1732389, "created_utc": **********.0, "num_crossposts": 0, "media": {"oembed": {"provider_url": "https://www.youtube.com/", "title": "DHH: Future of Programming, AI, Ruby on Rails, Productivity &amp; Parenting | <PERSON> Podcast #474", "html": "&lt;iframe width=\"356\" height=\"200\" src=\"https://www.youtube.com/embed/vagyIcmIGOQ?start=18340&amp;feature=oembed&amp;enablejsapi=1\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" referrerpolicy=\"strict-origin-when-cross-origin\" allowfullscreen title=\"DHH: Future of Programming, AI, Ruby on Rails, Productivity &amp;amp; Parenting | <PERSON>idman Podcast #474\"&gt;&lt;/iframe&gt;", "thumbnail_width": 480, "height": 200, "width": 356, "version": "1.0", "author_name": "<PERSON>", "provider_name": "YouTube", "thumbnail_url": "https://i.ytimg.com/vi/vagyIcmIGOQ/hqdefault.jpg", "type": "video", "thumbnail_height": 360, "author_url": "https://www.youtube.com/@lexfridman"}, "type": "youtube.com"}, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "In case an LKM aka “Loadable Kernel Module” ([https://medium.com/@boutnaru/the-linux-concept-journey-loadable-kernel-module-lkm-5eaa4db346a1](https://medium.com/@boutnaru/the-linux-concept-journey-loadable-kernel-module-lkm-5eaa4db346a1)) is loaded it can basically execute any code in kernel mode. Thus, the disable kernel module is a security feature that helps in hardening the system against attempts of loading malicious kernel modules like rootkits ([https://dfir.ch/posts/today\\_i\\_learned\\_lkm\\_kernel.modules\\_disabled/](https://dfir.ch/posts/today_i_learned_lkm_kernel.modules_disabled/)). It is important to understand that once enabled, modules can be neither loaded or unloaded ([https://sysctl-explorer.net/kernel/modules\\_disabled/](https://sysctl-explorer.net/kernel/modules_disabled/)).\n\nOverall, the configuration of this security feature is saved into the “modules\\_disabled” variable ([https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L129](https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L129)). Thus, beside checking for the “CAP\\_SYS\\_MODULE” capability when trying to unload a kernel module ([https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L732](https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L732)) or when trying to load a kernel module ([https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L3047](https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L3047)) the “modules\\_disabled” is also checked.\n\nLastly, We can enable\\\\disable this feature by writing “1” to “/proc/sys/kernel/modules\\_disabled” (“echo 1 &gt; /proc/sys/kernel/modules\\_disabled”) or using sysctl (“sysctl kernel.modules\\_disabled = 1”). In case the feature is enabled when we try to load a kernel module with “insmod” ([https://man7.org/linux/man-pages/man8/insmod.8.html](https://man7.org/linux/man-pages/man8/insmod.8.html)) the operation will fail ([https://linux-audit.com/kernel/increase-kernel-integrity-with-disabled-linux-kernel-modules-loading/](https://linux-audit.com/kernel/increase-kernel-integrity-with-disabled-linux-kernel-modules-loading/)) — as shown in the screenshot below. By the way, the same goes when trying to remove a module using for example “rmmod” ([https://linux.die.net/man/8/rmmod](https://linux.die.net/man/8/rmmod)). Remember we can use “modprobe” for performing both operations ([https://linux.die.net/man/8/modprobe](https://linux.die.net/man/8/modprobe)).\n\n[https:\\/\\/linux-audit.com\\/kernel\\/increase-kernel-integrity-with-disabled-linux-kernel-modules-loading\\/](https://preview.redd.it/fhy190l27gff1.png?width=758&amp;format=png&amp;auto=webp&amp;s=d67b0dd69c065404c9d551c1543cea51e9dcae7a)\n\n", "author_fullname": "t2_sx5cie38", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "The Linux Security Journey — Disable Kernel Modules", "link_flair_richtext": [{"e": "text", "t": "Security"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "media_metadata": {"fhy190l27gff1": {"status": "valid", "e": "Image", "m": "image/png", "p": [{"y": 14, "x": 108, "u": "https://preview.redd.it/fhy190l27gff1.png?width=108&amp;crop=smart&amp;auto=webp&amp;s=bfb90453cdf3c1414ca28e19a7cb74fa993fb3c4"}, {"y": 28, "x": 216, "u": "https://preview.redd.it/fhy190l27gff1.png?width=216&amp;crop=smart&amp;auto=webp&amp;s=55c5675a2a5e4d9eea98d9ef259e60b0d04e209d"}, {"y": 42, "x": 320, "u": "https://preview.redd.it/fhy190l27gff1.png?width=320&amp;crop=smart&amp;auto=webp&amp;s=c5a8ea051de97c6f1f0e48731036e569fb5655fa"}, {"y": 85, "x": 640, "u": "https://preview.redd.it/fhy190l27gff1.png?width=640&amp;crop=smart&amp;auto=webp&amp;s=ef6745519844d41b136ea3c1982c87feecb227c5"}], "s": {"y": 101, "x": 758, "u": "https://preview.redd.it/fhy190l27gff1.png?width=758&amp;format=png&amp;auto=webp&amp;s=d67b0dd69c065404c9d551c1543cea51e9dcae7a"}, "id": "fhy190l27gff1"}}, "name": "t3_1margu4", "quarantine": false, "link_flair_text_color": "dark", "upvote_ratio": 0.43, "author_flair_background_color": null, "subreddit_type": "public", "ups": 0, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": false, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Security", "can_mod_post": false, "score": 0, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": true, "mod_note": null, "created": 1753635652.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "self.linux", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;In case an LKM aka “Loadable Kernel Module” (&lt;a href=\"https://medium.com/@boutnaru/the-linux-concept-journey-loadable-kernel-module-lkm-5eaa4db346a1\"&gt;https://medium.com/@boutnaru/the-linux-concept-journey-loadable-kernel-module-lkm-5eaa4db346a1&lt;/a&gt;) is loaded it can basically execute any code in kernel mode. Thus, the disable kernel module is a security feature that helps in hardening the system against attempts of loading malicious kernel modules like rootkits (&lt;a href=\"https://dfir.ch/posts/today_i_learned_lkm_kernel.modules_disabled/\"&gt;https://dfir.ch/posts/today_i_learned_lkm_kernel.modules_disabled/&lt;/a&gt;). It is important to understand that once enabled, modules can be neither loaded or unloaded (&lt;a href=\"https://sysctl-explorer.net/kernel/modules_disabled/\"&gt;https://sysctl-explorer.net/kernel/modules_disabled/&lt;/a&gt;).&lt;/p&gt;\n\n&lt;p&gt;Overall, the configuration of this security feature is saved into the “modules_disabled” variable (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L129\"&gt;https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L129&lt;/a&gt;). Thus, beside checking for the “CAP_SYS_MODULE” capability when trying to unload a kernel module (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L732\"&gt;https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L732&lt;/a&gt;) or when trying to load a kernel module (&lt;a href=\"https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L3047\"&gt;https://elixir.bootlin.com/linux/v6.15.5/source/kernel/module/main.c#L3047&lt;/a&gt;) the “modules_disabled” is also checked.&lt;/p&gt;\n\n&lt;p&gt;Lastly, We can enable\\disable this feature by writing “1” to “/proc/sys/kernel/modules_disabled” (“echo 1 &amp;gt; /proc/sys/kernel/modules_disabled”) or using sysctl (“sysctl kernel.modules_disabled = 1”). In case the feature is enabled when we try to load a kernel module with “insmod” (&lt;a href=\"https://man7.org/linux/man-pages/man8/insmod.8.html\"&gt;https://man7.org/linux/man-pages/man8/insmod.8.html&lt;/a&gt;) the operation will fail (&lt;a href=\"https://linux-audit.com/kernel/increase-kernel-integrity-with-disabled-linux-kernel-modules-loading/\"&gt;https://linux-audit.com/kernel/increase-kernel-integrity-with-disabled-linux-kernel-modules-loading/&lt;/a&gt;) — as shown in the screenshot below. By the way, the same goes when trying to remove a module using for example “rmmod” (&lt;a href=\"https://linux.die.net/man/8/rmmod\"&gt;https://linux.die.net/man/8/rmmod&lt;/a&gt;). Remember we can use “modprobe” for performing both operations (&lt;a href=\"https://linux.die.net/man/8/modprobe\"&gt;https://linux.die.net/man/8/modprobe&lt;/a&gt;).&lt;/p&gt;\n\n&lt;p&gt;&lt;a href=\"https://preview.redd.it/fhy190l27gff1.png?width=758&amp;amp;format=png&amp;amp;auto=webp&amp;amp;s=d67b0dd69c065404c9d551c1543cea51e9dcae7a\"&gt;https://linux-audit.com/kernel/increase-kernel-integrity-with-disabled-linux-kernel-modules-loading/&lt;/a&gt;&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "f5ca482e-5da5-11ec-8c3d-42d730f72f5c", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "", "id": "1margu4", "is_robot_indexable": true, "report_reasons": null, "author": "<PERSON><PERSON><PERSON>", "discussion_type": null, "num_comments": 10, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1margu4/the_linux_security_journey_disable_kernel_modules/", "stickied": false, "url": "https://www.reddit.com/r/linux/comments/1margu4/the_linux_security_journey_disable_kernel_modules/", "subreddit_subscribers": 1732389, "created_utc": 1753635652.0, "num_crossposts": 0, "media": null, "is_video": false}}, {"kind": "t3", "data": {"approved_at_utc": null, "subreddit": "linux", "selftext": "GitHub Repository: https://github.com/KSXGitHub/parallel-disk-usage\n\nImplementation of hardlink detection and visualization: https://github.com/KSXGitHub/parallel-disk-usage/pull/291\n\nThe previous versions of pdu didn't care about whether 2 paths may in fact be the same file, but v0.20.0 now has a flag called `--deduplicate-hardlinks` that will detect the hardlinks and remove duplicated sizes from directory totals. Both paths are still treated as equally real (i.e. both their sizes are the same), but the total will only add one of them. For example, if there is 1GB `foo/a.7z` and `foo/b.7z` being a hardlink to `foo/a.7z`, the ASCII graph will show both `foo/a.7z` and `foo/b.7z` being 1GB each, and `foo` itself also 1GB.", "author_fullname": "t2_o4gyu", "saved": false, "mod_reason_title": null, "gilded": 0, "clicked": false, "title": "parallel-disk-usage (pdu) is a CLI tool that renders disk usage of a directory tree in an ASCII graph. Version 0.20.0 now has the ability to detect and remove hardlink sizes from totals.", "link_flair_richtext": [{"e": "text", "t": "Software Release"}], "subreddit_name_prefixed": "r/linux", "hidden": false, "pwls": 6, "link_flair_css_class": "", "downs": 0, "top_awarded_type": null, "hide_score": false, "name": "t3_1m9v27o", "quarantine": false, "link_flair_text_color": "light", "upvote_ratio": 0.95, "author_flair_background_color": null, "subreddit_type": "public", "ups": 18, "total_awards_received": 0, "media_embed": {}, "author_flair_template_id": null, "is_original_content": false, "user_reports": [], "secure_media": null, "is_reddit_media_domain": true, "is_meta": false, "category": null, "secure_media_embed": {}, "link_flair_text": "Software Release", "can_mod_post": false, "score": 18, "approved_by": null, "is_created_from_ads_ui": false, "author_premium": false, "thumbnail": "", "edited": false, "author_flair_css_class": null, "author_flair_richtext": [], "gildings": {}, "content_categories": null, "is_self": false, "mod_note": null, "created": 1753541022.0, "link_flair_type": "richtext", "wls": 6, "removed_by_category": null, "banned_by": null, "author_flair_type": "text", "domain": "i.redd.it", "allow_live_comments": false, "selftext_html": "&lt;!-- SC_OFF --&gt;&lt;div class=\"md\"&gt;&lt;p&gt;GitHub Repository: &lt;a href=\"https://github.com/KSXGitHub/parallel-disk-usage\"&gt;https://github.com/KSXGitHub/parallel-disk-usage&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;Implementation of hardlink detection and visualization: &lt;a href=\"https://github.com/KSXGitHub/parallel-disk-usage/pull/291\"&gt;https://github.com/KSXGitHub/parallel-disk-usage/pull/291&lt;/a&gt;&lt;/p&gt;\n\n&lt;p&gt;The previous versions of pdu didn&amp;#39;t care about whether 2 paths may in fact be the same file, but v0.20.0 now has a flag called &lt;code&gt;--deduplicate-hardlinks&lt;/code&gt; that will detect the hardlinks and remove duplicated sizes from directory totals. Both paths are still treated as equally real (i.e. both their sizes are the same), but the total will only add one of them. For example, if there is 1GB &lt;code&gt;foo/a.7z&lt;/code&gt; and &lt;code&gt;foo/b.7z&lt;/code&gt; being a hardlink to &lt;code&gt;foo/a.7z&lt;/code&gt;, the ASCII graph will show both &lt;code&gt;foo/a.7z&lt;/code&gt; and &lt;code&gt;foo/b.7z&lt;/code&gt; being 1GB each, and &lt;code&gt;foo&lt;/code&gt; itself also 1GB.&lt;/p&gt;\n&lt;/div&gt;&lt;!-- SC_ON --&gt;", "likes": null, "suggested_sort": null, "banned_at_utc": null, "url_overridden_by_dest": "https://i.redd.it/p8jou7oyb8ff1.png", "view_count": null, "archived": false, "no_follow": false, "is_crosspostable": true, "pinned": false, "over_18": false, "all_awardings": [], "awarders": [], "media_only": false, "link_flair_template_id": "904ea3e4-6748-11e7-b925-0ef3dfbb807a", "can_gild": false, "spoiler": false, "locked": false, "author_flair_text": null, "treatment_tags": [], "visited": false, "removed_by": null, "num_reports": null, "distinguished": null, "subreddit_id": "t5_2qh1a", "author_is_blocked": false, "mod_reason_by": null, "removal_reason": null, "link_flair_background_color": "#349e48", "id": "1m9v27o", "is_robot_indexable": true, "report_reasons": null, "author": "kredditacc96", "discussion_type": null, "num_comments": 5, "send_replies": true, "contest_mode": false, "mod_reports": [], "author_patreon_flair": false, "author_flair_text_color": null, "permalink": "/r/linux/comments/1m9v27o/paralleldiskusage_pdu_is_a_cli_tool_that_renders/", "stickied": false, "url": "https://i.redd.it/p8jou7oyb8ff1.png", "subreddit_subscribers": 1732389, "created_utc": 1753541022.0, "num_crossposts": 0, "media": null, "is_video": false}}], "before": null}}